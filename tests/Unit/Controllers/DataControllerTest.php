<?php

use App\Http\Controllers\Api\DataController;
use App\Models\Logic\Data\Push\Push as PushLogic;


/**
 * @coversDefaultClass \App\Http\Controllers\Api\DataController
 */
class DataControllerTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
    }

    /**
     * @covers ::push
     * Test that the controller has the correct custom rules and messages
     */
    public function testPushCustomValidationRulesAndMessages()
    {
        $controller = new DataController();

        // Test that custom rules are properly defined
        $this->assertArrayHasKey('push', $controller->customRules);
        $this->assertArrayHasKey('data', $controller->customRules['push']);
        $this->assertEquals('required|array', $controller->customRules['push']['data']);

        // Test that custom messages are properly defined
        $this->assertArrayHasKey('push', $controller->customMessages);
        $this->assertArrayHasKey('data.required', $controller->customMessages['push']);
        $this->assertArrayHasKey('data.array', $controller->customMessages['push']);
        $this->assertEquals(4120032, $controller->customMessages['push']['data.required']);
        $this->assertEquals(4120033, $controller->customMessages['push']['data.array']);
    }

    /**
     * Test that PushLogic has the expected function mapping
     */
    public function testPushLogicFuncMapping()
    {
        $mapping = PushLogic::getFuncMapping();

        // Test that mapping is an array and contains expected keys
        $this->assertIsArray($mapping);
        $this->assertArrayHasKey('PAY_LOG', $mapping);
        $this->assertArrayHasKey('OIL_STATION_DATA', $mapping);
        $this->assertArrayHasKey('ACCOUNT_CHANGE', $mapping);
        $this->assertArrayHasKey('REFUND_ORDER', $mapping);

        // Test that values are class names
        $this->assertIsString($mapping['PAY_LOG']);
        $this->assertIsString($mapping['OIL_STATION_DATA']);
    }

    /**
     * Test that the controller constructor works properly
     */
    public function testControllerConstruction()
    {
        $controller = new DataController();
        $this->assertInstanceOf(DataController::class, $controller);

        // Test that parent constructor is called
        $this->assertIsArray($controller->customRules);
        $this->assertIsArray($controller->customMessages);
    }

    /**
     * Test validation rules for different endpoints
     */
    public function testValidationRulesForDifferentEndpoints()
    {
        $controller = new DataController();

        // Test push validation rules
        $this->assertArrayHasKey('push', $controller->customRules);

        // Test other endpoint rules exist
        $this->assertArrayHasKey('getStationWhiteList', $controller->customRules);
        $this->assertArrayHasKey('receiveStationBlackAndWhiteList', $controller->customRules);
        $this->assertArrayHasKey('receiveStationPushRule', $controller->customRules);
    }

    /**
     * Test validation messages for different endpoints
     */
    public function testValidationMessagesForDifferentEndpoints()
    {
        $controller = new DataController();

        // Test push validation messages
        $this->assertArrayHasKey('push', $controller->customMessages);
        $this->assertEquals(4120032, $controller->customMessages['push']['data.required']);
        $this->assertEquals(4120033, $controller->customMessages['push']['data.array']);

        // Test other endpoint messages exist
        $this->assertArrayHasKey('getStationWhiteList', $controller->customMessages);
        $this->assertArrayHasKey('receiveStationPushRule', $controller->customMessages);
    }

    /**
     * Test that all expected message types are defined in PushLogic mapping
     */
    public function testAllExpectedMessageTypesExist()
    {
        $mapping = PushLogic::getFuncMapping();

        $expectedTypes = [
            'PAY_LOG',
            'OIL_STATION_DATA',
            'REFUND_ORDER',
            'ONLINE_PAY_ORDER',
            'PAY_SUCCESS',
            'AUTONOMOUS_ORDER',
            'VERIFICATION_RESULT',
            'REFUND',
            'REFUND_APPLY_AUDIT',
            'ORDER_STATUS_SYNC',
            'TRADE_RESULT',
            'TRADE_REFUND',
            'ACCOUNT_CHANGE'
        ];

        foreach ($expectedTypes as $type) {
            $this->assertArrayHasKey($type, $mapping, "Message type $type should exist in mapping");
            $this->assertIsString($mapping[$type], "Message type $type should map to a class name");
        }
    }

    /**
     * Test that the controller inherits from BasicController
     */
    public function testControllerInheritance()
    {
        $controller = new DataController();
        $this->assertInstanceOf(\App\Http\Controllers\Api\BasicController::class, $controller);
    }
}
