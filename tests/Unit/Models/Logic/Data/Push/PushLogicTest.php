<?php

namespace Tests\Unit\Models\Logic\Data\Push;

use App\Models\Logic\Data\Push\Push as PushLogic;
use App\Models\Logic\Data\Push\OilStationData;
use App\Models\Logic\Data\Push\PayLog;
use App\Models\Logic\Data\Push\AccountChange;
use Illuminate\Http\JsonResponse;
use Mockery;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

/**
 * @coversDefaultClass \App\Models\Logic\Data\Push\Push
 */
class PushLogicTest extends TestCase
{
    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * @covers ::getFuncMapping
     */
    public function testGetFuncMapping()
    {
        $mapping = PushLogic::getFuncMapping();
        
        $expectedMapping = [
            'PAY_LOG' => PayLog::class,
            'OIL_STATION_DATA' => OilStationData::class,
            'REFUND_ORDER' => 'App\Models\Logic\Data\Push\Refund',
            'ONLINE_PAY_ORDER' => 'App\Models\Logic\Data\Push\OnlinePayOrder',
            'PAY_SUCCESS' => 'App\Models\Logic\Data\Push\PaySuccess',
            'AUTONOMOUS_ORDER' => 'App\Models\Logic\Data\Push\AutonomousOrder',
            'VERIFICATION_RESULT' => 'App\Models\Logic\Data\Push\VerificationResult',
            'REFUND' => 'App\Models\Logic\Data\Push\Refund',
            'REFUND_APPLY_AUDIT' => 'App\Models\Logic\Data\Push\RefundApplyAudit',
            'ORDER_STATUS_SYNC' => 'App\Models\Logic\Data\Push\OrderStatusSync',
            'TRADE_RESULT' => 'App\Models\Logic\Data\Push\TradeResult',
            'TRADE_REFUND' => 'App\Models\Logic\Data\Push\TradeRefund',
            'ACCOUNT_CHANGE' => AccountChange::class,
        ];

        $this->assertIsArray($mapping);
        $this->assertArrayHasKey('PAY_LOG', $mapping);
        $this->assertArrayHasKey('OIL_STATION_DATA', $mapping);
        $this->assertArrayHasKey('ACCOUNT_CHANGE', $mapping);
    }

    /**
     * @covers ::handle
     */
    public function testHandleWithOilStationData()
    {
        $data = [
            'message_type' => 'OIL_STATION_DATA',
            'data' => [
                'station_id' => '12345',
                'oil_data' => ['price' => 6.50]
            ]
        ];

        // Mock the OilStationData class
        $mockOilStationData = Mockery::mock(OilStationData::class);
        $mockOilStationData->shouldReceive('handle')->once()->andReturn(null);

        // Mock the class instantiation
        Mockery::mock('overload:' . OilStationData::class, [
            'handle' => null
        ]);

        $pushLogic = new PushLogic($data);
        $result = $pushLogic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::handle
     */
    public function testHandleWithPayLog()
    {
        $data = [
            'message_type' => 'PAY_LOG',
            'data' => [
                'order_id' => '67890',
                'amount' => 100.00,
                'status' => 'success'
            ]
        ];

        // Mock the PayLog class
        $mockPayLog = Mockery::mock(PayLog::class);
        $mockPayLog->shouldReceive('handle')->once()->andReturn(null);

        Mockery::mock('overload:' . PayLog::class, [
            'handle' => null
        ]);

        $pushLogic = new PushLogic($data);
        $result = $pushLogic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::handle
     */
    public function testHandleWithAccountChange()
    {
        $data = [
            'message_type' => 'ACCOUNT_CHANGE',
            'data' => [
                'org_code' => 'TEST123',
                'change_type' => 1,
                'cash_amount' => 100.50,
                'fanli_amount' => 10.00,
                'cash_balance' => 500.00,
                'fanli_balance' => 50.00,
                'created_at' => **********
            ]
        ];

        // Mock the AccountChange class
        $mockAccountChange = Mockery::mock(AccountChange::class);
        $mockAccountChange->shouldReceive('handle')->once()->andReturn(null);

        Mockery::mock('overload:' . AccountChange::class, [
            'handle' => null
        ]);

        $pushLogic = new PushLogic($data);
        $result = $pushLogic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::handle
     */
    public function testHandleWithResponseObject()
    {
        $data = [
            'message_type' => 'OIL_STATION_DATA',
            'data' => ['station_id' => '12345']
        ];

        // Mock to return a Response object instead of null
        $mockResponse = Mockery::mock(Response::class);
        
        $mockOilStationData = Mockery::mock(OilStationData::class);
        $mockOilStationData->shouldReceive('handle')->once()->andReturn($mockResponse);

        Mockery::mock('overload:' . OilStationData::class, [
            'handle' => $mockResponse
        ]);

        $pushLogic = new PushLogic($data);
        $result = $pushLogic->handle();

        $this->assertInstanceOf(Response::class, $result);
    }

    /**
     * @covers ::handle
     */
    public function testHandleWithException()
    {
        $data = [
            'message_type' => 'OIL_STATION_DATA',
            'data' => ['station_id' => '12345']
        ];

        // Mock to throw an exception
        $mockOilStationData = Mockery::mock(OilStationData::class);
        $mockOilStationData->shouldReceive('handle')->once()->andThrow(new \Exception('External API error'));

        Mockery::mock('overload:' . OilStationData::class, [
            'handle' => function() { throw new \Exception('External API error'); }
        ]);

        $pushLogic = new PushLogic($data);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('External API error');

        $pushLogic->handle();
    }

    /**
     * Test with all valid message types to ensure mapping works
     */
    public function testHandleWithAllValidMessageTypes()
    {
        $messageTypes = [
            'PAY_LOG',
            'OIL_STATION_DATA',
            'REFUND_ORDER',
            'ONLINE_PAY_ORDER',
            'PAY_SUCCESS',
            'AUTONOMOUS_ORDER',
            'VERIFICATION_RESULT',
            'REFUND',
            'REFUND_APPLY_AUDIT',
            'ORDER_STATUS_SYNC',
            'TRADE_RESULT',
            'TRADE_REFUND',
            'ACCOUNT_CHANGE'
        ];

        foreach ($messageTypes as $messageType) {
            $data = [
                'message_type' => $messageType,
                'data' => ['test' => 'data']
            ];

            // Get the class name from mapping
            $mapping = PushLogic::getFuncMapping();
            $className = $mapping[$messageType];

            // Mock the specific class
            Mockery::mock('overload:' . $className, [
                'handle' => null
            ]);

            $pushLogic = new PushLogic($data);
            $result = $pushLogic->handle();

            $this->assertInstanceOf(JsonResponse::class, $result);
        }
    }

    /**
     * Test constructor with different data structures
     */
    public function testConstructorWithVariousDataStructures()
    {
        // Test with minimal data
        $data1 = ['message_type' => 'OIL_STATION_DATA'];
        $pushLogic1 = new PushLogic($data1);
        $this->assertInstanceOf(PushLogic::class, $pushLogic1);

        // Test with complex nested data
        $data2 = [
            'message_type' => 'PAY_LOG',
            'data' => [
                'order' => [
                    'id' => '12345',
                    'items' => [
                        ['name' => 'fuel', 'price' => 6.50],
                        ['name' => 'service', 'price' => 2.00]
                    ]
                ],
                'payment' => [
                    'method' => 'card',
                    'amount' => 8.50
                ]
            ],
            'metadata' => [
                'timestamp' => time(),
                'source' => 'mobile_app'
            ]
        ];
        $pushLogic2 = new PushLogic($data2);
        $this->assertInstanceOf(PushLogic::class, $pushLogic2);

        // Test with empty data array
        $data3 = [
            'message_type' => 'ACCOUNT_CHANGE',
            'data' => []
        ];
        $pushLogic3 = new PushLogic($data3);
        $this->assertInstanceOf(PushLogic::class, $pushLogic3);
    }
}
