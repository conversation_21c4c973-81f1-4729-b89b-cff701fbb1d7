<?php

namespace Unit\Common;

use App\Models\Data\OilMapping\Common;
use PHPUnit\Framework\TestCase;

/**
 * @coversDefaultClass \App\Models\Data\OilMapping\Common
 */
class CommonTest extends TestCase
{

    /**
     * @covers ::filterCommonField
     */
    public function testFilterCommonField()
    {
        $oilStationData = [
            'print_snk'         => 'test',
            'print_key'         => 'key',
            'card_classify'     => 'classify',
            'app_station_id'    => 'id',
            'init_provice_code' => '110',
            'init_city_code'    => '1101',
            'header_info'       => 'info',
            'time'              => 'now',
            'isstop'            => '0',
            'push_target_code'  => 'code',
            'push_target'       => 'target',
            'task_id'           => '789',
            'a'                 => 1,
        ];

        $expected = [
            'a' => 1,
        ];

        Common::filterCommonField($oilStationData);
        $this->assertEquals($expected, $oilStationData);
    }

    /**
     * @covers ::comparePriceAndDealRepeatOil
     */
    public function testComparePriceAndDealRepeatOil()
    {
        $oilData = [
            [
                'oil_name_val' => '柴油',
                'price'        => '2.00',
                'oil_name'     => 'diesel',
                'oil_type'     => '0',
                'oil_type_val' => '0',
                'oil_level'    => 'regular'
            ],
            [
                'oil_name_val' => '柴油',
                'price'        => '1.50',
                'oil_name'     => 'diesel',
                'oil_type'     => '0',
                'oil_type_val' => '0',
                'oil_level'    => 'premium'
            ],
            [
                'oil_name_val' => '汽油',
                'price'        => '1.00',
                'oil_name'     => 'gasoline',
                'oil_type'     => '92',
                'oil_type_val' => '92',
                'oil_level'    => 'regular'
            ]
        ];
        $expected = [
            [
                'oil_name_val' => '汽油',
                'price'        => '1.00',
                'oil_name'     => 'gasoline',
                'oil_type'     => '92',
                'oil_type_val' => '92',
                'oil_level'    => 'regular'
            ]
        ];
        $result = Common::comparePriceAndDealRepeatOil($oilData);
        $this->assertEquals($expected, $result);
        $oilData = [
            [
                'oil_name_val' => '柴油',
                'price'        => '2.00',
                'oil_name'     => 'diesel',
                'oil_type'     => '0',
                'oil_type_val' => '0',
                'oil_level'    => 'regular'
            ],
            [
                'oil_name_val' => '车用柴油',
                'price'        => '1.50',
                'oil_name'     => 'diesel',
                'oil_type'     => '0',
                'oil_type_val' => '0',
                'oil_level'    => 'premium'
            ],
            [
                'oil_name_val' => '汽油',
                'price'        => '1.00',
                'oil_name'     => 'gasoline',
                'oil_type'     => '92',
                'oil_type_val' => '92',
                'oil_level'    => 'regular'
            ]
        ];
        $expected = [
            [
                'oil_name_val' => '车用柴油',
                'price'        => '1.50',
                'oil_name'     => 'diesel',
                'oil_type'     => '0',
                'oil_type_val' => '0',
                'oil_level'    => 'premium'
            ],
            [
                'oil_name_val' => '汽油',
                'price'        => '1.00',
                'oil_name'     => 'gasoline',
                'oil_type'     => '92',
                'oil_type_val' => '92',
                'oil_level'    => 'regular'
            ]
        ];
        $result = Common::comparePriceAndDealRepeatOil($oilData);
        $this->assertEquals($expected, $result);
        $oilData = [
            [
                'oil_name_val' => '车用柴油',
                'price'        => '2.00',
                'oil_name'     => 'diesel',
                'oil_type'     => '0',
                'oil_type_val' => '0',
                'oil_level'    => 'regular'
            ],
            [
                'oil_name_val' => '柴油',
                'price'        => '1.50',
                'oil_name'     => 'diesel',
                'oil_type'     => '0',
                'oil_type_val' => '0',
                'oil_level'    => 'premium'
            ],
            [
                'oil_name_val' => '汽油',
                'price'        => '1.00',
                'oil_name'     => 'gasoline',
                'oil_type'     => '92',
                'oil_type_val' => '92',
                'oil_level'    => 'regular'
            ]
        ];
        $expected = [
            [
                'oil_name_val' => '柴油',
                'price'        => '1.50',
                'oil_name'     => 'diesel',
                'oil_type'     => '0',
                'oil_type_val' => '0',
                'oil_level'    => 'premium'
            ],
            [
                'oil_name_val' => '汽油',
                'price'        => '1.00',
                'oil_name'     => 'gasoline',
                'oil_type'     => '92',
                'oil_type_val' => '92',
                'oil_level'    => 'regular'
            ]
        ];
        $result = Common::comparePriceAndDealRepeatOil($oilData);
        $this->assertEquals($expected, $result);
        $oilData = [
            [
                'oil_name_val' => '车用柴油',
                'price'        => '2.00',
                'oil_name'     => 'diesel',
                'oil_type'     => '0',
                'oil_type_val' => '0',
                'oil_level'    => 'regular'
            ],
            [
                'oil_name_val' => '柴油',
                'price'        => '1.50',
                'oil_name'     => 'diesel',
                'oil_type'     => '0',
                'oil_type_val' => '0',
                'oil_level'    => 'premium'
            ],
            [
                'oil_name_val' => '柴油',
                'price'        => '1.50',
                'oil_name'     => 'diesel',
                'oil_type'     => '10',
                'oil_type_val' => '10',
                'oil_level'    => 'premium'
            ],
            [
                'oil_name_val' => '汽油',
                'price'        => '1.00',
                'oil_name'     => 'gasoline',
                'oil_type'     => '92',
                'oil_type_val' => '92',
                'oil_level'    => 'regular'
            ]
        ];
        $expected = [
            [
                'oil_name_val' => '柴油',
                'price'        => '1.50',
                'oil_name'     => 'diesel',
                'oil_type'     => '0',
                'oil_type_val' => '0',
                'oil_level'    => 'premium'
            ],
            [
                'oil_name_val' => '柴油',
                'price'        => '1.50',
                'oil_name'     => 'diesel',
                'oil_type'     => '10',
                'oil_type_val' => '10',
                'oil_level'    => 'premium'
            ],
            [
                'oil_name_val' => '汽油',
                'price'        => '1.00',
                'oil_name'     => 'gasoline',
                'oil_type'     => '92',
                'oil_type_val' => '92',
                'oil_level'    => 'regular'
            ]
        ];
        $result = Common::comparePriceAndDealRepeatOil($oilData);
        $this->assertEquals($expected, $result);
        $oilData = [
            [
                'oil_name_val' => '车用柴油',
                'price'        => '2.00',
                'oil_name'     => 'diesel',
                'oil_type'     => '0',
                'oil_type_val' => '0',
                'oil_level'    => 'regular'
            ],
            [
                'oil_name_val' => '柴油',
                'price'        => '1.50',
                'oil_name'     => 'diesel',
                'oil_type'     => '0',
                'oil_type_val' => '0',
                'oil_level'    => 'premium'
            ],
            [
                'oil_name_val' => '车用柴油',
                'price'        => '1.00',
                'oil_name'     => 'diesel',
                'oil_type'     => '10',
                'oil_type_val' => '10',
                'oil_level'    => 'premium'
            ],
            [
                'oil_name_val' => '柴油',
                'price'        => '1.50',
                'oil_name'     => 'diesel1',
                'oil_type'     => '10',
                'oil_type_val' => '10',
                'oil_level'    => 'premium'
            ],
            [
                'oil_name_val' => '汽油',
                'price'        => '1.00',
                'oil_name'     => 'gasoline',
                'oil_type'     => '92',
                'oil_type_val' => '92',
                'oil_level'    => 'regular'
            ]
        ];
        $expected = [
            [
                'oil_name_val' => '柴油',
                'price'        => '1.50',
                'oil_name'     => 'diesel',
                'oil_type'     => '0',
                'oil_type_val' => '0',
                'oil_level'    => 'premium'
            ],
            [
                'oil_name_val' => '车用柴油',
                'price'        => '1.00',
                'oil_name'     => 'diesel',
                'oil_type'     => '10',
                'oil_type_val' => '10',
                'oil_level'    => 'premium',
            ],
            [
                'oil_name_val' => '汽油',
                'price'        => '1.00',
                'oil_name'     => 'gasoline',
                'oil_type'     => '92',
                'oil_type_val' => '92',
                'oil_level'    => 'regular'
            ]
        ];
        $result = Common::comparePriceAndDealRepeatOil($oilData);
        $this->assertEquals($expected, $result);
    }
}