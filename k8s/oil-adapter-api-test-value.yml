replicaCount: 1
env:
  - name: SIDECAR__PORT
    value: '80'
  - name: SIDECAR__LISTEN_PORT
    value: '30081'
  - name: aliyun_logs_k8s-petroleum-card-logs-topic
    value: stdout
  - name: aliyun_logs_k8s-petroleum-card-logs-topic_tags
    value: env=test,product=petroleum-card,appid=oil-adapter-web
image:
  repository: registry.cn-beijing.aliyuncs.com/php-spec/oil-adapter:c992ec0b.2
nameOverride: "oil-adapter-web"
fullnameOverride: "oil-adapter-web"
configmap:
  envdata:
    PHPENV: "prod"
    APPLICATION__NAME: "oil-adapter"
    APPLICATION__DEBUG: "true"
    DB__CONNECTION: "mysql"
    DB__HOST: "*************"
    DB__PORT: "3306"
    DB__DATABASE: "oil_adapter"
    DB__USERNAME: "root"
    DB__PASSWORD: "cc509a6f75849b"
    APP__NAME: "oil-adapter"
    APP__URL: "//adapter.test.chinawayltd.com"
    CACHE__DRIVER: "redis"
    QUEUE_CONNECTION: "redis"
    QUEUE__DRIVER: "redis"
    REDIS__HOST: "*************"
    REDIS__PASSWORD: "123456"
    REDIS__PORT: "6382"
    REDIS__PREFIX: "adapter_"
    CRONTAB__ENABLE: "off"
    QUEUE__ENABLE: "off"
    SUPERVISOR__ENABLE: "on"
    RUN__ENVIRONMENT: "test"
    RUN__MODE: "remote"
    RUN_SCRIPTS: "1"
    WEBROOT: "/data/web/public"
    PHP_ERRORS_STDERR: "true"
    SKIP_COMPOSER: "on"
    MAIL__DRIVER: "smtp"
    MAIL__HOST: "smtp.feishu.cn"
    MAIL__PORT: "587"
    MAIL__USERNAME: "<EMAIL>"
    MAIL__PASSWORD: "wjS8tLhvQRt2loXn"
    MAIL__ENCRYPTION: "tls"
    MAIL__FROM__NAME: "油品对接"
    MAIL__FROM__ADDRESS: "<EMAIL>"
resources:
  limits:
    cpu: 100m
    memory: 2048Mi
  requests:
    cpu: 50m
    memory: 500Mi
ports:
  - name: http
    containerPort: 80
    protocol: TCP
ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: ingress-inner
    inginx.ingress.kubernetes.io/ssl-redirect: "true"
  hosts:
    - host: adapter.test.chinawayltd.com
      paths:
        - /
  tls:
    - secretName: test-chinawayltd-com
      hosts:
        - adapter.test.chinawayltd.com
healthProbes:
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /healthCheck
      port: 80
      scheme: HTTP
    initialDelaySeconds: 120
    periodSeconds: 2
    successThreshold: 2
    timeoutSeconds: 2
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /healthCheck
      port: 80
      scheme: HTTP
    initialDelaySeconds: 120
    periodSeconds: 2
    successThreshold: 1
    timeoutSeconds: 2

