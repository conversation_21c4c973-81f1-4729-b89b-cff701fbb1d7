<?php

/*
|--------------------------------------------------------------------------
| Application Routes
|--------------------------------------------------------------------------
|
| Here is where you can register all of the routes for an application.
| It is a breeze. Simply tell Lumen the URIs it should respond to
| and give it the Closure to call when that URI is requested.
|
*/

/**
 * admin路由组
 */

/** @var Router $router */

use Laravel\Lumen\Routing\Router;

$router->group(['prefix' => 'admin', 'middleware' => Illuminate\Session\Middleware\StartSession::class],
    function () use ($router) {
        $router->group(['prefix' => 'login'], function () use ($router) {
            $router->get('/index', 'Web\LoginController@index');
            $router->post('/in', 'Web\LoginController@in');
            $router->get('/out', 'Web\LoginController@out');
            $router->get('/getCaptcha', 'Web\LoginController@getCaptcha');
            $router->get('/getLoginUserInfo', 'Web\LoginController@getLoginUserInfo');
        });

        $router->group(['prefix' => 'config'], function () use ($router) {
            $router->get('/index', 'Web\ConfigController@index');
            $router->post('/getData', 'Web\ConfigController@getData');
            $router->post('/update', 'Web\ConfigController@update');
            $router->post('/create', 'Web\ConfigController@create');
            $router->post('/delete', 'Web\ConfigController@delete');
        });

        $router->group(['prefix' => 'role'], function () use ($router) {
            $router->post('/getData', 'Web\RoleController@getData');
            $router->post('/getSelect', 'Web\RoleController@getSelect');
            $router->post('/update', 'Web\RoleController@update');
            $router->post('/create', 'Web\RoleController@create');
            $router->post('/delete', 'Web\RoleController@delete');
        });

        $router->group(['prefix' => 'role_permission'], function () use ($router) {
            $router->post('/getData', 'Web\RolePermissionController@getData');
            $router->post('/update', 'Web\RolePermissionController@update');
            $router->post('/create', 'Web\RolePermissionController@create');
            $router->post('/delete', 'Web\RolePermissionController@delete');
        });

        $router->group(['prefix' => 'permission'], function () use ($router) {
            $router->post('/getData', 'Web\PermissionController@getData');
            $router->post('/update', 'Web\PermissionController@update');
            $router->post('/create', 'Web\PermissionController@create');
            $router->post('/delete', 'Web\PermissionController@delete');
            $router->post('/getSelect', 'Web\PermissionController@getSelect');
            $router->post('/getTree', 'Web\PermissionController@getTree');
        });

        $router->group(['prefix' => 'station_price'], function () use ($router) {
            $router->get('/index', 'Web\StationPriceController@index');
            $router->post('/getData', 'Web\StationPriceController@getData');
            $router->post('/stationPull', 'Web\StationPriceController@stationPull');
            $router->post('/getSelect', 'Web\StationPriceController@getSelect');
        });

        $router->group(['prefix' => 'user'], function () use ($router) {
            $router->get('/index', 'Web\UserController@index');
            $router->post('/getData', 'Web\UserController@getData');
            $router->post('/update', 'Web\UserController@update');
            $router->post('/delete', 'Web\UserController@delete');
            $router->post('/create', 'Web\UserController@create');
            $router->post('/updateSecret', 'Web\UserController@updateSecret');
        });

        $router->group(['prefix' => 'supplier'], function () use ($router) {
            $router->get('/index', 'Web\SupplierController@index');
            $router->post('/getData', 'Web\SupplierController@getData');
            $router->post('/getAllData', 'Web\SupplierController@getAllData');
            $router->post('/update', 'Web\SupplierController@update');
            $router->post('/create', 'Web\SupplierController@create');
            $router->post('/delete', 'Web\SupplierController@delete');
            $router->post('/getSupplierNameBySupplierCodes', 'Web\SupplierController@getSupplierNameBySupplierCodes');
        });

        $router->group(['prefix' => 'docking_platform'], function () use ($router) {
            $router->get('/index', 'Web\DockingPlatformController@index');
            $router->post('/getData', 'Web\DockingPlatformController@getData');
            $router->post('/update', 'Web\DockingPlatformController@update');
            $router->post('/create', 'Web\DockingPlatformController@create');
            $router->post('/delete', 'Web\DockingPlatformController@delete');
        });

        $router->group(['prefix' => 'station_push_condition'], function () use ($router) {
            $router->get('/index', 'Web\StationPushConditionController@index');
            $router->post('/getData', 'Web\StationPushConditionController@getData');
            $router->post('/update', 'Web\StationPushConditionController@update');
            $router->post('/create', 'Web\StationPushConditionController@create');
            $router->post('/delete', 'Web\StationPushConditionController@delete');
        });

        $router->group(['prefix' => 'station_push_switch'], function () use ($router) {
            $router->get('/index', 'Web\StationPushSwitchController@index');
            $router->post('/getData', 'Web\StationPushSwitchController@getData');
            $router->post('/stationPushAll', 'Web\StationPushSwitchController@stationPushAll');
            $router->post('/update', 'Web\StationPushSwitchController@update');
            $router->post('/create', 'Web\StationPushSwitchController@create');
            $router->post('/delete', 'Web\StationPushSwitchController@delete');
            $router->post('/getPushedStationList', 'Web\StationPushSwitchController@getPushedStationList');
        });

        $router->group(['prefix' => 'common'], function () use ($router) {
            $router->get('/getThirdPartyPlatformList', 'Web\CommonController@getThirdPartyPlatformList');
            $router->get('/getLogLevelList', 'Web\CommonController@getLogLevelList');
            $router->get('/getQueueLogTypeList', 'Web\CommonController@getQueueLogTypeList');
            $router->get('/getCurrentDayLogCountByType', 'Web\CommonController@getCurrentDayLogCountByType');
            $router->get('/getStationSourceList', 'Web\CommonController@getStationSourceList');
            $router->get('/getTradeTypeList', 'Web\CommonController@getTradeTypeList');
            $router->get('/getOilUnitList', 'Web\CommonController@getOilUnitList');
            $router->get('/getOrderRefundReason', 'Web\CommonController@getOrderRefundReason');
            $router->get('/getGmsOrderIsRefund', 'Web\CommonController@getGmsOrderIsRefund');
        });

        $router->group(['prefix' => 'home'], function () use ($router) {
            $router->get('/index', 'Web\HomeController@index');
        });

        $router->group(['prefix' => 'request_log'], function () use ($router) {
            $router->get('/index', 'Web\RequestLogController@index');
            $router->post('/getData', 'Web\RequestLogController@getData');
            $router->get('/getCurrentDayCount', 'Web\RequestLogController@getCurDayCount');
        });

        $router->group(['prefix' => 'receive_log'], function () use ($router) {
            $router->get('/index', 'Web\ReceiveLogController@index');
            $router->post('/getData', 'Web\ReceiveLogController@getData');
            $router->get('/getCurrentDayCount', 'Web\ReceiveLogController@getCurDayCount');
        });

        $router->group(['prefix' => 'queue_log'], function () use ($router) {
            $router->get('/index', 'Web\QueueLogController@index');
            $router->post('/getData', 'Web\QueueLogController@getData');
            $router->get('/getCurrentDayCount', 'Web\QueueLogController@getCurDayCount');
        });

        $router->group(['prefix' => 'failed_jobs'], function () use ($router) {
            $router->get('/index', 'Web\FailedJobsController@index');
            $router->post('/getData', 'Web\FailedJobsController@getData');
        });

        $router->group(['prefix' => 'response_log'], function () use ($router) {
            $router->get('/index', 'Web\ResponseLogController@index');
            $router->post('/getData', 'Web\ResponseLogController@getData');
            $router->get('/getCurrentDayCount', 'Web\ResponseLogController@getCurDayCount');
        });

        $router->group(['prefix' => 'order_assoc'], function () use ($router) {
            $router->get('/index', 'Web\OrderAssocController@index');
            $router->post('/getData', 'Web\OrderAssocController@getData');
            $router->post('/getChart', 'Web\OrderAssocController@getChart');
        });

        $router->group(['prefix' => 'order'], function () use ($router) {
            $router->get('/refund/index', 'Web\OrderController@refund');
            $router->post('/refund/do', 'Web\OrderController@doRefund');
            $router->post('/refund/getData', 'Web\OrderController@getRefundList');
            $router->post('/retryFailedOrder', 'Web\OrderController@retryFailedOrder');
            $router->post('/getSecondaryPaymentQrCode', 'Web\OrderController@getSecondaryPaymentQrCode');
        });

        $router->group(['prefix' => 'tool'], function () use ($router) {
            $router->post('/queryDriverInfo', 'Web\ToolController@queryDriverInfo');
            $router->post('/queryBillCheckResult', 'Web\ToolController@queryBillCheckResult');
            $router->post('/pushBill', 'Web\ToolController@pushBill');
        });

        $router->group(['prefix' => 'station_push_record'], function () use ($router) {
            $router->post('/getData', 'Web\StationPushRecordController@getData');
        });
    });