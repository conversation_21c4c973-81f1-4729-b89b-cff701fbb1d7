<?php

use App\Http\Middleware\ApiAuthenticate;
use <PERSON><PERSON>\Lumen\Routing\Router;

/*
|--------------------------------------------------------------------------
| Application Routes
|--------------------------------------------------------------------------
|
| Here is where you can register all of the routes for an application.
| It is a breeze. Simply tell Lumen the URIs it should respond to
| and give it the Closure to call when that URI is requested.
|
*/

/**
 * api 路由
 */

/** @var Router $router */
$router->group(['prefix' => 'trade', 'middleware' => ApiAuthenticate::class], function () use ($router) {
    $router->post('/pay', 'Api\TradeController@pay');
    $router->post('/reToBePaidCallback', 'Api\TradeController@reToBePaidCallback');
    $router->post('/payBySn', 'Api\TradeController@payBySn');
    $router->post('/query', 'Api\TradeController@query');
    $router->post('/refund', 'Api\TradeController@refund');
    $router->post('/oil', 'Api\TradeController@oil');
});

$router->group(['prefix' => 'common', 'middleware' => ApiAuthenticate::class], function () use ($router) {
    $router->post('/test', 'Api\CommonController@test');
    $router->post('/uuid', 'Api\CommonController@uuid');
    $router->post('/sign', 'Api\CommonController@sign');
    $router->post('/gasTest', 'Api\CommonController@gasTest');
    $router->post('/imitateWrittenOff', 'Api\CommonController@imitateWrittenOff');
});

$router->group(['prefix' => 'data', 'middleware' => ApiAuthenticate::class], function () use ($router) {
    $router->post('/push', 'Api\DataController@push');
    $router->post('/getStationWhiteList', 'Api\DataController@getStationWhiteList');
    $router->post('/getAvailableStationListForCustomer', 'Api\DataController@getAvailableStationListForCustomer');
    $router->post(
        '/getStopStationAndAvailableStationForCustomerConfig',
        'Api\DataController@getStopStationAndAvailableStationForCustomerConfig'
    );
    $router->post('/receiveStationBlackAndWhiteList', 'Api\DataController@receiveStationBlackAndWhiteList');
    $router->post('/receiveStationPushRule', 'Api\DataController@receiveStationPushRule');
});

$router->group(['prefix' => 'sign', 'middleware' => ApiAuthenticate::class], function () use ($router) {
    $router->post('/make', 'Api\SignController@make');
});

$router->group(['prefix' => 'code', 'middleware' => ApiAuthenticate::class], function () use ($router) {
    $router->post('/parse', 'Api\CodeController@parse');
    $router->post('/getSecondaryPaymentQrCode', 'Api\CodeController@getSecondaryPaymentQrCode');
});

$router->group(['prefix' => 'oil', 'middleware' => ApiAuthenticate::class], function () use ($router) {
    $router->post('/query', 'Api\OilController@query');
});

$router->group(['prefix' => 'account', 'middleware' => ApiAuthenticate::class], function () use ($router) {
    $router->post('/balance', 'Api\AccountController@query');
    $router->post('/query', 'Api\AccountController@query');
    $router->post('/change', 'Api\AccountController@change');
    $router->post('/changeRecord', 'Api\AccountController@changeRecord');
});

$router->group(['prefix' => 'bill', 'middleware' => ApiAuthenticate::class], function () use ($router) {
    $router->post('/query', 'Api\BillController@query');
    $router->post('/push', 'Api\BillController@push');
});

$router->group(['prefix' => 'station', 'middleware' => ApiAuthenticate::class], function () use ($router) {
    $router->post('/query', 'Api\StationController@queryOne');
    $router->post('/queryAll', 'Api\StationController@queryAll');
    $router->post('/receive', 'Api\StationController@receive');
});

$router->group(['prefix' => 'oil', 'middleware' => ApiAuthenticate::class], function () use ($router) {
    $router->post('/getGunNosByStationAndOil', 'Api\OilController@getGunNosByStationAndOil');
    $router->post('/getGunNosByStation', 'Api\OilController@getGunNosByStation');
    $router->post('/getGunNosByStationIds', 'Api\OilController@getGunNosByStationIds');
});

$router->group(['prefix' => 'user', 'middleware' => ApiAuthenticate::class], function () use ($router) {
    $router->post('/generate', 'Api\UserController@generate');
    $router->post('/add', 'Api\UserController@add');
});

$router->group(['prefix' => 'order', 'middleware' => ApiAuthenticate::class], function () use ($router) {
    $router->post('/toBePaid', 'Api\OrderController@toBePaid');
    $router->post('/reToBePaid', 'Api\OrderController@reToBePaid');
    $router->post('/cancel', 'Api\OrderController@cancel');
    $router->post('/query', 'Api\OrderController@query');
    $router->post('/refund', 'Api\TradeController@refund');
    $router->post('/getSecondaryPaymentCertificate', 'Api\OrderController@getSecondaryPaymentCertificate');
    $router->post('/refundApplication', 'Api\OrderController@refundApplication');
    $router->post('/refundCustomerForOrderCenter', 'Api\OrderController@refundCustomerForOrderCenter');
    $router->post('/queryStatus', 'Api\OrderController@query');
    $router->post('/split', 'Api\OrderController@split');
});

$router->group(['prefix' => 'zsh', 'middleware' => ApiAuthenticate::class], function () use ($router) {
    $router->post('/couponList', 'Api\ZshController@couponList');
    $router->post('/prePayBalanceQuery', 'Api\ZshController@prePayBalanceQuery');
    $router->post('/couponPurchase', 'Api\ZshController@couponPurchase');
    $router->post('/orderQuery', 'Api\ZshController@orderQuery');
    $router->post('/couponQuery', 'Api\ZshController@couponQuery');
    $router->post('/getPreAmount', 'Api\ZshController@getPreAmount');
});

$router->group(['prefix' => 'coupon', 'middleware' => ApiAuthenticate::class], function () use ($router) {
    $router->post('/getTypes', 'Api\CouponController@proxy');
    $router->post('/distribute', 'Api\CouponController@proxy');
    $router->post('/getVerificationCertificate', 'Api\CouponController@proxy');
    $router->post('/cancel', 'Api\CouponController@proxy');
    $router->post('/getState', 'Api\CouponController@proxy');
    $router->post('/applyDistBill', 'Api\CouponController@proxy');
    $router->post('/getDistBill', 'Api\CouponController@proxy');
    $router->post('/applyUsedBill', 'Api\CouponController@proxy');
    $router->post('/getUsedBill', 'Api\CouponController@proxy');
    $router->post('/getUsableCoupons', 'Api\CouponController@proxy');
    $router->post('/batchGetState', 'Api\CouponController@proxy');
});

