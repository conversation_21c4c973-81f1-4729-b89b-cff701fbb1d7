<?php


namespace Tool;


use BlueM\Tree;
use BlueM\Tree\Serializer\TreeJsonSerializerInterface;

class TreeSerializer implements TreeJsonSerializerInterface
{
    /**
     * @inheritDoc
     */
    public function serialize(Tree $tree)
    {
        $data = $tree->getRootNodes();
        $treeData = [];
        $this->parseTree($data, $treeData);
        return $treeData;
    }

    public function parseTree(array $tree, array &$returnData = [])
    {
        foreach ($tree as $k => $v) {

            $returnData[$k] = $v->toArray();

            if ($v->hasChildren()) {

                if (!isset($returnData[$k]['children'])) {

                    $returnData[$k]['children'] = [];
                }

                $this->parseTree($v->getChildren(), $returnData[$k]['children']);
            }
        }
    }
}
