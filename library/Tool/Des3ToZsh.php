<?php

namespace Tool;

/*
* 3Des加解密类
*/

class Des3ToZsh
{
    private $paddingBlockSize;

    public function __construct()
    {
        $this->paddingBlockSize = @mcrypt_get_block_size(MCRYPT_3DES, MCRYPT_MODE_ECB);
    }

    public function encrypt($input, $key): string
    {
        $input = $this->pkcs7Padding($input);
        //$key = str_pad($key,24,'0');
        $key = $key . substr($key, 0, 8);
        return @mcrypt_encrypt(MCRYPT_3DES, $key, $input, MCRYPT_MODE_ECB);
    }

    public function pkcs7Padding(string $input): string
    {
        $pad = $this->paddingBlockSize - (strlen($input) % $this->paddingBlockSize);

        return $input . str_repeat(chr($pad), $pad);
    }

    public function decrypt($encrypted, $key)
    {
        $encrypted = base64_decode($encrypted);
        //$key = str_pad($key,24,'0');
        $key = $key . substr($key, 0, 8);
        $td = @mcrypt_module_open(MCRYPT_3DES, '', MCRYPT_MODE_ECB, '');
        $iv = @mcrypt_create_iv(mcrypt_enc_get_iv_size($td), MCRYPT_RAND);
        @mcrypt_generic_init($td, $key, $iv);
        $decrypted = @mdecrypt_generic($td, $encrypted);
        @mcrypt_generic_deinit($td);
        @mcrypt_module_close($td);

        return $this->pkcs7UnPadding($decrypted);
    }

    public function pkcs7UnPadding(string $input)
    {
        $pad = ord($input[strlen($input) - 1]);

        if ($pad > strlen($input)) {
            return false;
        }

        if (strspn($input, chr($pad), strlen($input) - $pad) != $pad) {
            return false;
        }

        return substr($input, 0, -1 * $pad);
    }
}
