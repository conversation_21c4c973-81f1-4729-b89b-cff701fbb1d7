<?php


namespace Tool\Alarm;

use App\Models\Data\AuthConfig as AuthConfigData;
use Library\Monitor\Falcon;

class Fei<PERSON>hu extends Protocol
{
    private $accessToken;
    private $accessTokenTypeToConfigNameMapping = [
        'price'                       => 'FEI_SHU_ACCESS_TOKEN_ORDER',
        'station'                     => 'FEI_SHU_ACCESS_TOKEN',
        ''                            => 'FEI_SHU_ACCESS_TOKEN',
        'order'                       => 'FEI_SHU_ACCESS_TOKEN_ORDER',
        'up_stream_order'             => 'FEI_SHU_ACCESS_TOKEN_UP_STREAM_ORDER',
        'down_stream_order'           => 'FEI_SHU_ACCESS_TOKEN_DOWN_STREAM_ORDER',
        'balance'                     => 'FEI_SHU_ACCESS_TOKEN_BALANCE',
        'down_stream_bill'            => 'FEI_SHU_ACCESS_TOKEN_DOWN_STREAM_BILL',
        'simple'                      => 'FEI_SHU_ACCESS_TOKEN_SIMPLE',
        'reservation_order_exception' => 'FEI_SHU_ACCESS_TOKEN_RESERVATION_ORDER_EXCEPTION',
        'push_order_status_failed'    => 'FEI_SHU_ACCESS_TOKEN_PUSH_ORDER_STATUS_FAILED',
        'supplier_refund_alarm'       => 'FEI_SHU_ACCESS_TOKEN_SUPPLIER_REFUND_ALARM',
        'fy_order_alarm'              => 'FEI_SHU_ACCESS_TOKEN_FY_ORDER_ALARM',
    ];

    public function __construct()
    {
        parent::__construct();
    }

    public function saleGeGunForPrice(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['up_stream_order']
        );
        $parameters['title'] = '站点价格异常';
        $parameters['createdAt'] = date("Y-m-d H:i:s");
        Falcon::feishu($this->accessToken, $this->renderTemplate(__FUNCTION__, $parameters));
    }

    /**
     * @param array $parameters ['stationName' => 'x', 'stationId' => 'x', 'platformName' => 'x']
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/9/4 10:08 下午
     */
    public function stationPushFailed(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['station']
        );
        $parameters['title'] = '站点推送失败';
        $parameters['createdAt'] = date("Y-m-d H:i:s");
        global $routeId;
        $parameters['routeId'] = $routeId;
        Falcon::feishu($this->accessToken, $this->renderTemplate(__FUNCTION__, $parameters));
    }

    public function oilConvertFailed(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['station']
        );
        $parameters['title'] = '站点接收/拉取失败(油品转换失败)';
        $parameters['createdAt'] = date("Y-m-d H:i:s");
        global $routeId;
        $parameters['routeId'] = $routeId;
        Falcon::feishu($this->accessToken, $this->renderTemplate(__FUNCTION__, $parameters));
    }

    public function pushOrderFailed(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['down_stream_order']
        );
        Falcon::feishu($this->accessToken, $this->renderTemplate(__FUNCTION__, $parameters));
    }

    public function queryOrderFailed(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['up_stream_order']
        );
        Falcon::feishu($this->accessToken, $this->renderTemplate(__FUNCTION__, $parameters));
    }

    public function needManualRefund(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['up_stream_order']
        );
        Falcon::feishu($this->accessToken, $this->renderTemplate(__FUNCTION__, $parameters));
    }

    public function deductionMainAccountFailed(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['down_stream_order']
        );
        Falcon::feishu($this->accessToken, $this->renderTemplate(__FUNCTION__, $parameters));
    }

    public function getPayQrCodeBySupplierFailed(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName($this->accessTokenTypeToConfigNameMapping['order']);
        Falcon::feishu($this->accessToken, $this->renderTemplate(__FUNCTION__, $parameters));
    }

    public function writtenOffFailed(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['up_stream_order']
        );
        Falcon::feishu($this->accessToken, $this->renderTemplate(__FUNCTION__, $parameters));
    }

    public function getSecondaryCertificateFailed(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['up_stream_order']
        );
        Falcon::feishu($this->accessToken, $this->renderTemplate(__FUNCTION__, $parameters));
    }

    public function stationOilRepeat(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName($this->accessTokenTypeToConfigNameMapping['order']);
        Falcon::feishu($this->accessToken, $this->renderTemplate(__FUNCTION__, $parameters));
    }

    public function tradeDifferent(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['up_stream_order']
        );
        Falcon::feishu($this->accessToken, $this->renderTemplate(__FUNCTION__, $parameters));
    }

    public function priceNotMeetRequirements(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['up_stream_order']
        );
        Falcon::feishu($this->accessToken, $this->renderTemplate(__FUNCTION__, $parameters));
    }

    public function insufficientBalance(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['balance']
        );
        Falcon::feishu($this->accessToken, $this->renderTemplate(__FUNCTION__, $parameters));
    }

    public function dsBillPushFailed(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['down_stream_bill']
        );
        Falcon::feishu($this->accessToken, $this->renderTemplate(__FUNCTION__, $parameters));
    }

    public function gdPermissionInvalid(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['simple']
        );
        Falcon::feishu($this->accessToken, $this->renderTemplate(__FUNCTION__, $parameters));
    }

    public function queueMonitoringAlarm(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['simple']
        );
        Falcon::feishu($this->accessToken, $this->renderTemplate(__FUNCTION__, $parameters));
    }

    public function freeOrderMoney(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['up_stream_order']
        );
        Falcon::feishu($this->accessToken, $this->renderTemplate(__FUNCTION__, $parameters));
    }

    public function pushOrderStatusFailed(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['push_order_status_failed']
        );
        Falcon::feishu($this->accessToken, $this->renderTemplate(__FUNCTION__, $parameters));
    }

    public function reToBePaidApproveResult(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['push_order_status_failed']
        );
        Falcon::feishu($this->accessToken, $this->renderTemplate(__FUNCTION__, $parameters));
    }

    public function reservationOrderException(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['reservation_order_exception']
        );
        Falcon::feishu($this->accessToken, $this->renderTemplate(__FUNCTION__, $parameters));
    }

    public function supplierOrderFailed(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['up_stream_order']
        );
        Falcon::feishu($this->accessToken, $this->renderTemplate(__FUNCTION__, $parameters));
    }

    public function compareSupplierMoneyFailed(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['up_stream_order']
        );
        Falcon::feishu($this->accessToken, $this->renderTemplate(__FUNCTION__, $parameters));
    }

    public function oilAndGunListedPriceNotMatch(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['up_stream_order']
        );
        Falcon::feishu($this->accessToken, $this->renderTemplate(__FUNCTION__, $parameters));
    }

    public function oilAndGunSalePriceNotMatch(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['up_stream_order']
        );
        Falcon::feishu($this->accessToken, $this->renderTemplate(__FUNCTION__, $parameters));
    }

    public function gunListedPriceNoUnique(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['up_stream_order']
        );
        Falcon::feishu($this->accessToken, $this->renderTemplate(__FUNCTION__, $parameters));
    }

    public function gunSalePriceNoUnique(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['up_stream_order']
        );
        Falcon::feishu($this->accessToken, $this->renderTemplate(__FUNCTION__, $parameters));
    }

    public function outServiceInterfaceInvalid(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['simple']
        );
        Falcon::feishu($this->accessToken, $this->renderTemplate(__FUNCTION__, $parameters));
    }

    public function customerRefundCallback(string $accessToken, array $parameters = [])
    {
        Falcon::feishu($accessToken, $this->renderTemplate(__FUNCTION__, $parameters));
    }

    public function supplierRefundAlarm(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['supplier_refund_alarm']
        );
        Falcon::feishu($this->accessToken, $this->renderTemplate(__FUNCTION__, $parameters));
    }

    public function fyOrderUseTimeExceptionAlarm(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['fy_order_alarm']
        );
        Falcon::feishu($this->accessToken, $this->renderTemplate(__FUNCTION__, $parameters));
    }
}
