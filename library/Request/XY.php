<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;
use Faker\Provider\Uuid;


class XY extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param bool $needToken
     * @param int $timeOut
     * @param int $jsonRule
     * @return array|bool
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-31 18:03
     */
    public static function handle(
        $method = null,
        $data = null,
        bool $needToken = true,
        int $timeOut = 20,
        int $jsonRule = 320
    ) {
        Log::handle("Request xy started", "星油", $method, [
            'requestData' => $data,
        ], "", "info");
        $secret = AuthConfigData::getAuthConfigValByName("XY_APP_SECRET");
        $domain = AuthConfigData::getAuthConfigValByName("XY_APP_DOMAIN");
        $headers = [
            "Content-Type: application/json;charset=utf-8",
            "instance:" . AuthConfigData::getAuthConfigValByName("XY_APP_INSTANCE"),
        ];

        if ($needToken) {

            $token = app('redis')->get("xy_token");

            if (empty($token)) {

                $responseData = self::handle('identity-service/openApiUser/loginOpenApi', [
                    'appKey'    => AuthConfigData::getAuthConfigValByName("XY_APP_KEY"),
                    'appSecret' => $secret,
                ], false, $timeOut, $jsonRule);
                app('redis')->setex("xy_token", 7199, $responseData['data']['accessToken']);
                $token = $responseData['data']['accessToken'];
            }

            $nonceStr = md5(Uuid::uuid());
            $jsSessionId = preg_replace('/\$2y\$/', '\$2a\$', password_hash(md5(
                $nonceStr . $secret), PASSWORD_DEFAULT), 1);
            list($mSec, $sec) = explode(' ', microtime());
            $data['timestamp'] = (float)sprintf('%.0f', (floatval($mSec) + floatval($sec)) * 1000);
            ksort($data);
            $jsonData = json_encode($data, $jsonRule);
            $publicKey = AuthConfigData::getAuthConfigValByName("XY_APP_RSA_PUBLIC_KEY");
            $realPublicKey = openssl_pkey_get_public($publicKey);
            openssl_public_encrypt($jsonData, $data['params'], $realPublicKey);
            $data['params'] = base64_encode($data['params']);
            $headers[] = "JSESSIONID:" . $jsSessionId;
            $headers[] = "token:" . $nonceStr;
            $headers[] = "signType:MD5";
            $headers[] = "sign:" . strrev(md5($jsonData . $secret));
            $headers[] = "Authorization:$token";
        }

        $result = self::curl($domain . $method, $data, $timeOut, $headers, 'post', true,
            $jsonRule);
        $parseResult = $result;
        if (isset($result['data']) and is_string($result['data'])) {

            $privateKey = AuthConfigData::getAuthConfigValByName("ZSH_G7_PRIVATE_KEY");
            $realPrivateKey = openssl_pkey_get_private($privateKey);
            $parseResult = '';
            $result = openssl_private_decrypt($result['data'], $parseResult, $realPrivateKey);
        }

        Log::handle("Request xy finished", "星油", $method, [
            'requestData' => $data,
            'parseResult' => $parseResult,
        ], $result, "info");
        if (!array_has($parseResult, ['code', 'msg'])) {

            throw new Exception($parseResult['msg'] ?? '', 5000999);
        }
        if ($parseResult['code'] != 20000) {

            throw new Exception($parseResult['msg'], 5000999);
        }
        return $result;
    }
}
