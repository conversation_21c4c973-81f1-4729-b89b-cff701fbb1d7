<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;
use Exception;
use Throwable;

class TC extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @param int $jsonRule
     * @return array
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/12/25 6:18 下午
     */
    public static function handle($method = null, $data = null, int $timeOut = 20, int $jsonRule = 320): array
    {
        Log::handle("Request tc started", "天畅", $method, [
            'requestData' => $data,
        ], "", "info");

        if ($method == 'external/callback/oilMessage') {
            $domain = AuthConfig::getAuthConfigValByName('TC_APP_STATION_DOMAIN');
        } else {
            $domain = AuthConfig::getAuthConfigValByName('TC_APP_TRADE_DOMAIN');
        }

        $result = self::curl("$domain$method", $data, $timeOut, [
            "Content-Type: application/json",
        ], 'post', true, $jsonRule);
        Log::handle("Request tc finished", "天畅", $method, [
            'requestData' => $data,
        ], $result, "info");

        if (!array_has($result, ['code'])) {
            throw new Exception('天畅接口输出数据格式错误', 5000999);
        }

        if (!array_has($result, ['code'])) {
            throw new Exception($result['message'] ?? '', 5000999);
        }

        return $result;
    }
}
