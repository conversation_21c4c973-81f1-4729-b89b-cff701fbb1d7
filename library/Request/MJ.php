<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;
use Exception;
use Tool\CryptAES;

class MJ extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @param int $jsonRule
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-31 18:03
     */
    public static function handle($method = null, array $data = null, int $timeOut = 20, int $jsonRule = 320)
    {
        Log::handle("Request mj started", "秒加", $method, [
            'requestData' => $data,
        ], "", "info");
        $domain = AuthConfig::getAuthConfigValByName("MJ_APP_DOMAIN");
        $requestData = self::sign($data, $jsonRule);
        $result = self::curl("$domain$method", $requestData, $timeOut, [
            "Content-Type: application/json;charset=utf-8",
        ], 'post', true, $jsonRule);
        Log::handle("Request mj finished", "秒加", $method, [
            'requestData' => $data,
            'realRequestData' => $requestData,
        ], $result, "info");
        if (!array_has($result, ['errcode'])) {
            throw new Exception($result['errmsg'] ?? '', 5000999);
        }
        if ($result['errcode'] != 0) {
            throw new Exception($result['errmsg'] ?? '', 5000999);
        }
        $result['decrypt_data'] = [];
        if (!empty($result['replydata'])) {
            $result['decrypt_data'] = json_decode(CryptAES::decrypt(
                $result['replydata'],
                AuthConfig::getAuthConfigValByName("MJ_APP_AES_KEY")
            ) ?? '', true) ?? [];
        }
        Log::handle("Request mj finished", "秒加", $method, [
            'requestData' => $data,
            'realRequestData' => $requestData,
        ], $result, "info");
        return $result;
    }

    protected static function sign(
        array $data,
        int $jsonRule = 512
    ): array {
        $appId = AuthConfig::getAuthConfigValByName("MJ_APP_ID");
        $data['customernumber'] = $appId;
        $prepareData = [];
        ksort($data);
        foreach ($data as $key => $val) {
            if ($key != '' && !is_null($val)) {
                if (is_array($val) or is_object($val)) {
                    $val = json_encode($val, $jsonRule);
                }
                $prepareData[] = "$key=$val";
            }
        }
        $prepareStr = implode("&", $prepareData);
        $data['hmac'] = hash_hmac('md5', $prepareStr, AuthConfig::getAuthConfigValByName("MJ_APP_SIGN_KEY"));
        return [
            'customernumber' => $appId,
            'data'           => CryptAES::encrypt(
                json_encode($data, $jsonRule),
                AuthConfig::getAuthConfigValByName("MJ_APP_AES_KEY")
            ),
        ];
    }
}