<?php

namespace Request;

use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class YXT extends Base implements ThirdParty
{
    /**
     * @param string $method
     * @param array $data
     * @return array
     * @throws Exception
     * <AUTHOR> <<EMAIL>>
     * @since 2022/2/7 11:36 AM
     */
    public static function handle(string $method = "", array $data = []): array
    {
        $requestData = $data;
        $domain = AuthConfig::getAuthConfigValByName("YXT_APP_DOMAIN");
        Log::handle("Request yxt started", "蚁信通", $method, [
            'requestData' => $requestData,
        ], "", "info");
        $result = self::curl(
            "$domain$method",
            $requestData,
            10,
            [],
            'post',
            true,
            320
        );
        Log::handle("Request yxt finished", "蚁信通", $method, [
            'requestData' => $requestData,
        ], $result, "info");
        if (!array_has($result, ['code'])) {
            throw new Exception('蚁信通接口输出数据格式错误', 5000999);
        }
        if ($result['code'] != 0) {
            throw new Exception($result['message'] ?? '', 5000999);
        }
        return $result;
    }

    public static function sign(array &$data): string
    {
        $secret = AuthConfig::getAuthConfigValByName("YXT_APP_SECRET");
        $signArr = [$secret];
        ksort($data);
        foreach ($data as $k => &$v) {
            if (empty($v)) {
                $signArr[] = "$k";
                continue;
            }
            if (is_array($v) or is_object($v)) {
                $v = json_encode($v, 320);
                $signArr[] = "$k$v";
                continue;
            }
            $signArr[] = "$k$v";
        }
        $signArr[] = $secret;
        return strtoupper(md5(implode('', $signArr)));
    }
}
