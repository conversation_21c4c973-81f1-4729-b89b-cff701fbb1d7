<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class RRS extends Base implements ThirdParty
{
    /**
     * @param string $method
     * @param array $data
     * @return array
     * @throws Exception ---------------------------------------------------
     * <AUTHOR> <zhu<PERSON><PERSON>@zhongqixing.com>
     * @since 2019-07-12 16:11
     */
    public static function handle(string $method = '', array $data = []): array
    {
        $domain = AuthConfigData::getAuthConfigValByName("RRS_APP_DOMAIN");
        Log::handle("Request RRS started", "日日顺", $method, [
            "requestParams" => $data,
        ], "", "info");
        $data = [
            'data'      => json_encode($data),
            'timestamp' => date("Y-m-d H:i:s"),
            'app_key'   => AuthConfigData::getAuthConfigValByName("RRS_APP_KEY"),
        ];
        $data['sign'] = createSign(
            $data,
            AuthConfigData::getAuthConfigValByName(
                "RRS_APP_SECRET"
            )
        );
        $result = self::curl(
            "$domain$method",
            $data,
            20,
            [],
            "post",
            true,
            320
        );
        Log::handle("Request RRS finished", "日日顺", $method, [
            "requestParams" => $data,
        ], $result, "info");
        if (!array_has($result, ['code'])) {
            throw new Exception('日日顺接口输出数据格式错误', 5000999);
        }
        if ($result['code'] != 0) {
            throw new Exception($result['msg'] ?? '', 5000999);
        }
        return $result;
    }
}
