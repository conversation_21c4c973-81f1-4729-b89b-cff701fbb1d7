<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;
use Faker\Provider\Uuid;


class XMSK extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 15:29
     */
    public static function handle($method = null, $data = null, int $timeOut = 60): array
    {
        Log::handle("Request xmsk started", "象盟数科", $method, [
            'requestData' => $data,
        ], "", "info");
        $signData = [];
        $signData['bizCode'] = $method;
        $signData['appId'] = AuthConfigData::getAuthConfigValByName("XMSK_APP_KEY");
        $signData['bizId'] = Uuid::uuid();
        $signData['timestamp'] = getMillisecond();
        $signData['content'] = json_encode($data, 320);
        $requestData = self::sign($signData);
        $result = self::curl(
            AuthConfigData::getAuthConfigValByName("XMSK_APP_DOMAIN"),
            http_build_query($requestData)
        );
        Log::handle("Request xmsk finished", "象盟数科", $method, [
            'requestData'     => $data,
            'realRequestData' => $requestData,
        ], $result, "info");
        if (!array_has($result, ['code', 'message'])) {
            throw new Exception('象盟数科接口输出数据格式错误', 5000999);
        }
        if ($result['code'] != 0) {
            throw new Exception($result['message'] ?? '', 5000999);
        }
        if (!is_array($result['message']) or !array_has($result['message'], ['status'])) {
            throw new Exception('象盟数科接口输出数据格式错误', 5000999);
        }
        if ($result['message']['status'] != 0) {
            throw new Exception($result['message']['message'] ?? '', 5000999);
        }
        return $result;
    }

    /**
     * @throws Exception
     */
    public static function sign($data): array
    {
        if (!array_has($data, ['timestamp', 'bizCode', 'bizId', 'content'])) {
            throw new Exception("象盟数科签名缺失必要参数", 5000001);
        }
        $data['sign'] = sha1(
            "bizCode={$data['bizCode']}&bizId={$data['bizId']}&content={$data['content']}" .
            "&timestamp={$data['timestamp']}&appKey=" .
            AuthConfigData::getAuthConfigValByName("XMSK_APP_SECRET")
        );
        return $data;
    }
}
