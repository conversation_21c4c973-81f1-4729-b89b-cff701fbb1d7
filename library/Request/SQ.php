<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;
use Exception;
use Faker\Provider\Uuid;
use Tool\Des3;

class SQ extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @param int $jsonRule
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-31 18:03
     */
    public static function handle($method = null, $data = null, int $timeOut = 120, int $jsonRule = 256): array
    {
        Log::handle("Request sq started", "狮桥", $method, [
            'requestData' => $data,
        ], "", "info");
        $userId = AuthConfig::getAuthConfigValByName("SQ_APP_FIRST_KEY");
        $appId = AuthConfig::getAuthConfigValByName("SQ_APP_SECOND_KEY");
        $domain = AuthConfig::getAuthConfigValByName("SQ_APP_DOMAIN");
        $requestData = self::sign($data ?? [], $userId, $jsonRule);
        $result = self::curl("$domain$method", $requestData, $timeOut, [
            "Content-Type: application/json;charset=utf-8",
            "userid: $userId",
            "requestid: " . Uuid::uuid(),
            "appid: $appId",
        ], 'post', true, $jsonRule);
        Log::handle("Request sq finished", "狮桥", $method, [
            'requestData'    => $data,
            'encRequestData' => $requestData,
        ], $result, "info");

        if (!array_has($result, ['code'])) {
            throw new Exception('狮桥接口输出数据格式错误', 5000999);
        }

        if ($result['code'] != 200) {
            throw new Exception($result['msg'] ?? '', 5000666);
        }

        return $result;
    }

    protected static function sign(array $data, string $userId, int $jsonRule = 320): array
    {
        $dataStr = json_encode($data, $jsonRule);
        $timestamp = time();
        $desKey = base64_decode(AuthConfig::getAuthConfigValByName("SQ_APP_FIRST_SECRET"));
        $userKey = AuthConfig::getAuthConfigValByName("SQ_APP_SECOND_SECRET");
        $encryptObj = (new Des3($desKey, 'cbc'));
        $encData = $encryptObj->encrypt($dataStr, substr($desKey, 0, 8));
        $signStr = "data=$encData&timestamp=$timestamp&userid=$userId&userkey=$userKey";
        $sign = hash_hmac("sha256", $signStr, $userKey);
        return [
            "data"      => $encData,
            "timestamp" => $timestamp,
            "sign"      => $sign,
            "sign_type" => "sha256",
        ];
    }
}
