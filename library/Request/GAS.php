<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;
use Carbon\Carbon;

class GAS extends Base implements ThirdParty
{
    public static function handle(
        $method = null,
        $data = null,
        string $accessKey = '',
        string $secret = '',
        int $timeOut = 20,
        int $jsonRule = 512
    ) {
        Log::handle("Request gas started", "汇管油", $method, [
            'requestData' => $data,
            'accessKey'   => $accessKey,
            'secret'      => $secret
        ], "", "info");

        $params = [
            'method' => $method,
            'data' => json_encode($data),
            'timestamp' => Carbon::now('Asia/Shanghai')->toDateTimeString(),
            'app_key'   => $accessKey
        ];

        $sign = createSign($params, $secret);
        $params['sign'] = $sign;
        $api_url = AuthConfig::getAuthConfigValByName('API_GAS_API_URL');
        $result = self::curl($api_url, $params, $timeOut, getTraceInjectHeaders(true));

        Log::handle("Request gas finished", "汇管油", $method, [
            'requestData' => $data,
            'accessKey'   => $accessKey,
            'secret'      => $secret
        ], $result, "info");

        if (!array_has($result, ['code', 'data'])) {

            return [];
        }

        return $result;
    }
}
