<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class SQZSH extends Base implements ThirdParty
{
    /**
     * @param string $method
     * @param array $data
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 16:11
     */
    public static function handle(string $method = '', array $data = [], bool $checkCode = true): array
    {
        $data['companyCode'] = AuthConfigData::getAuthConfigValByName("SQZSH_COMPANY_CODE");
        $domain = AuthConfigData::getAuthConfigValByName("SQZSH_APP_DOMAIN");
        $data = self::sign($data);
        Log::handle("Request SQZSH started", "上汽中石化", $method, [
            "requestParams" => $data,
        ], "", "info");
        $result = self::curl("$domain$method", $data, 20, [
            "Content-Type: application/json",
            'Expect:',
        ], "post", true, 320);
        Log::handle("Request SQZSH finished", "上汽中石化", $method, [
            "requestParams" => $data,
        ], $result, "info");
        if ($checkCode) {
            if (!array_has($result, ['Code'])) {
                throw new Exception('上汽中石化接口输出数据格式错误', 5000999);
            }
            if ($result['Code'] != 2000) {
                throw new Exception($result['Message'] ?? '', 5000999);
            }
        }
        return $result;
    }

    public static function sign($data, int $timestamp = 0): array
    {
        $signData = [
            'appId'     => AuthConfigData::getAuthConfigValByName("SQZSH_APP_ID"),
            'parameter' => is_array($data) ? json_encode($data, 320) : $data,
            'secretKey' => AuthConfigData::getAuthConfigValByName("SQZSH_APP_SECRET"),
            'timestamp' => $timestamp <= 0 ? getMillisecond() : $timestamp,
        ];
        ksort($signData);
        $waitSignArr = [];
        foreach ($signData as $k => $v) {
            $waitSignArr[] = "$k=$v";
        }
        return [
            'secretKey' => $signData['secretKey'],
            'appId'     => $signData['appId'],
            'parameter' => $signData['parameter'],
            'sign'      => md5(implode("&", $waitSignArr) . $signData['appId']),
            'timestamp' => (string)$signData['timestamp'],
        ];
    }
}
