<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class GDQP extends Base implements ThirdParty
{
    public static $paddingPrecisionFields = [
        'quantity'         => 3,
        'originalPrice'    => 3,
        'amount'           => 2,
        'shellDiscount'    => 2,
        'discount'         => 2,
        'fuelsDiscount'    => 2,
        'nonfuelsDiscount' => 2,
        'totalDiscount'    => 2,
    ];

    /**
     * @param null $method
     * @param string $requestMethod
     * @param null $data
     * @param int $timeOut
     * @return array
     * @throws Exception ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 15:29
     */
    public static function handle(
        $method = null,
        string $requestMethod = 'post',
        $data = null,
        int $timeOut = 60
    ): array
    {
        Log::handle("Request gdqp started", "广东壳牌(新)", $method, [
            'requestData' => $data,
        ], "", "info");
        $data['merchantNum'] = AuthConfigData::getAuthConfigValByName('GDQP_APP_KEY');
        $data['timestamp'] = time();
        $data['sign'] = self::sign($data);
        $apiUrl = AuthConfigData::getAuthConfigValByName('GDQP_APP_DOMAIN') . $method;
        $result = self::curl($apiUrl, $data, $timeOut, [], $requestMethod, true, 320);
        Log::handle("Request gdqp finished", "广东壳牌(新)", $method, [
            'requestData' => $data,
        ], $result, "info");
        if (!array_has($result, ['respCode', 'respMsg'])) {
            throw new Exception('广东壳牌(新)接口输出数据格式错误', 5000999);
        } elseif ($result['respCode'] != '00000') {
            throw new Exception($result['respMsg'] ?? '', 5000999);
        }
        return $result;
    }

    public static function sign(array $data, string $secret = ''): string
    {
        ksort($data);
        $signData = [];
        foreach ($data as $k => $v) {
            if (is_null($v) or $v == "") {
                continue;
            }
            if (is_array($v)) {
                $v = json_encode($v);
            }
            $signData[] = $k . $v;
        }
        $signStr = implode('', $signData) . (empty($secret) ? AuthConfigData::getAuthConfigValByName(
                'GDQP_APP_SECRET'
            ) : $secret);
        return hash('sha256', $signStr);
    }
}
