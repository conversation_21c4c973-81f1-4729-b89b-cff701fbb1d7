<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;
use Carbon\Carbon;
use Exception;

class JD extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-24 16:27
     */
    public static function handle($method = null, $data = null, int $timeOut = 5): array
    {
        $accessKey = AuthConfig::getAuthConfigValByName("JD_APP_KEY");
        $secret = AuthConfig::getAuthConfigValByName("JD_SECRET");
        Log::handle("Request jd started", "九鼎", $method, [
            'requestData' => $data,
            'accessKey'   => $accessKey,
            'secret'      => $secret
        ], "", "info");

        $params = [
            'data'          => $data,
            'timestamp'     => Carbon::now('Asia/Shanghai')->toDateTimeString(),
            'truck_app_key' => $accessKey
        ];

        $sign = createSignForJd($params, $secret, 320);
        $params['sign'] = $sign;
        $api_url = AuthConfig::getAuthConfigValByName('JD_DOMAIN');
        $result = self::curl("$api_url$method", $params, $timeOut, [
            'Content-Type: application/json;charset=utf-8',
        ], 'post', true, 320);

        Log::handle("Request jd finished", "九鼎", $method, [
            'requestData' => $data,
            'accessKey'   => $accessKey,
            'secret'      => $secret
        ], $result, "info");
        if (!array_has($result, ['code'])) {

            return [];
        }

        if ($result["code"] != 0) {

            throw new Exception($result['msg'] ?? '', 5000999);
        }

        if (!array_has($result, ['data'])) {

            return [];
        }

        return $result;
    }
}
