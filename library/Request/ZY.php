<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class ZY extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @param int $jsonRule
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/9/6 5:47 下午
     */
    public static function handle($method = null, $data = null, int $timeOut = 60, int $jsonRule = 512): array
    {
        $appId = AuthConfigData::getAuthConfigValByName("ZY_APP_ID");
        $appKey = AuthConfigData::getAuthConfigValByName("ZY_APP_KEY");
        $domain = AuthConfigData::getAuthConfigValByName("ZY_DOMAIN");
        Log::handle("Request zy started", "找油", $method, [
            'requestData' => $data,
            'appId'       => $appId,
            'appKey'      => $appKey
        ], "", "info");

        $params = [
            'method'    => $method,
            'data'      => $data,
            'timestamp' => time(),
            'app_id'    => $appId
        ];

        $sign = createSignForZy($data ?? [], $params['timestamp'], $params['method'], $params['app_id'], $appKey);
        $params['sign'] = $sign;
        $params['data'] = json_encode($params['data'], $jsonRule);
        $result = self::curl($domain, $params, $timeOut, [
            "Content-Type: application/json;charset=utf-8"
        ], 'post', true, $jsonRule);

        Log::handle("Request zy finished", "找油", $method, [
            'requestData' => $data,
            'appId'       => $appId,
            'appKey'      => $appKey
        ], $result, "info");

        if (!array_has($result, ['code', 'data'])) {
            throw new Exception('找油接口输出数据格式错误', 5000999);
        }

        if ($result['code'] !== "0000") {
            throw new Exception($result['msg'] ?? '', 5000999);
        }

        return $result;
    }
}
