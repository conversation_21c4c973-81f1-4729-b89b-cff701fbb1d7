<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class CFHY extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @param int $jsonRule
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-31 18:03
     */
    public static function handle($method = null, $data = null, int $timeOut = 20, int $jsonRule = 512): array
    {
        Log::handle("Request cfhy started", "成丰货运", $method, [
            'requestData' => $data,
        ], "", "info");

        $domain = AuthConfig::getAuthConfigValByName("CFHY_APP_DOMAIN");
        $result = self::curl("$domain$method", $data, $timeOut, [
            'Content-Type: application/json;charset=utf-8',
            'Expect:',
        ], 'post', true, $jsonRule);

        Log::handle("Request cfhy finished", "成丰货运", $method, [
            'requestData' => $data,
        ], $result, "info");

        if (!array_has($result, ['code'])) {
            throw new Exception($result['msg'] ?? ($result['message'] ?? ''), 5000999);
        }

        if ($result['code'] != 0) {
            throw new Exception($result['msg'] ?? ($result['message'] ?? ''), 5000999);
        }

        return $result;
    }
}
