<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class TBJX extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @param int $jsonRule
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-31 18:03
     */
    public static function handle($method = null, $data = null, int $timeOut = 60, int $jsonRule = 256)
    {
        Log::handle("Request tbjx started", "通宝吉祥", $method, [
            'requestData' => $data,
        ], "", "info");
        $secret = AuthConfig::getAuthConfigValByName("TBJX_APP_SECRET");
        $domain = AuthConfig::getAuthConfigValByName("TBJX_APP_DOMAIN");
        $appId = AuthConfig::getAuthConfigValByName("TBJX_APP_KEY");
        $requestData = [];
        $requestData['name'] = $appId;
        $requestData['secret'] = $secret;
        $signData = self::sign($data ?? [], $appId, $secret, $jsonRule);
        $requestData['reqBody'] = $signData['data'];
        $requestData['sign'] = $signData['sign'];
        $result = self::curl($domain . $method, $requestData, $timeOut, [
            "Content-Type: application/json;charset=utf-8",
        ], 'post', true, $jsonRule);
        Log::handle("Request tbjx finished", "通宝吉祥", $method, [
            'requestData' => $data,
        ], $result, "info");
        if (!array_has($result, ['code'])) {
            throw new Exception($result['msg'] ?? '', 5000999);
        }
        if ($result['code'] != 200) {
            throw new Exception($result['msg'] ?? '', 5000999);
        }
        return $result;
    }

    protected static function sign(
        array $data,
        string $appId,
        string $secret,
        int $jsonRule = 256
    ): array {
        $signData = $appId . "@@" . $secret . "@@" . time() . "@@" . md5(uniqid()) . "@@" . json_encode(
                $data,
                $jsonRule
            ) . "@@";
        $privateKey = openssl_get_privatekey(transJavaRsaKeyToPhpOpenSSL(
            AuthConfigData::getAuthConfigValByName(
                "G7_TBJX_RSA_PRIVATE_KEY"
            ), false));
        openssl_sign($signData, $sign, $privateKey, OPENSSL_ALGO_SHA256);
        openssl_free_key($privateKey);
        return [
            'data' => $signData,
            'sign' => base64_encode($sign),
        ];
    }
}
