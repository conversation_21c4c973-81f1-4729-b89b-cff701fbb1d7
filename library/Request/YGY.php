<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;


class YGY extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @param bool $jsonRequest
     * @return array
     * @throws Exception ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019-07-12 15:29
     */
    public static function handle($method = null, $data = null, int $timeOut = 20, bool $jsonRequest = false): array
    {
        Log::handle("Request ygy started", "易管油", $method, [
            'requestData' => $data,
        ], "", "info");
        $domain = AuthConfigData::getAuthConfigValByName("YGY_APP_DOMAIN");
        $appId = AuthConfigData::getAuthConfigValByName("YGY_APP_KEY");
        $appSecret = AuthConfigData::getAuthConfigValByName("YGY_APP_SECRET");
        $sign = self::sign($data, $appSecret);
        $result = self::curl(
            $domain . $method . "?sign=" . $sign . "&appid=" . $appId,
            $data,
            $timeOut,
            [],
            'post',
            $jsonRequest,
            320
        );
        Log::handle("Request ygy finished", "易管油", $method, [
            'requestData' => $data,
        ], $result, "info");

        if (!array_has($result, ['code'])) {
            throw new Exception('易管油接口输出数据格式错误', 5000999);
        } else {
            if ($result['code'] != 0) {
                throw new Exception($result['msg'] ?? '', 5000999);
            }
        }

        return $result;
    }

    /**
     * PHP 版本获得加密秘钥
     * @param $postData
     * @param $secret
     * @param string $timestamp
     * @param string $randomNumber
     * @return string
     */
    public static function sign($postData, $secret, $timestamp = '', $randomNumber = ''): string
    {
        //获得当前时间戳（秒级）
        $postData['api_time_stamp'] = empty($timestamp) ? time() : $timestamp;
        //获得随机数 (0-100000)
        $postData['api_random_number'] = empty($randomNumber) ? rand(0, 100000) : $randomNumber;
        //按照键排序
        ksort($postData);
        //拼接加密字符串
        $signature = 'secret=' . $secret;
        foreach ($postData as $k => $v) {
            if (is_array($v) or is_object($v)) {
                sort($v);
                $v = json_encode($v, JSON_UNESCAPED_UNICODE);
            }

            $signature .= '&' . $k . '=' . $v;
        }
        //返回加密字符串
        return md5(sha1($signature)) . '_' . $postData['api_time_stamp'] . '_' . $postData['api_random_number'];
    }
}
