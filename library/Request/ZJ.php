<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;
use Carbon\Carbon;
use Exception;

class ZJ extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param string $requestMethod
     * @param null $data
     * @param int $timeOut
     * @param bool $jsonRequest
     * @return array
     * @throws Exception ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-31 18:03
     */
    public static function handle(
        $method = null,
        string $requestMethod = 'post',
        $data = null,
        int $timeOut = 20,
        bool $jsonRequest = true
    ): array {
        Log::handle("Request zj started", "中交", $method, [
            'requestData' => $data,
        ], "", "info");
        $secret = AuthConfig::getAuthConfigValByName("ZJ_APP_SECRET");
        $domain = AuthConfig::getAuthConfigValByName("ZJ_APP_DOMAIN");
        $params = [
            'data'      => json_encode($data),
            'timestamp' => Carbon::now('Asia/Shanghai')->toDateTimeString(),
            'app_key'   => AuthConfig::getAuthConfigValByName("ZJ_APP_KEY")
        ];
        $params['sign'] = createSign($params, $secret);
        $result = self::curl("$domain$method", $params, $timeOut, [], $requestMethod,
            $jsonRequest);
        Log::handle("Request zj finished", "中交", $method, [
            'requestData' => $params,
        ], $result, "info");

        if (!array_has($result, ['code'])) {
            throw new Exception('中交接口输出数据格式错误', 5000999);
        }

        if ($result['code'] != 0) {

            throw new Exception($result['msg'] ?? '', 5000999);
        }

        return $result;
    }
}
