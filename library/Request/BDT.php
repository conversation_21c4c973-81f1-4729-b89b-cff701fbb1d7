<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;
use Illuminate\Support\Facades\Redis;
use Throwable;
use Tool\CryptAES;

class BDT extends Base implements ThirdParty
{
    public const CACHE_KEY = "auth_bdt";
    public const PERSIST_FIELD = [
        'id',
        'appid',
        'draw'
    ];

    /**
     * @param string $method
     * @param array $data
     * @return array|mixed
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019-07-12 16:11
     */
    public static function handle($method = '', $data = [])
    {
        $domain = AuthConfigData::getAuthConfigValByName("BDT_DOMAIN");
        $requestParams = [];
        $requestParams['appid'] = $method;
        $secret = AuthConfigData::getAuthConfigValByName("BDT_SECRET");
        $requestParams['id'] = AuthConfigData::getAuthConfigValByName("BDT_PARTNER_ID");

        foreach ($data as $k => $v) {

            if (!in_array($k, self::PERSIST_FIELD)) {

                $requestParams[$k] = $v;
            }
        }

        try {

            $requestParams['draw'] = Redis::connection()->hget(self::CACHE_KEY, md5($method));

            if (empty($requestParams['draw']) or $requestParams['draw'] == 0) {

                $requestParams['draw'] = 0;
            }

            $requestParams['draw'] += 1;
            Redis::connection()->hset(self::CACHE_KEY, md5($method), $requestParams['draw']);
        } catch (Throwable $exception) {

            Log::handle("Get bdt draw failed",
                "宝兑通", $method, $data, "", "error");
        }

        $requestParams['draw'] = (string)$requestParams['draw'];
        $signStr = CryptAES::encrypt(json_encode($requestParams), $secret);
        Log::handle("Request bdt started", "宝兑通", $method, [
            "requestParams" => $requestParams,
            "realUrl"       => $domain . $signStr
        ], "", "info");
        $result = self::curl("$domain$signStr", $requestParams, 20, [
            "Content-Type: application/json",
        ], "post", true);
        $responseData = CryptAES::decrypt($result['responseRawData'] ?? "", $secret);
        $responseDataArr = json_decode($responseData, true);
        Log::handle("Request bdt finished", "宝兑通", $method, [
            "requestParams" => $requestParams,
            "realUrl"       => $domain . $signStr
        ], ['responseRawData' => $result, 'parsedResponseData' => $responseDataArr], "info");

        if (is_null($responseDataArr)) {

            throw new Exception('Request bdt failed', 5000999);
        } else {

            if (!array_has($responseDataArr, ['code', 'status'])) {
                throw new Exception('宝兑通接口输出数据格式错误', 5000999);
            } else {

                if (!$responseDataArr['status']) {

                    throw new Exception($responseDataArr['desc'] ?? "", 5000999);
                } else {

                    $responseDataArr = $responseDataArr['data'] ?? [];
                }
            }
        }

        return $responseDataArr;
    }
}
