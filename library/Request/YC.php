<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class YC extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 15:29
     */
    public static function handle($method = null, $data = null, int $timeOut = 60): array
    {
        Log::handle("Request yc started", "广东壳牌", $method, [
            'requestData' => $data,
        ], "", "info");
        $data['merchantno'] = AuthConfigData::getAuthConfigValByName('YC_APP_KEY');
        $data['timestamp'] = time();
        $data['sign'] = self::sign($data);
        $apiUrl = AuthConfigData::getAuthConfigValByName('YC_APP_DOMAIN') . $method;
        $result = self::curl($apiUrl, $data, $timeOut, [], 'get');
        Log::handle("Request yc finished", "广东壳牌", $method, [
            'requestData' => $data,
        ], $result, "info");

        if (!array_has($result, ['respCode', 'respMsg'])) {
            throw new Exception('广东壳牌接口输出数据格式错误', 5000999);
        } else {
            if ($result['respCode'] != '00000') {
                throw new Exception($result['respMsg'] ?? '', 5000999);
            }
        }

        return $result;
    }

    public static function sign(array $data): string
    {
        $prepareData = [];
        self::prepareData($data, $prepareData);
        ksort($prepareData);
        $signData = [];

        foreach ($prepareData as $k => $v) {
            if (is_array($v)) {
                foreach ($v as $cv) {
                    $signData[] = $k . $cv;
                }

                continue;
            }

            $signData[] = $k . $v;
        }

        return hash(
            'sha256',
            implode('', $signData) . AuthConfigData::getAuthConfigValByName(
                'YC_APP_SECRET'
            )
        );
    }

    public static function prepareData(array $data, array &$returnData = [])
    {
        foreach ($data as $k => $v) {
            if (is_array($v)) {
                foreach ($v as $cv) {
                    if (is_array($cv)) {
                        foreach ($cv as $cck => $ccv) {
                            if (!isset($returnData[$cck])) {
                                $returnData[$cck] = [];
                            }

                            $returnData[$cck][] = $ccv;
                        }
                    }
                }

                continue;
            }

            $returnData[$k] = $v;
        }
    }
}
