<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;

class GHC extends Base implements ThirdParty
{

    public static function handle(string $inCode = '', array $content = [])
    {
        $ghcDomain = AuthConfig::getAuthConfigValByName("GHC_DOMAIN");
        $ghcAppKey = AuthConfig::getAuthConfigValByName("GHC_APP_KEY");
        $ghcAppId = AuthConfig::getAuthConfigValByName("GHC_APP_ID");
        $ghcBasicUrl = $ghcDomain;
        $requestParams = [];
        $requestParams['rd'] = (string)mt_rand(100, 999);
        $requestParams['appScrectKey'] = $ghcAppKey;
        $requestParams['inCode'] = $inCode;
        $requestParams['tokenId'] = '';
        $requestParams['time'] = (string)getMillisecond();
        $requestParams['content'] = $content;
        $signData = $requestParams;
        unset($signData['inCode']);
        $signData['content'] = json_encode($signData['content'], 320);
        $signData = array_values($signData);
        sort($signData, SORT_STRING);
        $waitSignStr = php2JavaArrayValuesToString($signData);
        $signStr = sha1($waitSignStr);
        $requestParams['sign'] = $signStr;
        $requestParams['appId'] = $ghcAppId;
        Log::handle("Request ghc started", "管好车", $inCode, [
            "requestParams" => $requestParams,
            "realUrl"       => $ghcBasicUrl
        ], "", "info");
        $result = self::curl($ghcBasicUrl, $requestParams, 10, [
            'Content-Type: application/json'
        ], 'post', true, 320);
        Log::handle("Request ghc finished", "管好车", $inCode, [
            "requestParams" => $requestParams,
            "realUrl"       => $ghcBasicUrl
        ], $result, "info");

        return $result;
    }
}
