<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;
use Carbon\Carbon;
use Exception;

class MK extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @param bool $jsonRequest
     * @param int $jsonRule
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/11/20 5:41 下午
     */
    public static function handle(
        $method = null,
        $data = null,
        int $timeOut = 20,
        bool $jsonRequest = false,
        int $jsonRule = 256
    ): array {
        Log::handle("Request mk started", "码客", $method, [
            'requestData' => $data,
        ], "", "info");

        $appId = AuthConfig::getAuthConfigValByName("MK_APP_ID");
        $domain = AuthConfig::getAuthConfigValByName("MK_APP_DOMAIN");
        $appKey = AuthConfig::getAuthConfigValByName("MK_APP_KEY");
        $timestamp = Carbon::now('Asia/Shanghai')->timestamp;
        $sign = md5("$appId$appKey$timestamp");

        if (!$jsonRequest) {

            $data = http_build_query($data ?? []);
            $headers = [
                'Content-Type: application/x-www-form-urlencoded',
            ];
        } else {

            $headers = [
                'Content-Type: application/json;charset=utf-8',
            ];
        }

        $result = self::curl(
            "$domain$method?appid=$appId&sign=$sign&ts=$timestamp",
            $data,
            $timeOut,
            $headers, 'post', $jsonRequest);
        Log::handle("Request mk finished", "码客", $method, [
            'requestData' => $data,
        ], $result, "info");

        if (!array_has($result, ['code'])) {
            throw new Exception('码客接口输出数据格式错误', 5000999);
        }

        if ($result['code'] != 0) {

            throw new Exception($result['msg'] ?? '', 5000999);
        }

        if (!array_has($result, ['data'])) {
            throw new Exception('码客接口输出数据格式错误', 5000999);
        }

        return $result;
    }
}
