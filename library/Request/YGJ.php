<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;
use Tool\RsaToJava;

class YGJ extends Base implements ThirdParty
{
    /**
     * @param string $method
     * @param mixed $data
     * @return array|mixed
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 14:47
     */
    public static function handle(string $method = '', $data = [])
    {
        $domain = AuthConfigData::getAuthConfigValByName("YGJ_DOMAIN");
        $publicKey = AuthConfigData::getAuthConfigValByName("YGJ_PUBLIC_KEY");
        $signParams = [
            'cmd'     => $method,
            'data'    => json_encode($data, 256),
            'channel' => AuthConfigData::getAuthConfigValByName("YGJ_CHANNEL")
        ];
        $rsa = new RsaToJava($publicKey);
        $encData = "cmd=$method&data={$signParams['data']}&channel={$signParams['channel']}";
        $requestParams = $signParams;
        $requestParams['data'] = $rsa->publicEncrypt($encData);
        Log::handle("Request ygj started", "银管家", $method, [
            "requestParams" => $data,
            "realUrl"       => $domain,
            'sign'          => $requestParams['data'],
            'signParams'    => $signParams
        ], '', "info");
        $responseData = self::curl($domain, $requestParams, 10, [
            'Content-Type: application/json',
        ], 'post', true);
        Log::handle("Request ygj finished", "银管家", $method, [
            "requestParams" => $data,
            "realUrl"       => $domain,
            'sign'          => $requestParams['data'],
            'signParams'    => $signParams
        ], $responseData, "info");

        if (!array_has($responseData, ['code', 'data'])) {
            throw new Exception('银管家接口输出数据格式错误', 5000999);
        } else {
            if ($responseData['code'] != 0) {
                throw new Exception($responseData['msg'], 5000999);
            } else {
                $responseData = $responseData['data'];
            }
        }

        return $responseData;
    }
}
