<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class GBDW extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param string $requestType
     * @param array|null $data
     * @param int $timeOut
     * @return array
     * @throws Exception ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 15:29
     */
    public static function handle(
        $method = null,
        string $requestType = 'get',
        ?array $data = [],
        int $timeOut = 5
    ): array
    {
        $realData = $data;
        $realData['sign'] = self::sign($data);
        Log::handle("Request gbdw started", "固本能源(下游)", $method, [
            'requestData' => $realData,
        ], "", "info");
        $apiUrl = AuthConfigData::getAuthConfigValByName('GBDW_APP_DOMAIN') . "/channelCode/" .
                  AuthConfigData::getAuthConfigValByName("GBDW_APP_KEY") . "/" . $method;
        $result = self::curl($apiUrl, $realData, $timeOut, [
            "timestamp:" . getMillisecond(),
        ], $requestType, true, 320);
        Log::handle("Request gbdw finished", "固本能源(下游)", $method, [
            'requestData' => $realData,
        ], $result, "info");
        if (!array_has($result, ['code']) and !array_has($result, ['resultCode'])) {
            throw new Exception('固本能源(下游)接口输出数据格式错误', 5000999);
        }
        if ((array_has($result, 'code') and $result['code'] != 0) or (array_has(
                                                                          $result,
                                                                          'resultCode'
                                                                      ) and $result['resultCode'] != 0)) {
            throw new Exception($result['msg'] ?? ($result['resultMsg'] ?? ''), 5000999);
        }
        return $result;
    }

    public static function sign(?array $data): string
    {
        $signStr = "";
        ksort($data);
        foreach ($data as $k => $v) {
            if (empty($k)) {
                continue;
            }
            if (is_array($v) or is_object($v)) {
                $v = json_encode($v, 320);
            }
            if ((is_string($v) and $v === "") or is_null($v)) {
                continue;
            }
            $signStr .= "$k$v";
        }
        $signStr .= AuthConfigData::getAuthConfigValByName('GBDW_APP_SECRET');
        return md5($signStr);
    }
}
