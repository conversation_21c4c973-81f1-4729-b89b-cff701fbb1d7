version: "2"
services:
  oil-adapter:
    restart: always
    image: ${G7PAY_DOCKER_REGISTRY}/${PROJECT_GROUP}/${PROJECT_NAME}:${PROJECT_VERSION}
    labels:
      io.rancher.container.pull_image: always
      io.rancher.container.start_once: "true"
      project_description: 油站连接器
      project_group: php-spec
      project_name: oil-adapter
      project_type: php
      project_version: ${PROJECT_VERSION}
    environment:
      PHPENV: ${PHPENV}
      APPLICATION__NAME: ${APPLICATION__NAME}
      APPLICATION__DEBUG: ${APPLICATION__DEBUG}
      DB__CONNECTION: ${DB__CONNECTION}
      DB__HOST: ${DB__HOST}
      DB__PORT: ${DB__PORT}
      DB__DATABASE: ${DB__DATABASE}
      DB__USERNAME: ${DB__USERNAME}
      DB__PASSWORD: ${DB__PASSWORD}
      APP__NAME: ${APP__NAME}
      APP__ENV: ${APP__ENV}
      APP__KEY: ${APP__KEY}
      APP__DEBUG: ${APP__DEBUG}
      APP__LOG__LEVEL: ${APP__LOG__LEVEL}
      APP__URL: ${APP__URL}
      BROADCAST__DRIVER: ${BROADCAST__DRIVER}
      CACHE__DRIVER: ${CACHE__DRIVER}
      SESSION__DRIVER: ${SESSION__DRIVER}
      SESSION__LIFETIME: ${SESSION__LIFETIME}
      QUEUE__DRIVER: ${QUEUE__DRIVER}
      QUEUE_CONNECTION: ${QUEUE_CONNECTION}
      REDIS__HOST: ${REDIS__HOST}
      REDIS__PASSWORD: ${REDIS__PASSWORD}
      REDIS__PORT: ${REDIS__PORT}
      RUN__MODE: ${RUN__MODE}
      RUN__ENVIRONMENT: ${RUN__ENVIRONMENT}
      RUN_SCRIPTS: ${RUN_SCRIPTS}
      SKIP_COMPOSER: ${SKIP_COMPOSER}
      CRONTAB__ENABLE: ${CRONTAB__ENABLE}
      QUEUE__ENABLE: ${QUEUE__ENABLE}
      SUPERVISOR__ENABLE: ${SUPERVISOR__ENABLE}
      WEBROOT: '/data/web/public'
      PHP_ERRORS_STDERR: 'true'
      CAT__AGENT__TYPE: ${CAT__AGENT__TYPE}
      CAT__AGENT__HOST: ${CAT__AGENT__HOST}
      CAT__AGENT__PORT: ${CAT__AGENT__PORT}
      MAIL__DRIVER: ${MAIL__DRIVER}
      MAIL__HOST: ${MAIL__HOST}
      MAIL__PORT: ${MAIL__PORT}
      MAIL__USERNAME: ${MAIL__USERNAME}
      MAIL__PASSWORD: ${MAIL__PASSWORD}
      MAIL__ENCRYPTION: ${MAIL__ENCRYPTION}
      MAIL__FROM__NAME: ${MAIL__FROM__NAME}
      MAIL__FROM__ADDRESS: ${MAIL__FROM__ADDRESS}
    external_links:
      - tools-mid/redis:redis
      - tools-mid/consul:consul
      - tools-mid/mysql:mysql
      - tools-mid/cat-agent-udp:cat-agent-udp
      - tools-mid/host-cat:host-cat
    cap_add:
      - SYS_PTRACE
    mem_limit: 2147483648
