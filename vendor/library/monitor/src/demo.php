<?php
/**
 * Created by PhpStorm.
 * User: yuanzhi
 * Date: 2019/7/24
 * Time: 18:07
 */

require_once '../vendor/autoload.php';

use Library\Monitor\Report;
use Library\Monitor\Falcon;
use Library\Monitor\Request;
use Psr\Http\Message\ResponseInterface;


Falcon::feishu('7014b97a9cef4e0c8b74e005978a1947', '111');
exit;
//一、falcon打点统计
//1、直接打点
function falconSet() {
    Falcon::set('tt', '123');
}
//2、falcon增量打点
function falconInc() {
    Falcon::inc('aa');
}
//3、falcon耗时统计打点、会统计p99
function falconCost()
{
    Falcon::cost('ss', 99);
}
//4、url请求耗时打点，封装cost，可统计p99、p95
function requestToFalcon()
{
    Request::requestToFalcon($requestUrl ="http://172.16.2.152:8099/v1/fuel/station/list", $costTime = 200, $httpStatusCode = 200);
}

//二、request请求通用方法
//1、post、get 常规请求，包含接口耗时统计（封装requestToFalcon方法）
function requestData()
{
    $param = ['page' => 1, 'limit' => 5];
    $headers = [
        'X-G7-Openapi-Orgcode' => '200OS7'
    ];
    $content = Request::requestData('get', 'http://172.16.2.154:8099/v1/fuel/station/list', $param, $headers, 2, 1, function (ResponseInterface $req, $result){
        //调用成功以后处理
        var_dump($result);
        //http返回状态
        $statuscode = $req->getStatusCode();
        var_dump($statuscode);
    });
    var_dump($content);

}
//2、json参数格式，包含接口耗时统计（封装requestToFalcon方法）
function requestJson()
{
    $param = ['a' => '111', 'b' => '222'];
    $headers = [
        'X-G7-Openapi-Orgcode' => '200OS7'
    ];
    $content = Request::requestJson( 'http://172.16.2.154:8099/v1/fuel/station/list', $param, $headers, 2, 1, function (ResponseInterface $req, $result){
        //http返回状态
        $statuscode = $req->getStatusCode();
        if($statuscode == 200) {
            var_dump(json_decode($result, true));
        } else {
            var_dump('请求失败');
        }
    });
    var_dump($content);
}
//三、程序监控报警
//1、程序异常监控、发钉钉
function testExceptionMonitor() {
    $token = "677c8e4110a24008c2f9538d25e6f2f17d9c262715c8e1cae5e56b8245be160d";
    //额外的报出的信息，默认为空数组，根据项目具体实际情况
    $extra = [
        '服务'   => 'FOSS',
        '环境'  => 'test'
    ];
    ini_set('display_errors', 'On');
    Report::registMonitor($extra, $token, E_NOTICE|E_WARNING|E_ERROR);
    throw new Exception('aaaa');
}

//1、发送钉钉消息
function testSendContentToDingding()
{
    $token = "677c8e4110a24008c2f9538d25e6f2f17d9c262715c8e1cae5e56b8245be160d";
    Report::sendToDingding('测试信息，无需理会', $token);
}
//2、发送异常到钉钉
function sendExcetionToDdingding() {
    $token = "a9aa42ec9f4f09291ecf27c2debbfedd62c60b2b7f802eb9bae0785306ce9e10";
    $extra = [
        '服务'   => 'FOSS',
        '环境'  => 'test'
    ];
    return Report::sendExceptionToDingding(new Exception('抛异常测试11111'), $extra, $token);
}
testSendContentToDingding();
//$res = sendExcetionToDdingding();
//var_dump($res);
