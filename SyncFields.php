<?php
/**
 * Created by PhpStorm.
 * User: kevin
 * Date: 17-3-23
 * Time: 下午6:07
 */


/**
 * 请确认入口文件中有此行。
 * 如果项目中已使用composer，则无需在入口文件中再次引用此行，否则请添加此行
 */
require_once './vendor/autoload.php';

//$data = (new \GosSDK\Gos())
//    ->setMethod(GosSDK\Defines\Methods::QUEUE_PUSH)//设置异步方式或同步接口方法
//    ->setDataType(GosSDK\Defines\DataType::CARD_VICE)//设置所需操作的数据，如卡片、分配单、充值单等
//    ->setRequestType('POST')//设置请求方式，POST或GET，默认POST，如果为POST可不设定
//    ->setAction(GosSDK\Defines\Actions::CREATE)//设置数据操作类型，如：创建、修改、删除
//    ->setParams(['vice_no' => 'bbb'])//设置接口所需传输参数，数组类型
//    ->send();//执行请求

(new \GosSDK\SyncFieldsConfig())->sync();