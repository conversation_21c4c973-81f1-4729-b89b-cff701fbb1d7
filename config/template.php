<?php

return [
    'DingDing' => [
        'stationPushFailed'             => [
            '报警主题：{title}',
            '接入平台：{platformName}',
            '站点名称：{stationName}',
            '站点ID：{stationId}',
            '日志ID：{routeId}',
            '推送失败时间：{createdAt}'
        ],
        'saleGeGunForPrice'             => [
            '* 上报模块：{title}',
            '* 状态：异常',
            '* 上报时间：{createdAt}',
            '* 详情：
                站点供应商：{platformName}
                站点名称：{stationName}
                油品信息：{oilInfo}
                枪价：{gunPrice}
                结算价：{salePrice}',
        ],
        'oilConvertFailed'              => [
            '* 上报模块：{title}',
            '* 状态：异常',
            '* 上报时间：{createdAt}',
            '* 详情：
                站点供应商：{platformName}
                站点名称：{stationName}
                油品信息：{oilInfo}',
        ],
        'pushOrderFailed'               => [
            '{platform_name}下单失败',
            '站点：{station_name}',
            '油品：{oil}',
            '商品价格：{price}',
            '数量：{oil_num}',
            '金额：{money}',
            '车牌号：{plate_number}',
            '机构代码：{org_code}',
            '加油账号：{card_no}',
            '失败原因：{reason}',
        ],
        'queryOrderFailed'              => [
            '{platform_name}异常订单',
            '异常原因：{reason}',
            'G7流水号：{self_order_id}',
            '站点：{station_name}',
            '商品：{oil}',
            '{platform_name}商品：{platform_oil}',
            'G7油机价：{mac_price}',
            '{platform_name}油机价：{platform_mac_price}',
            '数量：{oil_num}',
            '油机总额：{mac_money}',
            '{platform_name}油机总额：{platform_mac_money}',
            '车牌号：{plate_number}',
            '手机号：{driver_phone}',
        ],
        'needManualRefund'              => [
            '壳牌订单号：{platform_order_id}',
            'G7流水号：{self_order_id}',
            '退款原因：{platform_name}发起退款，需要运营审核并退款',
            '站点：{station_name}',
            '商品：{oil}',
            '单价：{price}',
            '数量：{oil_num}',
            '司机结算额：{money}',
            '车牌号：{plate_number}',
            '手机号：{driver_phone}',
        ],
        'getPayQrCodeBySupplierFailed'  => [
            '{platform_name}获取二维码失败',
            '站点：{station_name}',
            '油品：{oil}',
            '单价：{price}',
            '数量：{oil_num}',
            '结算总额：{money}',
            '车牌号：{plate_number}',
            '手机号：{driver_phone}',
            '失败原因：{reason}',
        ],
        'writtenOffFailed'              => [
            '{platform_name}订单核销失败',
            '站点：{station_name}',
            '油品：{oil}',
            '单价：{price}',
            '数量：{oil_num}',
            '结算总额：{money}',
            '车牌号：{plate_number}',
            '手机号：{driver_phone}',
            '失败原因：{reason}',
        ],
        'getSecondaryCertificateFailed' => [
            '{title}',
            '站点：{station_name}',
            '油品：{oil}',
            '商品价格：{price}',
            '数量：{oil_num}',
            '金额：{money}',
            '车牌号：{plate_number}',
            '手机号：{driver_phone}',
            '{failed_description}',
            '失败原因：{reason}'
        ],
        'stationOilRepeat'              => [
            '{platformName}站点入库失败',
            '站点：{stationName}',
            '商品：{oilName}',
            '商品价格：{price}',
            '失败原因：同一商品推送时存在不同的价格',
        ],
        'tradeDifferent'                => [
            '{platformName}加油异常订单',
            '异常原因：司机支付金额与{platformName}核销金额不一致',
            '站点：{stationName}',
            'G7商品：{oil}',
            '单价：{price}',
            '数量：{oilNum}',
            'G7结算总额：{selfMoney}',
            '{platformName}结算总额：{platformMoney}',
            '车牌号：{plateNumber}',
            '手机号：{driver_phone}',
            '操作指导：若司机支付金额大于{platformName}核销金额，可在后台给司机退款后补录。\n若司机支付金额小于{platformName}核销金额，可联系油站，让油站拆单核销。',
        ],
        'priceNotMeetRequirements'      => [
            '{platformName}站点价格不符合要求',
            '站点名称：{stationName}',
            '油品名称：{oil}',
            '发改委价：{nationalPrice}',
            '油机价：{macPrice}',
            '操作建议：三方推送的该站点发改委价和油机价不一致，请联系三方进行核对，并及时下架该站点，否则会影响我方发票数量。',
        ],
        'insufficientBalance'           => [
            '上游运营商余额不足提醒',
            '时间：{time}',
            '运营商名称：{platform_name}',
            '剩余余额：{balance}',
            '——————————————–',
            '请关注并及时充值~',
        ],
        'dsBillPushFailed'              => [
            '{platform_name}账单未成功推送提醒',
            '账单日期：{time}',
            '推送状态：{status_desc}',
            '操作说明：{tips}',
        ],
        'gdPermissionInvalid'           => [
            '高德账户异常',
            '异常代码：{code}',
            '异常说明：{msg}',
        ],
        'queueMonitoringAlarm'          => [
            '报警主题：{title}',
            '报警队列：{name}',
            '报警时间：{time}',
            '队列长度：{length}',
        ],
        'freeOrderMoney'                => [
            '{platform_name}退款未取消预授权订单',
            '异常原因：{platform_name}取消预授权接口响应异常',
            'G7订单号：{self_order_id}',
            '异常信息：{reason}',
            '处理方法：复制异常信息和订单号，要求DT技术支持',
        ],
        'pushOrderStatusFailed'         => [
            '推送订单状态到{platform_name}发生错误',
            'G7订单号：{self_order_id}',
            'G7订单当前状态：{self_order_status_desc}',
            'G7订单推送状态：{push_self_order_status_desc}',
            '平台订单号：{platform_order_id}',
            '错误信息：{reason}',
            '推送时间：{push_time}',
        ],
        'reToBePaidApproveResult' => [
            '{platform_name}补单{status}',
            '站点：{station_name}',
            'G7订单号：{self_order_id}',
            '安得单号：{platform_order_id}',
            '*油品：{oil}',
            '单价：{price}',
            '数量：{oil_num}',
            '结算总额：{money}',
            '司机电话：{driver_phone}',
            '安得补录审核备注：{reason}',
            '{platform_name}补单审核状态:{approve_status}',
            '失败原因:{failed_reason}',
        ],
        'reservationOrderException'     => [
            '{platform_name}订单问题',
            '站点：{station_name}',
            'G7预售订单号：{self_order_id}',
            '问题类型：{reason}',
            '司机机构：{org_name}',
            '油品：{oil_name}',
            '单价：{price}',
            '数量：{oil_num}',
            '结算总额：{money}',
            '车牌号：{plate_number}',
            '手机号：{driver_phone}',
        ],
        'supplierOrderFailed' => [
            '{platform_name}站点下单失败',
            'G7订单号：{self_order_id}',
            '站点：{station_name}',
            '油品：{oil_name}',
            '站点应收单价：{supplier_price}',
            '数量：{oil_num}',
            '站点应收金额：{supplier_money}',
            '车牌号：{plate_number}',
            '手机号：{driver_phone}',
            '失败原因：{reason}',
        ],
        'compareSupplierMoneyFailed' => [
            '{platform_name}订单问题',
            '站点：{station_name}',
            'G7订单号：{self_order_id}',
            '问题类型：{reason}',
            '{platform_name}扣款：{platform_supplier_money}',
            'G7站点应收金额：{supplier_money}',
            '司机机构：{org_name}',
            '油品：{oil}',
            '单价：{price}',
            '数量：{oil_num}',
            '结算总额：{money}',
            '车牌号：{plate_number}',
            '手机号：{driver_phone}',
        ],
        'oilAndGunListedPriceNotMatch' => [
            '{platform_name}{oil}商品和枪的挂牌价不一致，商品下架',
            '商品：{oil}',
            '商品挂牌价：{oil_listed_price}',
            '油枪挂牌价：{gun_listed_price}',
        ],
        'oilAndGunSalePriceNotMatch' => [
            '{platform_name}{oil}商品和枪的{platform_name}价不一致，商品下架',
            '商品：{oil}',
            '商品{platform_name}价：{oil_sale_price}',
            '油枪{platform_name}价：{gun_sale_price}',
        ],
        'gunListedPriceNoUnique' => [
            '{platform_name}{oil}不同枪挂牌价不一致，商品下架',
            '商品：{oil}',
            '油枪挂牌价：{gun_listed_price}',
        ],
        'gunSalePriceNoUnique' => [
            '{platform_name}{oil}不同枪{platform_name}价不一致，商品下架',
            '商品：{oil}',
            '油枪{platform_name}价：{gun_sale_price}',
        ],
        'outServiceInterfaceInvalid'    => [
            '请求{platform_name}{reason}',
            '请求地址：{request_url}',
            '请求耗时：{request_total_time}s',
            '错误信息：{request_error}',
            '链路ID ：{route_id}',
        ],
        'customerRefundCallback'    => [
            '{platform_name}订单审核结果',
            'G7订单号：{self_order_id}',
            '三方订单号：{platform_order_id}',
            '退款结果：{result}',
            '审核原因：{reason}',
        ],
        'supplierRefundAlarm'    => [
            '上游订单退款成功',
            '供应商单号：{supplier_order_id}',
            'G7单号：{self_order_id}',
            '客户单号：{platform_order_id}',
            '运营商：{supplier_name}',
            '油站名称：{station_name}',
            '客户机构：{org_name}',
            '单价：{price}',
            '升数：{oil_num}',
            '总额：{money}',
        ],
        'fyOrderUseTimeExceptionAlarm'    => [
            '福佑订单耗时异常',
            'G7单号：{self_order_id}',
            '客户单号：{platform_order_id}',
            '站点名称：{station_name}',
            '金额：{money}',
            '升数：{oil_num}',
            '订单时间：{receive_time}',
            '耗时时长：{use_time}',
            '接单TradeId：{order_trace_id}',
            '支付TradeId：{pay_trace_id}',
        ],
    ],
    'FeiShu'   => [
        'stationPushFailed'             => [
            '报警主题：{title}',
            '接入平台：{platformName}',
            '站点名称：{stationName}',
            '站点ID：{stationId}',
            '日志ID：{routeId}',
            '推送失败时间：{createdAt}'
        ],
        'saleGeGunForPrice'             => [
            '* 上报模块：{title}',
            '* 状态：异常',
            '* 上报时间：{createdAt}',
            '* 详情：
                站点供应商：{platformName}
                站点名称：{stationName}
                油品信息：{oilInfo}
                枪价：{gunPrice}
                结算价：{salePrice}',
        ],
        'oilConvertFailed'              => [
            '* 上报模块：{title}',
            '* 状态：异常',
            '* 上报时间：{createdAt}',
            '* 详情：
                站点供应商：{platformName}
                站点名称：{stationName}
                油品信息：{oilInfo}',
        ],
        'pushOrderFailed'               => [
            '{platform_name}下单失败',
            '站点：{station_name}',
            '油品：{oil}',
            '商品价格：{price}',
            '数量：{oil_num}',
            '金额：{money}',
            '车牌号：{plate_number}',
            '机构代码：{org_code}',
            '加油账号：{card_no}',
            '失败原因：{reason}',
        ],
        'queryOrderFailed'              => [
            '{platform_name}异常订单',
            '异常原因：{reason}',
            '站点：{station_name}',
            '商品：{oil}',
            '{platform_name}商品：{platform_oil}',
            'G7油机价：{mac_price}',
            '{platform_name}油机价：{platform_mac_price}',
            '数量：{oil_num}',
            'G7油机总额：{mac_money}',
            '{platform_name}油机总额：{platform_mac_money}',
            '车牌号：{plate_number}',
            '手机号：{driver_phone}',
        ],
        'needManualRefund'              => [
            '壳牌订单号：{platform_order_id}',
            'G7流水号：{self_order_id}',
            '退款原因：{platform_name}发起退款，需要运营审核并退款',
            '站点：{station_name}',
            '商品：{oil}',
            '单价：{price}',
            '数量：{oil_num}',
            '司机结算额：{money}',
            '车牌号：{plate_number}',
            '手机号：{driver_phone}',
        ],
        'deductionMainAccountFailed'    => [
            '{platform_name}G7主卡扣款失败',
            '站点：{station_name}',
            '油品：{oil}',
            '商品价格：{price}',
            '数量：{oil_num}',
            '金额：{money}',
            '车牌号：{plate_number}',
            '机构代码：{org_code}',
            '加油账号：{card_no}',
            '失败原因：{reason}',
        ],
        'getPayQrCodeBySupplierFailed'  => [
            '{platform_name}获取二维码失败',
            '站点：{station_name}',
            '油品：{oil}',
            '单价：{price}',
            '数量：{oil_num}',
            '结算总额：{money}',
            '车牌号：{plate_number}',
            '手机号：{driver_phone}',
            '失败原因：{reason}',
        ],
        'writtenOffFailed'              => [
            '{title}',
            '站点：{station_name}',
            '油品：{oil}',
            '单价：{price}',
            '数量：{oil_num}',
            '结算总额：{money}',
            '车牌号：{plate_number}',
            '手机号：{driver_phone}',
            '失败原因：{reason}',
        ],
        'getSecondaryCertificateFailed' => [
            '{title}',
            '站点：{station_name}',
            '油品：{oil}',
            '商品价格：{price}',
            '数量：{oil_num}',
            '金额：{money}',
            '车牌号：{plate_number}',
            '手机号：{driver_phone}',
            '{failed_description}',
            '失败原因：{reason}'
        ],
        'stationOilRepeat'              => [
            '{platformName}站点入库失败',
            '站点：{stationName}',
            '商品：{oilName}',
            '商品价格：{price}',
            '失败原因：同一商品推送时存在不同的价格',
        ],
        'tradeDifferent'                => [
            '{platformName}加油异常订单',
            '异常原因：司机支付金额与{platformName}核销金额不一致',
            '站点：{stationName}',
            'G7商品：{oil}',
            '单价：{price}',
            '数量：{oilNum}',
            'G7结算总额：{selfMoney}',
            '{platformName}结算总额：{platformMoney}',
            '车牌号：{plateNumber}',
            '手机号：{driver_phone}',
            "操作指导：若司机支付金额大于{platformName}核销金额，可在后台给司机退款后补录。\n若司机支付金额小于{platformName}核销金额，可联系油站，让油站拆单核销。",
        ],
        'priceNotMeetRequirements'      => [
            '{platformName}站点价格不符合要求',
            '站点名称：{stationName}',
            '油品名称：{oil}',
            '发改委价：{nationalPrice}',
            '油机价：{macPrice}',
            '操作建议：三方推送的该站点发改委价和油机价不一致，请联系三方进行核对，并及时下架该站点，否则会影响我方发票数量。',
        ],
        'insufficientBalance'           => [
            '上游运营商余额不足提醒',
            '时间：{time}',
            '运营商名称：{platform_name}',
            '剩余余额：{balance}',
            '——————————————–',
            '请关注并及时充值~',
        ],
        'dsBillPushFailed'              => [
            '{platform_name}账单未成功推送提醒',
            '账单日期：{time}',
            '推送状态：{status_desc}',
            '操作说明：{tips}',
        ],
        'gdPermissionInvalid'           => [
            '高德账户异常,请及时处理',
            '异常代码：{code}',
            '异常说明：{msg}',
        ],
        'queueMonitoringAlarm'          => [
            '报警主题：{title}',
            '报警队列：{name}',
            '报警时间：{time}',
            '队列长度：{length}',
        ],
        'freeOrderMoney'                => [
            '{platform_name}退款未取消预授权订单',
            '异常原因：{platform_name}取消预授权接口响应异常',
            'G7订单号：{self_order_id}',
            '异常信息：{reason}',
            '处理方法：复制异常信息和订单号，要求DT技术支持',
        ],
        'pushOrderStatusFailed'         => [
            '推送订单状态到{platform_name}发生错误',
            'G7订单号：{self_order_id}',
            'G7订单当前状态：{self_order_status_desc}',
            'G7订单推送状态：{push_self_order_status_desc}',
            '平台订单号：{platform_order_id}',
            '错误信息：{reason}',
            '推送时间：{push_time}',
        ],
        'reToBePaidApproveResult' => [
            '{platform_name}补单{status}',
            '站点：{station_name}',
            'G7订单号：{self_order_id}',
            '安得单号：{platform_order_id}',
            '*油品：{oil}',
            '单价：{price}',
            '数量：{oil_num}',
            '结算总额：{money}',
            '司机电话：{driver_phone}',
            '安得补录审核备注：{reason}',
            '{platform_name}补单审核状态:{approve_status}',
            '失败原因:{failed_reason}',
        ],
        'reservationOrderException'     => [
            '{platform_name}订单问题',
            '站点：{station_name}',
            'G7预售订单号：{self_order_id}',
            '问题类型：{reason}',
            '司机机构：{org_name}',
            '油品：{oil_name}',
            '单价：{price}',
            '数量：{oil_num}',
            '结算总额：{money}',
            '车牌号：{plate_number}',
            '手机号：{driver_phone}',
        ],
        'supplierOrderFailed' => [
            '{platform_name}站点下单失败',
            '站点：{station_name}',
            '油品：{oil_name}',
            '站点应收单价：{supplier_price}',
            '数量：{oil_num}',
            '站点应收金额：{supplier_money}',
            '车牌号：{plate_number}',
            '手机号：{driver_phone}',
            '失败原因：{reason}',
        ],
        'compareSupplierMoneyFailed' => [
            '{platform_name}订单问题',
            '站点：{station_name}',
            'G7订单号：{self_order_id}',
            '问题类型：{reason}',
            '{platform_name}扣款：{platform_supplier_money}',
            'G7站点应收金额：{supplier_money}',
            '司机机构：{org_name}',
            '油品：{oil}',
            '单价：{price}',
            '数量：{oil_num}',
            '结算总额：{money}',
            '车牌号：{plate_number}',
            '手机号：{driver_phone}',
        ],
        'oilAndGunListedPriceNotMatch' => [
            '{platform_name}{oil}商品和枪的挂牌价不一致，商品下架',
            '商品：{oil}',
            '商品挂牌价：{oil_listed_price}',
            '油枪挂牌价：{gun_listed_price}',
        ],
        'oilAndGunSalePriceNotMatch' => [
            '{platform_name}{oil}商品和枪的{platform_name}价不一致，商品下架',
            '商品：{oil}',
            '商品{platform_name}价：{oil_sale_price}',
            '油枪{platform_name}价：{gun_sale_price}',
        ],
        'gunListedPriceNoUnique' => [
            '{platform_name}{oil}不同枪挂牌价不一致，商品下架',
            '商品：{oil}',
            '油枪挂牌价：{gun_listed_price}',
        ],
        'gunSalePriceNoUnique' => [
            '{platform_name}{oil}不同枪{platform_name}价不一致，商品下架',
            '商品：{oil}',
            '油枪{platform_name}价：{gun_sale_price}',
        ],
        'outServiceInterfaceInvalid'    => [
            '请求{platform_name}{reason}',
            '请求地址：{request_url}',
            '请求耗时：{request_total_time}s',
            '错误信息：{request_error}',
            '链路ID ：{route_id}',
        ],
        'customerRefundCallback'    => [
            '{platform_name}订单审核结果',
            'G7订单号：{self_order_id}',
            '三方订单号：{platform_order_id}',
            '退款结果：{result}',
            '审核原因：{reason}',
        ],
        'supplierRefundAlarm'    => [
            '上游订单退款成功',
            '供应商单号：{supplier_order_id}',
            'G7单号：{self_order_id}',
            '客户单号：{platform_order_id}',
            '运营商：{supplier_name}',
            '油站名称：{station_name}',
            '客户机构：{org_name}',
            '单价：{price}',
            '升数：{oil_num}',
            '总额：{money}',
        ],
        'fyOrderUseTimeExceptionAlarm'    => [
            '福佑订单耗时异常',
            'G7单号：{self_order_id}',
            '客户单号：{platform_order_id}',
            '站点名称：{station_name}',
            '金额：{money}',
            '升数：{oil_num}',
            '订单时间：{receive_time}',
            '耗时时长：{use_time}',
            '接单TradeId：{order_trace_id}',
            '支付TradeId：{pay_trace_id}',
        ],
    ],
];
