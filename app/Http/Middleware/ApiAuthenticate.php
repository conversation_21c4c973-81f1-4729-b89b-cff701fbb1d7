<?php

namespace App\Http\Middleware;

use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Log\ReceiveLog as Log;
use Closure;
use Illuminate\Contracts\Auth\Factory;
use Illuminate\Http\Request;


class ApiAuthenticate
{
    /**
     * The authentication guard factory instance.
     *
     * @var Factory
     */
    protected $auth;

    /**
     * Create a new middleware instance.
     *
     * @return void
     */
    public function __construct()
    {
    }

    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        //记录请求日志
        $params = $request->all();
        Log::handle("Receive request", $params['app_key'] ?? '', [
            'requestData' => $params
        ], "info", $request->getPathInfo());

        //忽略签名验证的方法
        $methods = config('role.allowPassCheckSign.api', []);
        $route = $request->route()[1]['uses'];
        $realRoute = explode("\\", $route);
        $method = $realRoute[count($realRoute) - 1];
        $methodParse = explode("@", $method);
        define("CONTROLLER_NAME", $methodParse[0]);
        define("ACTION_NAME", $methodParse[1]);

        if (in_array($method, $methods)) {

            return $next($request);
        }

        $accessKey = $request->input("app_key", "");

        if (empty($accessKey)) {

            responseFormat(4030001, [], true);
        }

        //序列化输入参数作为check请求是否重复的key,3秒内同参数超出1次的请求直接响应相应错误描述及错误码
        $requestData = $request->all();
        unset($requestData['timestamp']); //移除请求数据中影响check重复请求的timestamp字段
        if (!in_array($method, config("role.allowPassCheckFrequency.api", []))) {

            $checkRepeatRequestKey = md5(var_export($requestData, true));
            $connection = app('redis');
            $checkRepeatRequestNum = $connection->incrby($checkRepeatRequestKey, 1);

            if ($checkRepeatRequestNum == 1) {

                $connection->expire($checkRepeatRequestKey, 3);
            } elseif ($checkRepeatRequestNum > 1) {

                responseFormat(5000020, [], true);
            }
        }

        $authInfo = AuthInfoData::getAuthInfoByAccessKey($request->input('app_key', ''));

        if (!$authInfo) {

            responseFormat(4030001, [], true);
        }

        if (array_has($params, ['timestamp', 'sign', 'data'])) {

            $checkSignData = [
                'timestamp' => $params['timestamp'],
                'data'      => $params['data'],
                'app_key'   => $params['app_key'],
            ];
            $sign = $params['sign'];
            $timestamp = strtotime($params['timestamp']);
            $currStamp = strtotime(date("Y-m-d H:i:s"));

            //请求时间20分钟有效
            if ($currStamp - $timestamp > 1200) {

                responseFormat(4030002, [], true);
            }

            unset($params['sign']);
            $makeSign = createSign($checkSignData, $authInfo['secret']);

            if ($sign != $makeSign) {

                Log::handle("签名错误", "", [
                    "requestBody" => $request->all(),
                    "authInfo"    => $authInfo,
                    "makeSign"    => $makeSign
                ], "error");
                responseFormat(4030003, [], true);
            }
        } else {

            responseFormat(4030001, [], true);
        }

        $request->merge(['auth_data' => $authInfo]);

        return $next($request);
    }
}
