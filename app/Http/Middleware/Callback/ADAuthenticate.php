<?php

namespace App\Http\Middleware\Callback;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Log\ReceiveLog as Log;
use App\Models\Data\Log\ResponseLog;
use Closure;
use Evit\PhpGmCrypto\Encryption\EvitSM4Encryption;
use Exception;
use Illuminate\Contracts\Auth\Factory;
use Illuminate\Http\Request;


class ADAuthenticate
{
    /**
     * The authentication guard factory instance.
     *
     * @var Factory
     */
    protected $auth;

    /**
     * Create a new middleware instance.
     *
     * @return void
     */
    public function __construct()
    {
    }

    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        // 记录本地请求 path
        $route = $request->route()[1]['uses'];
        $realRoute = explode("\\", $route);
        $method = $realRoute[count($realRoute) - 1];
        $methodParse = explode("@", $method);
        define("CONTROLLER_NAME", $methodParse[0]);
        define("ACTION_NAME", $methodParse[1]);
        //序列化输入参数作为check请求是否重复的key,3秒内同参数超出1次的请求直接响应相应错误描述及错误码
        $requestData = $request->all();
        unset($requestData['timestamp']); //移除请求数据中影响check重复请求的timestamp字段
        $checkRepeatRequestKey = md5(var_export($requestData, true));
        $connection = app('redis');
        $checkRepeatRequestNum = $connection->incrby($checkRepeatRequestKey, 1);
        if ($checkRepeatRequestNum == 1) {
            $connection->expire($checkRepeatRequestKey, 3);
        } elseif ($checkRepeatRequestNum > 1) {
            //增加拒绝重复请求的打点
            responseFormatForAd(5000020, [], true);
        }

        $authInfo = AuthInfoData::getAuthInfoFieldByNameAbbreviation('ad');
        if (!$authInfo) {
            ResponseLog::handle([
                "requestBody" => $request->all(),
                'msg'         => '签名错误',
                "authInfo"    => $authInfo,
            ]);
            responseFormatForAd(4030001, [], true);
        }

        Log::handle("Receive request", $authInfo['access_key'], [
            'requestData' => $request->all()
        ], "info", $request->getPathInfo());
        $params = $request->all();
        $appSecret = AuthConfigData::getAuthConfigValByName("AD_APP_SECRET");
        $dataEncKey = AuthConfigData::getAuthConfigValByName("AD_APP_DATA_ENC_KEY");
        $dataEncIv = AuthConfigData::getAuthConfigValByName("AD_APP_DATA_ENC_IV");
        $realRequestData = (new EvitSM4Encryption([
            "key" => $dataEncKey,
            "iv"  => $dataEncIv,
            "mode" => "cbc",
        ]))->decrypt(base64_decode($params['encryptData']));
        Log::handle("Receive request", $authInfo['access_key'], [
            'realRequestData' => json_decode($realRequestData, true)
        ], "info", $request->getPathInfo());
        $digest = $params['digest'] ?? '';
        $makeDigest = base64_encode(
            md5(
                urlencode(
                    $params['encryptData'] . $params['timestamp'] . $appSecret
                ),
                true
            )
        );
        if ($digest != $makeDigest) {
            ResponseLog::handle([
                "requestBody" => $request->all(),
                'msg'         => '签名错误',
                "makeSign"    => $makeDigest,
                "authInfo"    => $authInfo,
            ]);
            responseFormatForAd(4030003, [], true);
        }
        $request->merge([
            'auth_data' => $authInfo,
            'data'      => $realRequestData,
        ]);
        return $next($request);
    }
}
