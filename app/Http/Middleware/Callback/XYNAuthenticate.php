<?php

namespace App\Http\Middleware\Callback;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Log\ReceiveLog as Log;
use App\Models\Data\Log\ResponseLog;
use Closure;
use Illuminate\Contracts\Auth\Factory;
use Illuminate\Http\Request;
use Tool\RsaToJava;


class XYNAuthenticate
{
    /**
     * The authentication guard factory instance.
     *
     * @var Factory
     */
    protected $auth;

    /**
     * Create a new middleware instance.
     *
     * @return void
     */
    public function __construct()
    {
    }

    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // 记录本地请求 path
        $route = $request->route()[1]['uses'];
        $realRoute = explode("\\", $route);
        $method = $realRoute[count($realRoute) - 1];
        $methodParse = explode("@", $method);
        define("CONTROLLER_NAME", $methodParse[0]);
        define("ACTION_NAME", $methodParse[1]);
        //序列化输入参数作为check请求是否重复的key,3秒内同参数超出1次的请求直接响应相应错误描述及错误码
        $requestData = $request->all();
        unset($requestData['timestamp']); //移除请求数据中影响check重复请求的timestamp字段
        $checkRepeatRequestKey = md5(var_export($requestData, true));
        $connection = app('redis');
        $checkRepeatRequestNum = $connection->incrby($checkRepeatRequestKey, 1);
        if ($checkRepeatRequestNum == 1) {
            $connection->expire($checkRepeatRequestKey, 3);
        } elseif ($checkRepeatRequestNum > 1) {
            //增加拒绝重复请求的打点
            responseFormatForXyn(5000020, [], true);
        }

        $authInfo = AuthInfoData::getAuthInfoFieldByNameAbbreviation('xyn');
        if (!$authInfo) {
            ResponseLog::handle([
                "requestBody" => $request->all(),
                'msg'         => '签名错误',
                "authInfo"    => $authInfo,
            ]);
            responseFormatForXyn(4030001, [], true);
        }

        Log::handle("Receive request", $authInfo['access_key'], [
            'requestData' => $requestData,
            'headerInfo'  => $request->header(),
        ], "info", $request->getPathInfo());
        $secret = AuthConfigData::getAuthConfigValByName("XYN_APP_SECRET");
        $requestData = (new RsaToJava(
            '', transJavaRsaKeyToPhpOpenSSL(
            AuthConfigData::getAuthConfigValByName("XYN_APP_RSA_PRIVATE_KEY"),
            false
        ), null, function (string $str) {
            return base64_decode($str);
        }
        ))->privateDecrypt($request->post('params'));
        Log::handle("Receive request", $authInfo['access_key'], [
            'parsedRequestData' => $requestData,
        ], "info", $request->getPathInfo());
        $makeSign = strrev(md5($requestData . $secret));
        if ($request->header('sign') != $makeSign) {
            ResponseLog::handle([
                "requestBody" => $request->all(),
                'msg'         => '签名错误',
                "makeSign"    => $makeSign,
                "authInfo"    => $authInfo,
            ]);
            responseFormatForXyn(4030003, [], true);
        }
        $request->merge([
            'auth_data' => $authInfo,
            'data' => json_encode([
                'data' => json_decode($requestData, true) ?? []
            ])
        ]);
        return $next($request);
    }
}
