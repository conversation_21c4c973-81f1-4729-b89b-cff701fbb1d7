<?php
// 鲸启
namespace App\Http\Middleware\Callback;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Log\ReceiveLog as Log;
use App\Models\Data\Log\ResponseLog;
use Closure;
use Illuminate\Contracts\Auth\Factory;
use Illuminate\Http\Request;
use Request\JQ as JQRequest;


class JQAuthenticate
{
    /**
     * The authentication guard factory instance.
     *
     * @var Factory
     */
    protected $auth;

    /**
     * Create a new middleware instance.
     *
     * @return void
     */
    public function __construct()
    {
    }

    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $route = $request->route()[1]['uses'];
        $realRoute = explode("\\", $route);
        $method = $realRoute[count($realRoute) - 1];
        $methodParse = explode("@", $method);
        define("CONTROLLER_NAME", $methodParse[0]);
        define("ACTION_NAME", $methodParse[1]);
        //序列化输入参数作为check请求是否重复的key,3秒内同参数超出1次的请求直接响应相应错误描述及错误码
        $requestData = $request->all();
        unset($requestData['timestamp']); //移除请求数据中影响check重复请求的timestamp字段
        $checkRepeatRequestKey = md5(var_export($requestData, true));
        $connection = app('redis');
        $checkRepeatRequestNum = $connection->incrby($checkRepeatRequestKey, 1);
        if ($checkRepeatRequestNum == 1) {
            $connection->expire($checkRepeatRequestKey, 3);
        } elseif ($checkRepeatRequestNum > 1) {
            responseFormatForJq(5000020, [], config('error.5000020'), true);
        }

        $authInfo = AuthInfoData::getAuthInfoFieldByNameAbbreviation('jq');
        if (!$authInfo) {
            ResponseLog::handle([
                "requestBody"   => $request->all(),
                "requestHeader" => $request->header(),
                'msg'           => '签名错误',
                "authInfo"      => $authInfo,
            ]);
            responseFormatForJq('40006', [], '签名验证失败', true, 2);
        }

        Log::handle("Receive request", $authInfo['access_key'], [
            'requestData'   => $request->all(),
            "requestHeader" => $request->header(),
        ], "info", $request->getPathInfo());
        $params = $request->all();
        $sign = $params['sign'];
        unset($params['sign']);
        $makeSign = JQRequest::sign(
            $params,
            AuthConfigData::getAuthConfigValByName(
                'JQ_APP_SECRET'
            )
        );
        if ($sign != $makeSign) {
            ResponseLog::handle([
                "requestBody"   => $request->all(),
                "requestHeader" => $request->header(),
                'msg'           => '签名错误',
                "makeSign"      => $makeSign,
                "authInfo"      => $authInfo,
            ]);
            responseFormatForJq(4030003, [], config('error.4030003'), true);
        }
        $pathInfo = explode('/', $request->path());
        $routeMethod = end($pathInfo);
        if (in_array($routeMethod, [
            'reversal',
            'purchase',
        ])) {
            $request->merge(['qp_callback' => true]);
        }
        if ($routeMethod == 'refund') {
            $request->merge(['force_refund' => true]);
        }
        if ($routeMethod == 'reversal') {
            $request->merge(['not_refund' => true]);
        }
        $request->merge(['auth_data' => $authInfo]);
        return $next($request);
    }
}
