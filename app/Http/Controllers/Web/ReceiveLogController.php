<?php


namespace App\Http\Controllers\Web;


use App\Models\Data\Log\ReceiveLog as ReceiveLogData;
use App\Models\Logic\ReceiveLog\GetData as GetDataLogic;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Throwable;

class ReceiveLogController extends BasicController
{
    public $customMessages = [
        'do' => [
            'userName.required' => 4120081,
            'password.required' => 4120082,
        ],
    ];

    public $customRules = [
        'do' => [
            'userName' => 'required',
            'password' => 'required',
        ],
    ];

    public function index()
    {
        return view("receive_log.index");
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhu<PERSON><PERSON>@zhongqixing.com>
     * @since 2019-07-05 15:49
     */
    public function getData(Request $request)
    {
        $this->validateRequestParam($request);
        return (new GetDataLogic($this->requestData))->handle();
    }

    public function getCurDayCount(Request $request)
    {
        $this->validateRequestParam($request);
        $count = ReceiveLogData::getCurDayCount();
        return responseFormat(0, [
            'count' => isset($this->requestData['token']) ? $count : number_format(
                $count),
        ]);
    }
}
