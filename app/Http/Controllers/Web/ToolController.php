<?php


namespace App\Http\Controllers\Web;


use App\Models\Logic\Tool\GetBillCheckResult as GetBillCheckResultToolLogic;
use App\Models\Logic\Tool\GetDriverInfo as GetDriverInfoToolLogic;
use App\Models\Logic\Tool\PushBill as PushBillLogic;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Throwable;

class ToolController extends BasicController
{
    public $customMessages = [
        'queryDriverInfo'      => [
            'car_license.car_license'    => 4120409,
            'name_abbreviation.required' => 4120060,
        ],
        'queryBillCheckResult' => [
            'name_abbreviation.required'             => 4120060,
            'platform_order_id.required_without_all' => 4120017,
            'platform_order_id.alpha_num'            => 4120018,
            'created_at.required_without'            => 4120490,
        ],
        'pushBill_sffy'        => [
            'name_abbreviation.required'   => 4120060,
            'created_at_start.required'    => 4120506,
            'created_at_start.date_format' => 4120506,
            'created_at_end.required'      => 4120506,
            'created_at_end.date_format'   => 4120506,
        ],
        'pushBill'             => [
            'name_abbreviation.required' => 4120060,
        ],
    ];

    public $customRules = [
        'queryDriverInfo'      => [
            'car_license'       => 'car_license',
            'name_abbreviation' => 'required',
        ],
        'queryBillCheckResult' => [
            'name_abbreviation' => 'required',
            'platform_order_id' => 'required_without_all:created_at',
            'created_at'        => 'required_without:platform_order_id',
        ],
        'pushBill_sffy'        => [
            'name_abbreviation' => 'required',
            'created_at_start'  => 'required|date_format:"Y-m-d H:i:s"',
            'created_at_end'    => 'required|date_format:"Y-m-d H:i:s"',
        ],
        'pushBill'             => [
            'name_abbreviation' => 'required',
        ],
    ];

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/6/24 2:44 下午
     */
    public function queryDriverInfo(Request $request)
    {
        $this->validateRequestParam($request);
        return (new GetDriverInfoToolLogic($this->requestData))->handle();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/6/24 2:44 下午
     */
    public function queryBillCheckResult(Request $request)
    {
        $this->validateRequestParam($request);
        return (new GetBillCheckResultToolLogic($this->requestData))->handle();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/6/24 2:44 下午
     */
    public function pushBill(Request $request): JsonResponse
    {
        $this->validateRequestParam(
            $request,
            __FUNCTION__ . (!empty(
            $request->input(
                "name_abbreviation",
                ""
            )
            ) ? "_{$request->input("name_abbreviation", "")}"
                : '')
        );
        return (new PushBillLogic($this->requestData, $request->input("name_abbreviation", "")))->handle();
    }
}
