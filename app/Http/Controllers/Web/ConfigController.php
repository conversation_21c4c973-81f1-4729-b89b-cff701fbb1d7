<?php


namespace App\Http\Controllers\Web;


use App\Models\Logic\AuthConfig\Config as ConfigLogic;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Throwable;

class ConfigController extends BasicController
{
    public $customMessages = [
        'getData' => [
            'configName.alpha_dash' => 4120085,
        ],
        'create'  => [
            'configName.alpha_dash' => 4120085,
            'configName.required'   => 4120086,
            'configVal.required'    => 4120087,
        ],
        'update'  => [
            'id.required'           => 4120088,
            'id.integer'            => 4120089,
            'configVal.required'    => 4120087,
            'configName.required'   => 4120086,
            'configName.alpha_dash' => 4120085,
        ],
        'delete'  => [
            'id.required' => 4120088,
            'id.integer'  => 4120089,
        ],
    ];

    public $customRules = [
        'create'  => [
            'configName' => 'required|alpha_dash',
            'configVal'  => 'required',
        ],
        'update'  => [
            'id'         => 'required|alpha_dash',
            'configVal'  => 'required',
            'configName' => 'required|alpha_dash',
        ],
        'getData' => [
            'configName' => 'alpha_dash',
        ],
        'delete'  => [
            'id' => 'required|alpha_dash',
        ],
    ];

    public function index()
    {
        return view("auth.config.index");
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-22 19:01
     */
    public function getData(Request $request)
    {
        $this->validateRequestParam($request);
        return responseFormat(0, (new ConfigLogic($this->requestData))->getData());
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-22 19:01
     */
    public function create(Request $request)
    {
        $this->validateRequestParam($request);
        (new ConfigLogic($this->requestData))->create();
        return responseFormat();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-22 19:01
     */
    public function update(Request $request)
    {
        $this->validateRequestParam($request);
        (new ConfigLogic($this->requestData))->update();
        return responseFormat();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-22 19:01
     */
    public function delete(Request $request)
    {
        $this->validateRequestParam($request);
        (new ConfigLogic($this->requestData))->delete();
        return responseFormat();
    }
}
