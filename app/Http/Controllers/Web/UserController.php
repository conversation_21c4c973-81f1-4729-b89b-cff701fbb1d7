<?php


namespace App\Http\Controllers\Web;


use App\Models\Logic\AuthInfo\AuthInfo as AuthInfoLogic;
use App\Models\Logic\Role\Info as RoleInfoLogic;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Throwable;

class UserController extends BasicController
{
    public $customMessages = [
        'getData'      => [
            'role_code.alpha_num'     => 4120042,
            'access_key.alpha_dash'   => 4120045,
            'name_abbreviation.regex' => 4120039,
        ],
        'create'       => [
            'access_key.required'        => 4120044,
            'access_key.alpha_dash'      => 4120045,
            'secret.required'            => 4120046,
            'secret.alpha_dash'          => 4120047,
            'name_abbreviation.required' => 4120038,
            'name_abbreviation.regex'    => 4120039,
            'role.required'              => 4120040,
            'role.in'                    => 4120041,
            'role_code.alpha_num'        => 4120042,
            'role_code.required'         => 4120043,
            'card_no.numeric'            => 4120037,
        ],
        'update'       => [
            'access_key.required'        => 4120044,
            'access_key.alpha_dash'      => 4120045,
            'secret.required'            => 4120046,
            'secret.alpha_dash'          => 4120047,
            'name_abbreviation.required' => 4120038,
            'name_abbreviation.regex'    => 4120039,
            'role.required'              => 4120040,
            'role.in'                    => 4120041,
            'role_code.alpha_num'        => 4120042,
            'role_code.required'         => 4120043,
            'card_no.numeric'            => 4120037,
        ],
        'delete'       => [
            'id.required' => 4120088,
            'id.integer'  => 4120089,
        ],
        'updateSecret' => [
            'old_secret.required'   => 4120047,
            'old_secret.alpha_dash' => 4120047,
            'new_secret.required'   => 4120148,
            'new_secret.alpha_dash' => 4120148,
        ],
    ];

    public $customRules = [
        'create'       => [
            'access_key'        => 'required|alpha_dash',
            'secret'            => 'required|alpha_dash',
            'name_abbreviation' => [
                'required',
                'regex:/^[\da-zA-Z\-_\|]+$/u'
            ],
            'role'              => 'in:0,1,2,3',
            'role_code'         => 'alpha_num',
            'card_no'           => "numeric",
        ],
        'update'       => [
            'id'                => 'required|alpha_dash',
            'access_key'        => 'required|alpha_dash',
            'secret'            => 'required|alpha_dash',
            'name_abbreviation' => [
                'required',
                'regex:/^[\da-zA-Z\-_\|]+$/u'
            ],
            'role'              => 'in:0,1,2,3',
            'role_code'         => 'alpha_num',
            'card_no'           => "numeric",
        ],
        'delete'       => [
            'id' => 'required|alpha_dash',
        ],
        'getData'      => [
            'role_code'         => 'alpha_num',
            'access_key'        => 'alpha_dash',
            'name_abbreviation' => [
                'regex:/^[\da-zA-Z\-_\|]+$/u'
            ],
        ],
        'updateSecret' => [
            'old_secret' => 'required|alpha_dash',
            'new_secret' => 'required|alpha_dash',
        ],
    ];

    public function index()
    {
        return view("auth.info.index");
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-22 19:01
     */
    public function getData(Request $request)
    {
        $this->validateRequestParam($request);
        return responseFormat(0, (new AuthInfoLogic($this->requestData))->getData());
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-22 19:01
     */
    public function create(Request $request)
    {
        if ($roleValidateData = RoleInfoLogic::getValidateData()) {
            $this->customRules['create']['role'] = 'in:' . implode(',', $roleValidateData);
        }

        $this->validateRequestParam($request);
        (new AuthInfoLogic($this->requestData))->create();
        return responseFormat();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-22 19:01
     */
    public function update(Request $request)
    {
        if ($roleValidateData = RoleInfoLogic::getValidateData()) {
            $this->customRules['update']['role'] = 'in:' . implode(',', $roleValidateData);
        }

        $this->validateRequestParam($request);
        (new AuthInfoLogic($this->requestData))->update();
        return responseFormat();
    }

    public function updateSecret(Request $request)
    {
        $this->validateRequestParam($request);
        (new AuthInfoLogic($this->requestData))->updateSecret();
        return responseFormat(0, [
            'url' => getLaravelAndMachineEnv("APP__URL") . "/web/login/index",
        ]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-22 19:01
     */
    public function delete(Request $request)
    {
        $this->validateRequestParam($request);
        (new AuthInfoLogic($this->requestData))->delete();
        return responseFormat();
    }
}
