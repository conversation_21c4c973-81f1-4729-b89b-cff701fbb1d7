<?php


namespace App\Http\Controllers\Web;


use App\Models\Logic\DockingPlatform\Info as InfoLogic;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Throwable;

class DockingPlatformController extends BasicController
{
    public $customMessages = [
        'getData'                            => [
            'name_abbreviation.regex' => 4120097,
        ],
        'create'                             => [
            'name_abbreviation.regex'    => 4120097,
            'name_abbreviation.required' => 4120096,
            'platform_name.required'     => 4120094,
        ],
        'update'                             => [
            'id.required'                => 4120088,
            'id.integer'                 => 4120089,
            'platform_name.required'     => 4120094,
            'name_abbreviation.required' => 4120096,
            'name_abbreviation.regex'    => 4120097,
        ],
        'delete'                             => [
            'id.required' => 4120088,
            'id.integer'  => 4120089,
        ],
        'getPlatformNameByNameAbbreviations' => [
            'name_abbreviation.required' => 4120096,
            'name_abbreviation.array'    => 4120097,
        ],
    ];

    public $customRules = [
        'create'                             => [
            'platform_name'     => 'required',
            'name_abbreviation' => [
                'required',
                'regex:/^[\da-zA-Z\-_\|]+$/u'
            ],
        ],
        'update'                             => [
            'id'                => 'required|integer',
            'platform_name'     => 'required',
            'name_abbreviation' => [
                'required',
                'regex:/^[\da-zA-Z\-_\|]+$/u'
            ],
        ],
        'delete'                             => [
            'id' => 'required|integer',
        ],
        'getData'                            => [
            'name_abbreviation' => [
                'regex:/^[\da-zA-Z\-_\|]+$/u'
            ],
        ],
        'getPlatformNameByNameAbbreviations' => [
            'name_abbreviation' => 'required|array',
        ],
    ];

    public function index()
    {
        return view("docking_platform.index");
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-22 19:01
     */
    public function getData(Request $request)
    {
        $this->validateRequestParam($request);
        return responseFormat(0, (new InfoLogic($this->requestData))->getData());
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-22 19:01
     */
    public function create(Request $request)
    {
        $this->validateRequestParam($request);
        (new InfoLogic($this->requestData))->create();
        return responseFormat();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-22 19:01
     */
    public function update(Request $request)
    {
        $this->validateRequestParam($request);
        (new InfoLogic($this->requestData))->update();
        return responseFormat();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-24 20:52
     */
    public function delete(Request $request)
    {
        $this->validateRequestParam($request);
        (new InfoLogic($this->requestData))->delete();
        return responseFormat();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-25 15:13
     */
    public function getPlatformNameByNameAbbreviations(Request $request)
    {
        $this->validateRequestParam($request);
        (new InfoLogic($this->requestData))->getPlatformNameByNameAbbreviation();
        return responseFormat();
    }
}
