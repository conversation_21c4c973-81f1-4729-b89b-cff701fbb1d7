<?php


namespace App\Http\Controllers\Web;


use App\Models\Logic\Order\Assoc as AssocLogic;
use App\Models\Logic\Order\Refund as RefundLogic;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Throwable;

class OrderController extends BasicController
{
    public $customMessages = [
        'doRefund'                  => [
            'id.required'   => 4120088,
            'id.numeric' => 4120089,
        ],
        'retryFailedOrder'          => [
            'ids.required' => 4120088,
            'ids.array'    => 4120089,
        ],
        'getSecondaryPaymentQrCode' => [
            'id.required' => 4120088,
            'id.numeric'  => 4120089,
        ],
    ];

    public $customRules = [
        'doRefund'                  => [
            'id' => 'required|numeric',
        ],
        'retryFailedOrder'          => [
            'ids' => 'required|array',
        ],
        'getSecondaryPaymentQrCode' => [
            'id' => 'required|numeric',
        ],
    ];

    public function refund(Request $request)
    {
        return view("order.refund.index", [
            'platform_order_id' => $request->get('id', ''),
        ]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-05 15:49
     */
    public function getRefundList(Request $request)
    {
        $this->validateRequestParam($request);
        return (new RefundLogic($this->requestData))->getRefundList();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-08-01 18:44
     */
    public function doRefund(Request $request)
    {
        $this->validateRequestParam($request);
        return (new RefundLogic($this->requestData))->doRefund();
    }

    /**
     * @throws Exception
     */
    public function retryFailedOrder(Request $request): JsonResponse
    {
        $this->validateRequestParam($request);
        return (new AssocLogic($this->requestData))->retryFailedOrder();
    }

    /**
     * @param Request $request
     * @return JsonResponse|mixed
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/9/2 2:33 下午
     */
    public function getSecondaryPaymentQrCode(Request $request)
    {
        $this->validateRequestParam($request);
        return (new AssocLogic($this->requestData))->getSecondaryPaymentQrCode();
    }
}
