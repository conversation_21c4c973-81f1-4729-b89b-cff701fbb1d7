<?php

namespace App\Http\Controllers\Api;

use App\Models\Logic\AuthConfig\Config as ConfigLogic;
use App\Models\Logic\Data\Push\OilStationData;
use App\Models\Logic\Data\Push\OilStationData as OilStationDataLogic;
use App\Models\Logic\Data\Push\Push as PushLogic;
use App\Models\Logic\Station\Main as StationMainLogic;
use App\Models\Logic\StationPushCondition\Main as StationPushConditionLogic;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Throwable;

class DataController extends BasicController
{
    public $customMessages = [
        'push'                               => [
            'data.required' => 4120032,
            'data.array'    => 4120033,
        ],
        'getStationWhiteList'                => [
            'org_code.required'  => 4120401,
            'org_code.alpha_num' => 4120402,
        ],
        'getAvailableStationListForCustomer' => [
            'org_code.required'  => 4120401,
            'org_code.alpha_num' => 4120402,
        ],
        'receiveStationBlackAndWhiteList'    => [
            'list_type.required' => 4120530,
            'list_type.in'       => 4120531,
            'list.required'      => 4120532,
            'list.array'         => 4120533,
        ],
        'receiveStationPushRule'             => [
            'org_code.required'          => 4120401,
            'org_code.alpha_num'         => 4120402,
            'push_value.required'        => 4120534,
            'push_value.array'           => 4120535,
            'push_value.*.type.required' => 4120536,
            'push_value.*.type.in'       => 4120537,
            'push_value.*.value.present' => 4120538,
            'push_value.*.value.array'   => 4120539,
        ],
    ];

    public $customRules = [
        'push'                               => [
            'data' => 'required|array',
        ],
        'getStationWhiteList'                => [
            'org_code' => 'required|alpha_num',
        ],
        'getAvailableStationListForCustomer' => [
            'org_code' => 'required|alpha_num',
        ],
        'receiveStationBlackAndWhiteList'    => [
            'list_type' => 'required|in:STATION_WHITE_LIST_FOR_DOWNSTREAM,STATION_FORCE_PUSH_STOP_BY_STATION_ID',
            'list'      => 'required|array',
        ],
        'receiveStationPushRule'             => [
            'org_code'           => 'required|alpha_num',
            'push_value'         => 'required|array',
            'push_value.*.type'  => 'required|in:trade_type,station_oil_unit,pcode_list',
            'push_value.*.value' => 'present|array',
        ],
    ];

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-08-22 17:58
     */
    public function push(Request $request)
    {
        $this->validateRequestParam($request);
        $validator = Validator::make($this->requestData, [
            'message_type' => 'required|in:' . implode(',', array_keys(PushLogic::getFuncMapping())),
        ], [
            'message_type.required' => 4120030,
            'message_type.in'       => 4120031,
        ]);
        if ($validator->fails()) {
            responseFormat($validator->errors()->first(), [], true);
        }
        switch ($this->requestData["message_type"]) {
            case "OIL_STATION_DATA":
                $rules = [
                    'id'             => 'alpha_dash',
                    'station_name'   => '',
                    'lat'            => 'numeric',
                    'lng'            => 'numeric',
                    'provice_code'   => 'numeric',
                    'city_code'      => 'numeric',
                    'address'        => '',
                    'rebate_grade'   => 'in:A,B,C,D,E',
                    'business_hours' => 'string',
                    'isstop'         => 'in:1,0',
                    'price_list'     => 'array',
                    'trade_type'     => 'in:1,2,3,4,5',
                ];
                $messages = [
                    'id.required'           => 4120117,
                    'id.alpha_dash'         => 4120118,
                    'station_name.required' => 4120137,
                    'lat.required'          => 4120121,
                    'lat.numeric'           => 4120122,
                    'lng.required'          => 4120123,
                    'lng.numeric'           => 4120124,
                    'provice_code.required' => 4120125,
                    'provice_code.numeric'  => 4120126,
                    'city_code.required'    => 4120127,
                    'city_code.numeric'     => 4120128,
                    'address.required'      => 4120129,
                    'rebate_grade.in'       => 4120131,
                    'isstop.required'       => 4120133,
                    'isstop.in'             => 4120134,
                    'price_list.array'      => 4120135,
                    'trade_type.required'   => 4120410,
                    'trade_type.in'         => 4120411,
                ];
                $validator = Validator::make($this->requestData["data"], $rules, $messages);

                if ($validator->fails()) {
                    responseFormat($validator->errors()->first(), [], true);
                }
                $roleCodes = array_column(array_columns_depth($this->requestData["data"], ["orgcode"]), 'orgcode');
                $this->requestData["role_code"] = $roleCodes[0] ?? '';
                break;
            case "ONLINE_PAY_ORDER":
                $rules = [
                    'pcode' => 'required',
                ];
                $messages = [
                    'pcode.required' => 4120034,
                ];
                $validator = Validator::make($this->requestData["data"], $rules, $messages);

                if ($validator->fails()) {
                    responseFormat($validator->errors()->first(), [], true);
                }

                $roleCodes = array_column(array_columns_depth($this->requestData["data"], ["pcode"]), 'pcode');
                $this->requestData["role_code"] = $roleCodes[0] ?? '';
                break;
            case "AUTONOMOUS_ORDER":
                $rules = [
                    'pcode'                 => 'required',
                    'id'                    => 'required',
                    'drivertel'             => 'present|nullable',
                    'oil_type_id'           => 'alpha_num|nullable',
                    'oil_name_id'           => 'required|alpha_num',
                    'oil_level_id'          => 'alpha_num|nullable',
                    'station_id'            => 'required',
                    'pushExtends'           => 'required',
                    'pushExtends.price'     => 'required',
                    'pushExtends.priceGun'  => 'required',
                    'pushExtends.amountGun' => 'required',
                    'vice_no'               => 'required',
                    'trade_price'           => 'required',
                    'trade_money'           => 'required',
                    'trade_num'             => 'required',
                    'trade_place'           => 'required',
                    'trade_time'            => 'required',
                    'app_station_id'        => 'required',
                    'supplier_pay_price'    => 'required',
                    'trades_no'             => 'required',
                    'oil_name'              => 'required',
                    'supplier_money'        => 'required',
                    'supplier_price'        => 'required',
                    'org_code'              => 'required',
                ];
                $messages = [
                    'pcode.required'                 => 4120034,
                    'id.required'                    => 4120162,
                    'drivertel.present'              => 4120072,
                    'oil_type_id.alpha_num'          => 4120011,
                    'oil_type_id.nullable'           => 4120011,
                    'oil_name_id.required'           => 4120013,
                    'oil_name_id.alpha_num'          => 4120014,
                    'oil_level_id.alpha_num'         => 4120015,
                    'oil_level_id.nullable'          => 4120016,
                    'station_id.required'            => 4120117,
                    'pushExtends.required'           => 4120476,
                    'vice_no.required'               => 4120423,
                    'trade_price.required'           => 4120007,
                    'trade_money.required'           => 4120009,
                    'trade_num.required'             => 4120005,
                    'trade_place.required'           => 4120470,
                    'trade_time.required'            => 4120471,
                    'app_station_id.required'        => 4120472,
                    'supplier_pay_price.required'    => 4120473,
                    'trades_no.required'             => 4120474,
                    'truck_no.required'              => 4120403,
                    'oil_name.required'              => 4120475,
                    'pushExtends.price.required'     => 4120477,
                    'pushExtends.priceGun.required'  => 4120478,
                    'pushExtends.amountGun.required' => 4120479,
                    'supplier_price.required'        => 4120491,
                    'supplier_money.required'        => 4120492,
                    'org_code.required'              => 4120034,
                ];
                $validator = Validator::make($this->requestData["data"], $rules, $messages);
                if ($validator->fails()) {
                    responseFormat($validator->errors()->first(), [], true);
                }

                $roleCodes = array_column(array_columns_depth($this->requestData["data"], ["pcode"]), 'pcode');
                $this->requestData["role_code"] = $roleCodes[0] ?? '';
                break;
            case "PAY_LOG":
                $rules = [
                    'orgcode' => 'required',
                    'money'   => 'required|numeric',
                    'card_no' => 'required|numeric',
                ];
                $messages = [
                    'orgcode.required' => 4120034,
                    'money.required'   => 4120009,
                    'money.numeric'    => 4120010,
                    'card_no.required' => 4120062,
                    'card_no.numeric'  => 4120063,
                ];
                $validator = Validator::make($this->requestData["data"], $rules, $messages);

                if ($validator->fails()) {
                    responseFormat($validator->errors()->first(), [], true);
                }
                $roleCodes = array_column(array_columns_depth($this->requestData["data"], ["orgcode"]), 'orgcode');
                $this->requestData["role_code"] = $roleCodes[0] ?? '';
                break;
            case "PAY_SUCCESS":
                $rules = [
                    'order_id' => 'required',
                ];
                $messages = [
                    'order_id.required' => 4120162,
                ];
                $validator = Validator::make($this->requestData["data"], $rules, $messages);
                if ($validator->fails()) {
                    responseFormat($validator->errors()->first(), [], true);
                }
                break;
            case 'VERIFICATION_RESULT':
                $rules = [
                    'order_id'            => 'required',
                    'verification_status' => 'required|in:1,2',
                ];
                $messages = [
                    'order_id.required'            => 4120162,
                    'verification_status.required' => 4120519,
                    'verification_status.in'       => 4120520,
                ];
                $validator = Validator::make($this->requestData["data"], $rules, $messages);
                if ($validator->fails()) {
                    responseFormat($validator->errors()->first(), [], true);
                }
                $this->requestData['order_id'] = $this->requestData['data']['order_id'] ?? '';
                $this->requestData['verification_status'] = $this->requestData['data']['verification_status'] ?? '';
                break;
            case 'REFUND':
                $rules = [
                    'order_id'          => 'required|alpha_num',
                    'order_holder_code' => 'required|alpha_num',
                ];
                $messages = [
                    'order_id.required'           => 4120162,
                    'order_id.alpha_numeric'      => 4120162,
                    'order_holder_code.required'  => 4120526,
                    'order_holder_code.alpha_num' => 4120527,
                ];
                $validator = Validator::make($this->requestData["data"], $rules, $messages);
                if ($validator->fails()) {
                    responseFormat($validator->errors()->first(), [], true);
                }
                break;
            case 'REFUND_APPLY_AUDIT':
                $rules = [
                    'order_id'     => 'required|alpha_num',
                    'org_code'     => 'required|alpha_num',
                    'audit_status' => 'required|in:1,2',
                    'audit_reason' => 'present',
                ];
                $messages = [
                    'order_id.required'      => 4120162,
                    'order_id.alpha_numeric' => 4120162,
                    'org_code.required'      => 4120401,
                    'org_code.alpha_num'     => 4120402,
                    'audit_status.required'  => 4120542,
                    'audit_status.in'        => 4120543,
                    'audit_reason.present'   => 4120544,
                ];
                $validator = Validator::make($this->requestData["data"], $rules, $messages);
                if ($validator->fails()) {
                    responseFormat($validator->errors()->first(), [], true);
                }
                break;
            case 'ORDER_STATUS_SYNC':
                $rules = [
                    'order_id'      => 'required|alpha_num',
                    'org_code'      => 'required|alpha_num',
                    'order_status'  => 'required|numeric',
                    'supplier_code' => 'required',
                    'trade_type'    => 'required|numeric',
                ];
                $messages = [
                    'order_id.required'      => 4120162,
                    'order_id.alpha_numeric' => 4120162,
                    'org_code.required'      => 4120401,
                    'org_code.alpha_num'     => 4120402,
                    'order_status.required'  => 4120545,
                    'order_status.numeric'   => 4120546,
                    'supplier_code.required' => 4120034,
                    'trade_type.required'    => 4120547,
                    'trade_type.numeric'     => 4120548,
                ];
                $validator = Validator::make($this->requestData["data"], $rules, $messages);
                if ($validator->fails()) {
                    responseFormat($validator->errors()->first(), [], true);
                }
                break;
            case 'TRADE_RESULT':
                $rules = [
                    'org_code'            => 'required|alpha_num',
                    'tradeId'             => 'required|alpha_num',
                    'orderId'             => 'required|alpha_num',
                    'cashAmount'          => 'present|nullable|numeric',
                    'returnAmount'        => 'present|nullable|numeric',
                    'aliAmount'           => 'present|nullable',
                    'aliAccount'          => 'present|nullable',
                    'supplierAccountName' => 'present|nullable',
                    'accountBalance'      => 'present|nullable',
                ];
                $messages = [
                    'org_code.required'           => 4120401,
                    'org_code.alpha_num'          => 4120402,
                    'tradeId.required'            => 4120623,
                    'tradeId.alpha_num'           => 4120624,
                    'orderId.required'            => 4120625,
                    'orderId.alpha_num'           => 4120626,
                    'cashAmount.present'          => 4120009,
                    'cashAmount.numeric'          => 4120010,
                    'returnAmount.present'        => 4120628,
                    'returnAmount.numeric'        => 4120629,
                    'aliAmount.present'           => 4120630,
                    'aliAmount.numeric'           => 4120631,
                    'aliAccount.present'          => 4120632,
                    'supplierAccountName.present' => 4120633,
                    'accountBalance.present'      => 4120634,
                    'accountBalance.numeric'      => 4120635,
                ];
                $validator = Validator::make($this->requestData["data"], $rules, $messages);
                if ($validator->fails()) {
                    responseFormat($validator->errors()->first(), [], true);
                }
                break;
            case 'TRADE_REFUND':
                $rules = [
                    'org_code'          => 'required|alpha_num',
                    'tradeId'           => 'required|alpha_num',
                    'orderId'           => 'required|alpha_num',
                    'refundOrderNumber' => 'required',
                ];
                $messages = [
                    'org_code.required'          => 4120401,
                    'org_code.alpha_num'         => 4120402,
                    'tradeId.required'           => 4120623,
                    'tradeId.alpha_num'          => 4120624,
                    'orderId.required'           => 4120625,
                    'orderId.alpha_num'          => 4120626,
                    'refundOrderNumber.required' => 4120549,
                ];
                $validator = Validator::make($this->requestData["data"], $rules, $messages);
                if ($validator->fails()) {
                    responseFormat($validator->errors()->first(), [], true);
                }
                break;
            case 'ACCOUNT_CHANGE':
                $rules = [
                    'id'            => 'required|alpha_num',
                    'org_code'      => 'required|alpha_num',
                    'change_type'   => 'required|numeric',
                    'cash_amount'   => 'required|numeric',
                    'fanli_amount'  => 'required|numeric',
                    'cash_balance'  => 'required|numeric',
                    'fanli_balance' => 'required|numeric',
                    'created_at'    => 'required|numeric',
                ];
                $messages = [
                    'org_code.required'      => 4120401,
                    'org_code.alpha_num'     => 4120402,
                    'change_type.required'   => 4120611,
                    'change_type.numeric'    => 4120612,
                    'cash_amount.required'   => 4120613,
                    'cash_amount.numeric'    => 4120614,
                    'fanli_amount.required'  => 4120615,
                    'fanli_amount.numeric'   => 4120616,
                    'cash_balance.required'  => 4120619,
                    'cash_balance.numeric'   => 4120620,
                    'fanli_balance.required' => 4120621,
                    'fanli_balance.numeric'  => 4120622,
                    'created_at.required'    => 4120617,
                    'created_at.numeric'     => 4120618,
                ];
                $validator = Validator::make($this->requestData["data"], $rules, $messages);
                if ($validator->fails()) {
                    responseFormat($validator->errors()->first(), [], true);
                }
                break;
        }

        return (new PushLogic($this->requestData))->handle();
    }

    public function getStationWhiteList(Request $request): JsonResponse
    {
        $this->validateRequestParam($request);
        return responseFormat(0, OilStationDataLogic::getStationWhiteList($this->requestData['org_code']));
    }

    public function getAvailableStationListForCustomer(Request $request): JsonResponse
    {
        $this->validateRequestParam($request);
        return StationMainLogic::getAvailableStationListForCustomer($this->requestData['org_code']);
    }

    /**
     * 获取站点计费模式列表
     * @return JsonResponse
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-09 18:28
     */
    public function getStopStationAndAvailableStationForCustomerConfig(): JsonResponse
    {
        return (new OilStationData($this->requestData))->getStopStationAndAvailableStationForCustomerConfig();
    }

    /**
     * @throws Throwable
     */
    public function receiveStationBlackAndWhiteList(Request $request): JsonResponse
    {
        $this->validateRequestParam($request);
        return (new ConfigLogic($this->requestData))->receiveStationBlackAndWhiteList();
    }

    public function receiveStationPushRule(Request $request): JsonResponse
    {
        $this->validateRequestParam($request);
        return (new StationPushConditionLogic($this->requestData))->receiveStationPushRule();
    }
}