<?php

namespace App\Http\Controllers\Api;

use App\Models\Logic\Zsh\CouponList;
use App\Models\Logic\Zsh\CouponPurchase;
use App\Models\Logic\Zsh\CouponQuery;
use App\Models\Logic\Zsh\GetPrefAmount;
use App\Models\Logic\Zsh\OrderQuery;
use App\Models\Logic\Zsh\PrePayBalanceQuery;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Throwable;


class ZshController extends BasicController
{
    public $customMessages = [
        'prePayBalanceQuery' => [
            'accountType.required' => 4120064,
            'accountType.in'       => 4120065,
        ],
        'couponPurchase'     => [
            'oUCode.required'      => 4120066,
            'oUCode.in'            => 4120067,
            'quantity.required'    => 4120068,
            'quantity.integer'     => 4120069,
            'couponCode.required'  => 4120070,
            'couponCode.alpha_num' => 4120071,
            'telephone.regex'      => 4120072,
            'telephone.required'   => 4120072,
            'orderNo.required'     => 4120073,
            'orderNo.alpha_dash'   => 4120074,
        ],
        'orderQuery'         => [
            'orderNo.required'   => 4120073,
            'orderNo.alpha_dash' => 4120074,
        ],
        'couponQuery'        => [
            'couponNo.required'   => 4120075,
            'couponNo.alpha_dash' => 4120076,
        ],
        'getPreAmount'       => [
            'year.required'  => 4120077,
            'year.regex'     => 4120078,
            'month.required' => 4120079,
            'month.regex'    => 4120080,
        ],
    ];

    public $customRules = [
        'prePayBalanceQuery' => [
            'accountType' => 'required|in:0,1',
        ],
        'couponPurchase'     => [
            'oUCode'     => 'required|in:100,100002,100024,100025,100026,100027,100028,100029,100030,100031,100032,100033,
            100034,100035,100036,100037,100038,100039,100040,100041,100042,100043',
            'quantity'   => 'required|integer',
            'couponCode' => 'required|alpha_num',
            'telephone'  => [
                'required',
                'regex:/^1[0-9]{10}$/'
            ],
            'orderNo'    => 'required|alpha_dash',
        ],
        'orderQuery'         => [
            'orderNo' => 'required|alpha_dash',
        ],
        'couponQuery'        => [
            'couponNo' => 'required|alpha_dash',
        ],
        'getPreAmount'       => [
            'year'  => [
                'required',
                'regex:/^[0-9]{4}$/'
            ],
            'month' => [
                'required',
                'regex:/^[0-9]{2}$/'
            ],
        ],
    ];

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-02 14:07
     */
    public function couponList(): JsonResponse
    {
        return (new CouponList($this->requestData))->handle();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-02 18:53
     */
    public function prePayBalanceQuery(Request $request)
    {
        $this->validateRequestParam($request);
        return (new PrePayBalanceQuery($this->requestData))->handle();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-03 11:11
     */
    public function couponPurchase(Request $request)
    {
        $this->validateRequestParam($request);
        return (new CouponPurchase($this->requestData))->handle();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-03 11:46
     */
    public function orderQuery(Request $request)
    {
        $this->validateRequestParam($request);
        return (new OrderQuery($this->requestData))->handle();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-03 14:12
     */
    public function couponQuery(Request $request)
    {
        $this->validateRequestParam($request);
        return (new CouponQuery($this->requestData))->handle();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-03 17:04
     */
    public function getPreAmount(Request $request)
    {
        $this->validateRequestParam($request);
        return (new GetPrefAmount($this->requestData))->handle();
    }
}
