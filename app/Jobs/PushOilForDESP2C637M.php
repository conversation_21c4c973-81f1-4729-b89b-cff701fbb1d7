<?php

namespace App\Jobs;

use Exception;
use Request\DESP as DESPRequest;

class PushOilForDESP2C637M extends PushOilDataBasicJob
{
    /**
     * @return array|null
     * @throws Exception
     */
    public function push(): ?array
    {
        return DESPRequest::handle(
            "open_api/v1/goods/station/syncStation",
            $this->oilData,
            explode(
                '|',
                $this->nameAbbreviation
            )[1]
        );
    }
}
