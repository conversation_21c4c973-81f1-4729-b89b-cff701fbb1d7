<?php

namespace App\Jobs;

use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\OilPriceMapping\GS as GS_OIL_PRICE_DATA;
use App\Models\Data\StationPrice as StationPriceData;
use Request\GS as GSRequest;
use Throwable;


class SendPullPriceInfoForGS extends BasicJob
{
    public $stationIds;

    /**
     * SubPullPriceInfoForCzb constructor.
     * @param array $stationIds
     */
    public function __construct(array $stationIds = [])
    {
        $this->stationIds = $stationIds;
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-15 20:03
     */
    public function handle()
    {
        $authInfoData = AuthInfoData::getAuthInfoFieldByNameAbbreviation("gs");
        $platformCode = $authInfoData['role_code'];

        try {

            $stationPriceData = GSRequest::handle("Agree_Oil_Price/Get_OilPrice_Info", [
                'point_no' => implode(',', $this->stationIds)
            ]);
            $stationPriceData['Oils_Datas'] = openssl_decrypt(base64_decode($stationPriceData['Oils_Datas']),
                'AES-128-ECB', AuthConfigData::getAuthConfigValByName("GS_APP_SIGN_KEY"),
                OPENSSL_RAW_DATA);
            $stationPriceMappingData = [];
            $stationPriceData = json_decode($stationPriceData['Oils_Datas'], true);
            Log::handle("Gs current station price data", [
                "gsCurrentStationPriceData" => $stationPriceData,
            ], '港森', 'oil_station_data', 'info');
            foreach ($stationPriceData as $value) {

                $stationPriceMappingData[$value['point_no']] = $value;
            }

            //判断拉取到的价格是否和缓存中的价格一致；
            foreach ($stationPriceMappingData as $pointNo => $v) {

                //需要推送，则拼装需要推送的参数
                $stationBasicTemp['price_list'] = GS_OIL_PRICE_DATA::getOilPriceData($v['oils_data']);
                if (empty($stationBasicTemp['price_list'])) {

                    Log::handle("The station's price of gs is empty", [
                        "stationBasicTemp"          => $stationBasicTemp,
                        "gsCurrentStationPriceData" => $v ?? [],
                    ], '港森', 'oil_station_data', 'warning');
                    continue;
                }

                $stationBasicTemp['id'] = $pointNo;
                $pushStationData = $stationBasicTemp;
                $insertStationPriceData = $stationBasicTemp;
                $insertStationPriceData['enabled_state'] = 1;
                $insertStationPriceData['station_id'] = $pointNo;
                $insertStationPriceData['platform_code'] = $platformCode;
                foreach ($insertStationPriceData['price_list'] as &$ssv) {
                    $ssv['oil_no'] = config('oil.oil_no_simple')[$ssv['oil_type']] ?? '';
                    $ssv['oil_type'] = array_flip(config('oil.oil_type'))[$ssv['oil_name']];
                    $ssv['oil_level'] = array_flip(config('oil.oil_level'))[$ssv['oil_level']] ?? '';
                }

                $insertStationPriceData['oil_price_list'] = $insertStationPriceData['price_list'] ?? [];

                //去除影响推送站端数据中心的字段
                if (!empty($pushStationData['price_list'])) {

                    foreach ($pushStationData['price_list'] as &$psv) {

                        unset($psv['sale_price'], $psv['listing_price'], $psv['issue_price'], $psv['gun_no']);
                    }
                }

                $pushStationData['assoc_id'] = $pointNo;
                $pushStationData['name_abbreviation'] = "gs";
                StationPriceData::create($insertStationPriceData);
                $this->pushPriceToStationHub($pushStationData);
            }
        } catch (Throwable $exception) {

            Log::handle("Get failed about the station's price of gs", [
                "stationIds" => $this->stationIds,
                'exception'  => json_decode(Log::getMessage([
                    'exception' => $exception,
                ]), true)['exception'],
            ], '港森', 'oil_station_data', 'error');
        }
    }
}
