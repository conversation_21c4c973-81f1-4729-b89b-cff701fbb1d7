<?php

namespace App\Jobs;

use App\Console\Commands\PullOilForCzb;
use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\OilPriceMapping\CZB as CZBOILPRICEDATA;
use App\Models\Data\StationPrice as StationPriceData;
use Request\CZB as CZBRequest;
use Request\FOSS_STATION as FOSS_STATIONRequest;
use Throwable;
use Tool\CzbLogin;


class SubPullPriceInfoForCzb extends BasicJob
{
    public const OIL_STATION_PRICE_CACHE_CHECK = 'oil_station_price_check';
    public $stationIds;

    /**
     * SubPullPriceInfoForCzb constructor.
     * @param array $stationIds
     */
    public function __construct(array $stationIds = [])
    {
        $this->stationIds = $stationIds;
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-15 20:03
     */
    public function handle()
    {
        $oilStationCacheCheckKey = self::OIL_STATION_CACHE_CHECK . "_czb";
        $oilStationPriceCacheCheckKey = self::OIL_STATION_PRICE_CACHE_CHECK . "_czb";
        $phone = randomPhone();
        CzbLogin::handle($phone, false);
        $cacheStationData = [];
        $gasId = '';
        $authInfoData = convertListToDict(AuthInfoData::getAuthInfoByWhere([
            [
                'field'    => 'name_abbreviation',
                'operator' => 'in',
                'value'    => PullOilForCzb::getNameAbbreviations(),
            ],
        ], [
            'access_key',
            'name_abbreviation',
            'role_code'
        ], false), "name_abbreviation");
        $stopStations = [];

        try {

            $cacheOilPriceTemp = app('redis')->hmget($oilStationPriceCacheCheckKey,
                $this->stationIds);
            $realCacheOilPriceTemp = [];

            foreach ($this->stationIds as $k => $v) {

                $realCacheOilPriceTemp[$v] = json_decode($cacheOilPriceTemp[$k], true);
            }

            $stationPriceData = CZBRequest::handle("gas/queryPriceByPhone", [
                'phone'        => $phone,
                'gasIds'       => implode(',', $this->stationIds),
                'platformType' => AuthConfigData::getAuthConfigValByName("CZB_PLATFORM_NO")
            ]);
            $stationPriceMappingData = convertListToDict($stationPriceData['result'], 'gasId');
            $stationBasicData = app('redis')->hmget($oilStationCacheCheckKey, $this->stationIds);

            foreach ($stationBasicData as $v) {

                try {

                    $isPush = false;
                    $stationBasicTemp = json_decode($v, true);
                    $cacheStationData = $stationBasicTemp;
                    $gasId = $stationBasicTemp['gasId'];

                    if (isset($stationPriceMappingData[$gasId])) {

                        $curPriceData = $stationPriceMappingData[$gasId];
                        $stationBasicTemp['price_list'] = CZBOILPRICEDATA::getOilPriceData(
                            $curPriceData['oilPriceList']);
                        $stationBasicTemp['is_stop'] = 0;
                        $insertStationPriceData['enabled_state'] = 1;
                    }

                    $stationBasicTemp['isCheck'] = false;
                    $stationBasicTemp['assoc_id'] = $gasId;
                    $realNameAbbreviation = !empty($stationBasicTemp['real_name_abbreviation']) ?
                        $stationBasicTemp['real_name_abbreviation'] : "czb";
                    $stationBasicTemp['name_abbreviation'] = $realNameAbbreviation;
                    $insertStationPriceData = $stationBasicTemp;
                    $insertStationPriceData['station_id'] = $gasId;
                    $insertStationPriceData['platform_code'] = $authInfoData[$realNameAbbreviation]['role_code'];

                    if (isset($stationBasicTemp['isPush']) and $stationBasicTemp['isPush']) {

                        $isPush = true;
                    }

                    if (!array_has($realCacheOilPriceTemp, [$gasId]) or (array_has($realCacheOilPriceTemp, [$gasId]) and
                            is_null($realCacheOilPriceTemp[$gasId])) or $stationBasicTemp['price_list'] !=
                        $realCacheOilPriceTemp[$gasId] ?? []) {

                        $isPush = true;
                    }

                    if (!empty($stationBasicTemp['price_list'])) {

                        foreach ($insertStationPriceData['price_list'] as &$ssv) {

                            if (!checkIsAssocArray($ssv)) {

                                continue;
                            }

                            $ssv['oil_no'] = str_replace(array_flip(config('oil.oil_type'))[$ssv['oil_name']],
                                "", config('oil.oil_no_simple')[$ssv['oil_type']] ?? '');
                            $ssv['oil_type'] = array_flip(config('oil.oil_type'))[$ssv['oil_name']];
                            $ssv['oil_level'] = array_flip(config('oil.oil_level'))[$ssv['oil_level']] ??
                                '';

                            if (isset($ssv['gun_no'])) {

                                $ssv['gun_no'] = implode(',', $ssv['gun_no']);
                            } else {

                                $ssv['gun_no'] = '';
                            }
                        }

                        $insertStationPriceData['oil_price_list'] = $insertStationPriceData['price_list'];
                    } else {

                        if ($isPush) {

                            $stopStations[] = $cacheStationData;
                            Log::handle("The station's price of czb is empty", [
                                "stationBasicTemp"           => $stationBasicTemp,
                                "czbCurrentStationPriceData" => $stationPriceMappingData[$gasId] ?? [],
                            ], '车主邦', 'oil_station_data', 'warning');
                            continue;
                        }
                    }

                    if ($isPush) {

                        unset($stationBasicTemp['isPush'], $stationBasicTemp['gasId'],
                            $stationBasicTemp['real_name_abbreviation']);
                        StationPriceData::create($insertStationPriceData);
                        $pushStationData = $stationBasicTemp;

                        //去除影响推送站端数据中心的字段
                        if (!empty($pushStationData['price_list'])) {

                            foreach ($pushStationData['price_list'] as &$psv) {

                                unset($psv['sale_price'], $psv['listing_price'], $psv['issue_price'], $psv['gun_no']);
                            }
                        }

                        $pushStationData['clearGunCache'] = true;
                        $pushStationData['gunCacheKeyPrefix'] = "gun_number_";
                        $this->pushStationToStationHub($pushStationData);
                        app('redis')->hset($oilStationPriceCacheCheckKey, $gasId,
                            json_encode($stationBasicTemp['price_list']));
                        unset($cacheStationData['isPush']);
                        app('redis')->hset($oilStationCacheCheckKey, $gasId,
                            json_encode($cacheStationData));
                    }
                } catch (Throwable $exception) {

                    $cacheStationData['isPush'] = true;
                    app('redis')->hset($oilStationCacheCheckKey, $gasId,
                        json_encode($cacheStationData));
                    Log::handle("Get failed about the station's price of czb", [
                        "gasId"     => $gasId,
                        'exception' => json_decode(Log::getMessage([
                            'exception' => $exception,
                        ]), true)['exception'],
                    ], '车主邦', 'oil_station_data', 'error');
                }
            }
        } catch (Throwable $exception) {

            Log::handle("Get failed about the station's price of czb", [
                "stationIds" => $this->stationIds,
                'exception'  => json_decode(Log::getMessage([
                    'exception' => $exception,
                ]), true)['exception'],
            ], '车主邦', 'oil_station_data', 'error');
        }

        if (!empty($stopStations)) {

            try {

                foreach ($stopStations as &$sv) {

                    $sv['app_station_id'] = $sv['id'];
                    $realNameAbbreviation = !empty($sv['real_name_abbreviation']) ?
                        $sv['real_name_abbreviation'] : "czb";
                    $sv['pcode'] = $authInfoData[$realNameAbbreviation]['role_code'];
                }

                $pCodeAuthInfoMapping = convertListToDict($authInfoData, 'role_code');
                $stopStationData = array_columns($stopStations, ['app_station_id', 'pcode']);
                $stopStationDataForSupplierCode = [];
                foreach ($stopStationData as $v) {

                    if (!isset($stopStationDataForSupplierCode[$v['pcode']])) {

                        $stopStationDataForSupplierCode[$v['pcode']] = [];
                    }
                    $stopStationDataForSupplierCode[$v['pcode']][] = $v;
                }
                foreach ($stopStationDataForSupplierCode as $k => $v) {

                    $total = count($v);
                    $totalPage = ceil($total / 50);
                    $authInfo = $pCodeAuthInfoMapping[$k];
                    for ($p = 0; $p < $totalPage; $p++) {

                        $currentData = array_slice($v, $p * 50, 50);

                        try {

                            FOSS_STATIONRequest::handle("v1/station/batchStop", [
                                "id_list" => $currentData,
                                "app_key" => $authInfo['access_key'],
                            ]);
                        } catch (Throwable $exception) {

                            Log::handle("Stop failed about the station's price of czb", [
                                "data"      => $currentData,
                                'exception' => json_decode(Log::getMessage([
                                    'exception' => $exception,
                                ]), true)['exception'],
                            ], '车主邦', 'oil_station_data', 'error');
                        }
                    }
                }

                StationPriceData::updateByWhere([
                    [
                        'field'    => "station_id",
                        'operator' => "in",
                        'value'    => array_column($stopStations, 'gasId'),
                    ],
                    [
                        'field'    => "platform_code",
                        'operator' => "in",
                        'value'    => array_column($authInfoData, 'role_code'),
                    ],
                ], [
                    'enabled_state' => 2,
                ]);

                foreach ($stopStations as $stv) {

                    app('redis')->hset($oilStationPriceCacheCheckKey, $stv['gasId'], json_encode([]));
                    unset($stv['isPush'], $stv['app_station_id'], $stv['pcode']);
                    app('redis')->hset($oilStationCacheCheckKey, $stv['gasId'],
                        json_encode($stv));
                }
            } catch (Throwable $exception) {

                Log::handle("Stop failed about the station's price of czb", [
                    "stationIds"   => $this->stationIds,
                    "stopStations" => $stopStations,
                    'exception'    => json_decode(Log::getMessage([
                        'exception' => $exception,
                    ]), true)['exception'],
                ], '车主邦', 'oil_station_data', 'error');
            }
        }
    }
}
