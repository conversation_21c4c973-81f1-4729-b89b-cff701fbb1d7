<?php

namespace App\Jobs;

use Exception;
use Request\MB as MBRequest;

class PushOilForMB extends PushOilDataBasicJob
{
    protected $stationCacheEncrypt = false;

    /**
     * @return array|null
     * @throws Exception
     */
    public function push(): ?array
    {
        return MBRequest::handle("/api/gas-station/sync-info", [
            'stationInfos' => json_encode($this->oilData, 320),
        ]);
    }
}
