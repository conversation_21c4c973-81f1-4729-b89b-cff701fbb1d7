<?php

namespace App\Jobs;

use App\Models\Dao\AuthInfo as AuthInfoDao;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\OrderAssoc as OrderAssocData;
use Exception;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Throwable;

class PushTradeToGas extends BasicJob
{
    public const ACQUIRE_CARD_LOCK_KEY_PREFIX = 'acquire_card_lock_';
    public const ACQUIRE_CARD_LOCK_DURATION   = 20;
    public $taskData;
    public $orderAssocData;
    public $authData;

    /**
     * PushTradeToGas constructor.
     * @param array $taskData
     * @param array $orderAssocData
     * @param AuthInfoDao $authData
     * @throws Exception
     */
    public function __construct(array $taskData, array $orderAssocData, AuthInfoDao $authData)
    {
        if (empty($authData->card_no)) {

            throw new Exception("认证信息有误");
        }
        $this->taskData = $taskData;
        $this->orderAssocData = $orderAssocData;
        $this->authData = $authData;
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/9/25 2:41 下午
     */
    public function handle()
    {
        $deductCardNo = $this->authData->card_no;
        $acquireCardLockKey = self::ACQUIRE_CARD_LOCK_KEY_PREFIX . $deductCardNo;
        $redisConn = app('redis');

        loop:

        if ($redisConn->set($acquireCardLockKey, $this->taskData['order_id'], 'ex',
            self::ACQUIRE_CARD_LOCK_DURATION, 'nx')) {

            $realNameAbbreviation = $this->authData->name_abbreviation;
            $nameAbbreviations = explode('_', $this->authData->name_abbreviation);
            if (count($nameAbbreviations) > 1) {

                $realNameAbbreviation = $nameAbbreviations[0];
            }
            $this->authData = AuthInfoData::getAuthInfoFieldByNameAbbreviation($realNameAbbreviation,
                '*', true);
            $platformName = DockingPlatformInfoData::getFieldsByNameAbbreviation($realNameAbbreviation)['platform_name'];

            try {

                Log::handle("PushTradeToGasFor task starts processing", [
                    "data" => $this->taskData,
                ], $platformName, "refueling_statement", "info");
                $orderData = FOSS_ORDERRequest::handle('/api/oil_adapter/makeOrder', [
                    'oil_level'      => $this->taskData['oilLevelId'],
                    'oil_name'       => $this->taskData['oilNameId'],
                    'oil_type'       => $this->taskData['oilTypeId'],
                    'card_no'        => $deductCardNo,
                    'driver_phone'   => $this->taskData['driver_phone'] ?? '',
                    'truck_no'       => $this->taskData['truck_no'] ?? '',
                    'oil_money'      => $this->taskData['money'],
                    'pay_money'      => $this->taskData['money'],
                    'third_order_id' => $this->taskData['order_id'],
                    'oil_num'        => $this->taskData['oil_num'],
                    'oil_price'      => $this->taskData['price'],
                    'station_id'     => $this->taskData['station_id'],
                    'oil_time'       => $this->taskData['oil_time'],
                    'oil_unit'       => 2,
                    'trade_mode'     => 12,
                    'driver_source'  => 2,
                ]);
                if (!array_has($orderData, ["code", "msg", "data"])) {

                    throw new Exception("", 5000001);
                }
                if ($orderData['code'] != 0 or empty($orderData['data'])) {

                    throw new Exception($orderData['msg'], 5000009);
                }

                $updateData = [
                    'self_order_status' => 1,
                ];
                if (isset($orderData['data']['order_id'])) {

                    $updateData['self_order_id'] = $orderData['data']['order_id'];
                }

                OrderAssocData::updateOrderInfoById($this->orderAssocData['id'], $updateData);
                releaseRedisLock($redisConn, $acquireCardLockKey, $this->taskData['order_id']);
                Log::handle("The PushTradeToGasFor task has been processed", [
                    "data" => $this->taskData,
                ], $platformName, "refueling_statement", "info");
                $this->job->delete();
            } catch (Throwable $exception) {

                releaseRedisLock($redisConn, $acquireCardLockKey, $this->taskData['order_id']);
                Log::handle("PushTradeToGasFor js task has exception", [
                    "data"      => $this->taskData,
                    "exception" => $exception
                ], $platformName, "refueling_statement", "error");
                throw $exception;
            }
        } else {

            goto loop;
        }
    }
}
