<?php

namespace App\Jobs;

use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Request\SFFY as SFFYRequest;
use Throwable;

class PushOrderCancelForSFFY extends BasicJob
{
    protected $orderInfo;

    public function __construct(array $orderInfo)
    {
        $this->orderInfo = $orderInfo;
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws Throwable
     */
    public function handle()
    {
        $orderIsDeductionFailed = FOSS_ORDERRequest::handle('/api/oil_adapter/determineOrderIsDeductionFailed', [
            'order_id' => $this->orderInfo['self_order_id'],
        ]);
        if ($orderIsDeductionFailed['data']) {
            SFFYRequest::handle("int/oilOrder/cancelOrder", [
                'orderNo' => $this->orderInfo['self_order_id'],
                'opType'  => 1,
                'source'  => 8,
            ]);
        }
    }
}
