<?php
/**
 * Created by PhpStorm.
 * User: zhanglingxiang
 * Date: 2019/1/16
 * Time: 下午3:14
 */

namespace App\Console\Commands;


use App\Models\Data\Common;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\OrderAssoc;
use Exception;
use Illuminate\Console\Command;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Request\ZWL as ZWLRequest;
use Throwable;
use Tool\Alarm\FeiShu;

class CheckReservationOrderStatus extends Command
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'check-reservation-order-status {--nameAbbreviation=}';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'check-reservation-order-status.
                                --name_abbreviation: check which order. eg:zwl';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/3/3 10:35 下午
     */
    public function handle()
    {
        if (!$nameAbbreviation = $this->option("nameAbbreviation")) {
            throw new Exception("nameAbbreviation is invalid");
        }
        $waitCheckOrderData = OrderAssoc::getOrderInfoByWhere([
            [
                'field'    => 'platform_name',
                'operator' => '=',
                'value'    => $nameAbbreviation,
            ],
            [
                'field'    => 'platform_order_id',
                'operator' => '!=',
                'value'    => '',
            ],
            [
                'field'    => 'created_at',
                'operator' => '>=',
                'value'    => date('Y-m-d 00:00:00', strtotime('-1 day')),
            ],
            [
                'field'    => 'created_at',
                'operator' => '<=',
                'value'    => date('Y-m-d 23:59:59', strtotime('-1 day')),
            ]
        ], ['self_order_id', 'platform_order_id', 'extend'], false, true);
        $fossOrderOrderData = convertListToDict(
            FOSS_ORDERRequest::handle(
                "/api/order/getBatchOrderItem",
                [
                    "order_ids" => $waitCheckOrderData->pluck('self_order_id')->toArray(),
                ]
            )["data"] ?? [],
            "order_id"
        );
        $fossOrderReservationOrderToOrderData = convertListToDict(
            FOSS_ORDERRequest::handle(
                "/api/oil_adapter/getOrderInfoByReservationOrder",
                [
                    "reservation_order_id" => $waitCheckOrderData->pluck('self_order_id')->toArray(),
                ]
            )["data"] ?? [],
            "original_order_id"
        );
        foreach ($waitCheckOrderData as $v) {
            $zwlOrderData = ZWLRequest::handle("/order/$v->platform_order_id", [], 'get');
            // 如果中物流订单状态为取消，G7订单状态应为已退款如不是则报警提醒
            if ($zwlOrderData['result']['status'] == 'CANCEL' and $fossOrderOrderData[$v['self_order_id']]['order_status'] != 6) {
                $extend = json_decode($v->extend, true);
                (new FeiShu())->reservationOrderException([
                    'platform_name' => DockingPlatformInfoData::getPlatformNameByNameAbbreviation(
                        $nameAbbreviation
                    ),
                    'station_name'  => $extend['trade_place'],
                    'self_order_id' => $v->self_order_id,
                    'reason'        => "中物流预售单状态与G7不一致,原因:中物流状态:" . $zwlOrderData['result']['status'] . ",G7状态:" . Common::G7_ORDER_STATUS_DESC[$fossOrderOrderData[$v['self_order_id']]['order_status']],
                    'org_name'      => $extend['org_name'],
                    'oil_name'      => $extend['oil_name'],
                    'price'         => $extend['trade_price'],
                    'oil_num'       => $extend['trade_num'],
                    'plate_number'  => $extend['truck_no'],
                    'driver_phone'  => $extend['drivertel'],
                    'money'         => $extend['trade_money'],
                ]);
            }
            if ($zwlOrderData['result']['status'] == 'FINISH') {
                if (!isset($fossOrderReservationOrderToOrderData[$v['self_order_id']])) {
                    $extend = json_decode($v->extend, true);
                    (new FeiShu())->reservationOrderException([
                        'platform_name' => DockingPlatformInfoData::getPlatformNameByNameAbbreviation(
                            $nameAbbreviation
                        ),
                        'station_name'  => $extend['trade_place'],
                        'self_order_id' => $v->self_order_id,
                        'reason'        => "中物流预售单状态与G7不一致,原因:中物流状态:" . $zwlOrderData['result']['status'] . ",G7状态:" . Common::G7_ORDER_STATUS_DESC[$fossOrderOrderData[$v['self_order_id']]['order_status']],
                        'org_name'      => $extend['org_name'],
                        'oil_name'      => $extend['oil_name'],
                        'price'         => $extend['trade_price'],
                        'oil_num'       => $extend['trade_num'],
                        'plate_number'  => $extend['truck_no'],
                        'driver_phone'  => $extend['drivertel'],
                        'money'         => $extend['trade_money'],
                    ]);
                    continue;
                }
                if (bccomp(
                        $zwlOrderData['result']['final_value'],
                        $fossOrderReservationOrderToOrderData[$v['self_order_id']]['mac_amount'],
                        2
                    ) !== 0) {
                    $extend = json_decode($v->extend, true);
                    (new FeiShu())->reservationOrderException([
                        'platform_name' => DockingPlatformInfoData::getPlatformNameByNameAbbreviation(
                            $nameAbbreviation
                        ),
                        'station_name'  => $extend['trade_place'],
                        'self_order_id' => $v->self_order_id,
                        'reason'        => "中物流预售单实际加油金额与G7预售转销售订单的油机金额不一致",
                        'org_name'      => $extend['org_name'],
                        'oil_name'      => $extend['oil_name'],
                        'price'         => $extend['trade_price'],
                        'oil_num'       => $extend['trade_num'],
                        'plate_number'  => $extend['truck_no'],
                        'driver_phone'  => $extend['drivertel'],
                        'money'         => $extend['trade_money'],
                    ]);
                }
            }
        }
        return 0;
    }
}
