<?php
/**
 * Created by PhpStorm.
 * User: zhanglingxiang
 * Date: 2019/1/16
 * Time: 下午3:14
 */

namespace App\Console\Commands;


use App\Models\Data\Log\QueueLog as Log;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use PDO;
use Throwable;

class CreateStationPushRecordTable extends Command
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'push-station-record-table:create';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'create push station record table.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/3/3 10:35 下午
     */
    public function handle()
    {
        $currentCarbon = Carbon::now();
        $currentYear = $currentCarbon->yearIso;
        $currentMonth = $currentCarbon->month;
        $nextMonthCarbon = $currentCarbon->addMonth();
        $nextYear = $nextMonthCarbon->yearIso;
        $nextMonth = $nextMonthCarbon->month;
        $nextNMonthCarbon = $currentCarbon->addMonth();
        $nextNYear = $nextNMonthCarbon->yearIso;
        $nextNMonth = $nextNMonthCarbon->month;
        $connection = DB::connection("mysql");
        $tablePrefix = $connection->getTablePrefix();
        $pdo = $connection->getPdo();
        $curTable = "{$tablePrefix}station_push_record_{$currentYear}_" . getZeroPrefixNumber($currentMonth);
        $nextTable = "{$tablePrefix}station_push_record_{$nextYear}_" . getZeroPrefixNumber($nextMonth);
        $nextNTable = "{$tablePrefix}station_push_record_{$nextNYear}_" . getZeroPrefixNumber($nextNMonth);
        $this->createTable($pdo, $curTable, $nextTable);
        $this->createTable($pdo, $curTable, $nextNTable);
    }

    public static function createTable(PDO $pdo, string $oldTable, string $newTable)
    {
        Log::handle("Create $newTable table started", func_get_args(), "系统",
            "create_table", "info");
        $count = $pdo->query("select * from `information_schema`.`TABLES` where `TABLE_NAME` = '$newTable'")
                     ->rowCount();

        if ($count <= 0) {

            try {

                $pdo->exec("create table $newTable like $oldTable");
            } catch (Throwable $e) {

                Log::handle("Create $newTable table failed", func_get_args(), "系统",
                    "create_table", "error");
            }
        }

        Log::handle("Create $newTable table finished", func_get_args(), "系统",
            "create_table", "info");
    }
}
