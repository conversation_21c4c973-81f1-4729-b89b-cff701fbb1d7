<?php
/**
 * Created by PhpStorm.
 * User: zhanglingxiang
 * Date: 2019/1/16
 * Time: 下午3:14
 */

namespace App\Console\Commands;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;
use Illuminate\Console\Command;
use Request\Base as BaseRequest;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Throwable;
use Tool\Alarm\FeiShu;


class PushBillToSFFY extends Command
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'push-bill:sffy {--billDate=} {--failedStopImmediately=}';
    protected $orderStatusMapping = [
        2 => 1,
        6 => 2,
    ];

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'push bill.
                                  --billDate: the date of bill.
                                  --failedStopImmediately: push failed stop immediately.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/3/3 10:35 下午
     */
    public function handle()
    {
        $failedStopImmediately = ($this->options()['failedStopImmediately'] ?? 0) == 1;
        $billDate = $this->options()['billDate'] ?? '';
        $orgCodes = AuthInfoData::getAuthInfoByWhere([
            [
                'field'    => 'name_abbreviation',
                'operator' => 'like',
                'value'    => 'sffy%',
            ],
        ], ['role_code'], false, true)->pluck('role_code')->toArray();
        $orderData = FOSS_ORDERRequest::handle(
                "/api/oil_adapter/getOrgBillOrder", [
                "org_code"   => $orgCodes,
                "date_begin" => $billDate ?? '',
                "date_end"   => $billDate ?? '',
            ])["data"] ?? [];
        $pageTotal = ceil(count($orderData) / 500);
        for ($i = 0; $i < $pageTotal; $i++) {

            $pushData = [];
            foreach (array_slice($orderData, $i * 500, 500) as $v) {
                $pushData[] = [
                    'orderNo'       => $v['third_order_id'],
                    'thirdOrderId'  => $v['order_id'],
                    'oilNum'        => $v['oil_num'],
                    'price'         => $v['oil_price'],
                    'listedPrice'   => $v['mac_price'],
                    'basePrice'     => empty($v['ndrc_price']) ? $v['mac_price'] : $v['ndrc_price'],
                    'amount'        => $v['mac_amount'],
                    'paymentAmount' => ($this->orderStatusMapping[$v['order_status']] == 2 ? bcsub($v['oil_money'],
                        bcadd($v['oil_money'], $v['oil_money'], 2), 2) : $v['oil_money']),
                    'refuelingTime' => $v['create_time'],
                    'orderType'     => $this->orderStatusMapping[$v['order_status']],
                    'source'        => 4,
                ];
            }
            $result = [];
            Log::handle("Request sffy started", "丰油", 'pushBill', [
                'requestData' => $pushData,
            ], [], "info");
            try {

                $result = BaseRequest::curl(AuthConfigData::getAuthConfigValByName("SFFY_BILL_RECEIVE_URL"),
                    $pushData, 60, [], 'post', true, 320);
                if ($result['responseHttpCode'] != 200) {

                    throw new Exception(config("error.5000125"), 5000125);
                }
                if (!$result['success']) {

                    throw new Exception(config("error.5000125"), 5000125);
                }
            } catch (Throwable $t) {

                (new FeiShu())->dsBillPushFailed([
                    'platform_name' => DockingPlatformInfoData::getPlatformNameByNameAbbreviation("sffy"),
                    'time'          => $billDate,
                    'status_desc'   => '未成功',
                    'tips'          => '请在OA系统手动触发未成功的账单',
                ]);
                Log::handle("Request sffy has exception", "丰油", 'pushBill', [
                    'requestData' => $pushData,
                ], $result, "error");
                if ($failedStopImmediately) {

                    throw new Exception(empty($t->getMessage()) ? config("error.5000001") :
                        $t->getMessage(), empty($t->getCode()) ? 5000001 : $t->getCode());
                }
                continue;
            }
            Log::handle("Request sffy finished", "丰油", 'pushBill', [
                'requestData' => $pushData,
            ], $result, "info");
        }
    }
}
