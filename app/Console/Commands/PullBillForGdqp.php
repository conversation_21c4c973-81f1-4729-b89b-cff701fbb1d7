<?php
/**
 * Created by PhpStorm.
 * User: zhanglingxiang
 * Date: 2019/1/16
 * Time: 下午3:14
 */

namespace App\Console\Commands;

use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\OrderAssoc as OrderAssocData;
use Illuminate\Support\Facades\Mail;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use Request\GDQP as GDQPRequest;
use Throwable;

class PullBillForGdqp extends PullBill
{
    protected $nameAbbreviation = 'gdqp';

    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-bill:gdqp {--billDate=}';
    protected $name      = 'pull bill for gdqp';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull bill for gdqp';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @return void
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/4/9 5:05 下午
     */
    public function handle()
    {
        $billDate = $this->option("billDate");
        if (empty($billDate)) {
            $billDate = date("Ymd", strtotime('-1 day'));
        }

        $billFileData = GDQPRequest::handle("reconciliationinfo", 'get', [
            "date" => $billDate,
        ]);
        if (!array_has($billFileData, ['fileName', 'fileMD5'])) {
            Log::handle(
                'Bill of gdqp is empty.',
                $billFileData,
                DockingPlatformInfoData::getPlatformNameByNameAbbreviation('gdqp'),
                'pull_bill',
                'info'
            );
            return;
        }
        if (empty($billFileData['fileName']) or empty($billFileData['fileMD5'])) {
            Log::handle(
                'Bill of gdqp is empty.',
                $billFileData,
                DockingPlatformInfoData::getPlatformNameByNameAbbreviation('gdqp'),
                'pull_bill',
                'info'
            );
            return;
        }

        $billData = GDQPRequest::handle("reconciliationfile", 'get', [
            "date" => $billDate,
        ]);
        if (empty($billData['file'])) {
            Log::handle(
                'Bill of gdqp is empty.',
                $billData,
                DockingPlatformInfoData::getPlatformNameByNameAbbreviation('gdqp'),
                'pull_bill',
                'info'
            );
            return;
        }
        $realBillData = explode("\r\n", $billData['file']);
        $realBillDataTitleMapping = array_flip(explode('|', $realBillData[0]));
        $realBillData = array_slice($realBillData, 1, count($realBillData));
        $dealBillData = [];
        foreach ($realBillData as $v) {
            if (!empty($v)) {
                $billTemp = explode('|', $v);
                $dealBillData[] = [
                    'platform_order_id' => $billTemp[$realBillDataTitleMapping['壳牌网关交易跟踪号']],
                    'station_id'        => $billTemp[$realBillDataTitleMapping['油站ID']],
                    'trade_time'        => $billTemp[$realBillDataTitleMapping['交易时间']],
                    'listing_price'     => $billTemp[$realBillDataTitleMapping['油机价-应付']],
                    'money'             => $billTemp[$realBillDataTitleMapping['交易金额-应付']],
                    'pay_money'         => $billTemp[$realBillDataTitleMapping['交易金额-实付']],
                    'discount'          => $billTemp[$realBillDataTitleMapping['壳牌优惠金额']],
                ];
            }
        }

        $platformOrderIds = array_column($dealBillData, 'platform_order_id');
        $orderAssocData = convertListToDict(
            OrderAssocData::getOrderInfoByWhere([
                [
                    'field'    => 'platform_order_id',
                    'operator' => 'in',
                    'value'    => $platformOrderIds,
                ],
            ], [
                'self_order_id',
                'platform_order_id',
                'extend'
            ], false),
            'platform_order_id'
        );

        $exportData = [];
        foreach ($dealBillData as $v) {
            $extend = json_decode($orderAssocData[$v['platform_order_id']]['extend'] ?? '', true);
            $v['self_order_id'] = $extend["owner"]["trades_no"] ?? '';
            $v['self_money'] = $extend['owner']['trade_money'] ?? 0.00;
            $v['station_name'] = $extend['owner']['trade_place'] ?? '';
            $v['oil_name'] = $extend['owner']['oil_name'] ?? '';
            $exportData[] = $v;
        }

        $title = [
            '壳牌网关交易跟踪号' => 'platform_order_id',
            '油站ID'             => 'station_id',
            '油站名称'           => 'station_name',
            '油品名称'           => 'oil_name',
            '交易时间'           => 'trade_time',
            '油机价-应付'        => 'listing_price',
            '交易金额-应付'      => 'money',
            '交易金额-实付'      => 'pay_money',
            '壳牌优惠金额 '      => 'discount',
            'G7能源订单号'       => 'self_order_id',
            'G7能源司机实付金额' => 'self_money',
        ];

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $titCol = 'A';
        foreach ($title as $key => $value) {
            $sheet->getStyle($titCol . '1')->applyFromArray([
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical'   => Alignment::VERTICAL_CENTER,
                ],
            ]);
            $sheet->setCellValue($titCol . '1', $key);
            $titCol++;
        }
        $row = 2;
        foreach ($exportData as $ev) {
            $dataCol = 'A';
            foreach ($title as $value) {
                $sheet->getStyle($dataCol . $row)->applyFromArray([
                    'alignment' => [
                        'horizontal' => Alignment::HORIZONTAL_CENTER,
                        'vertical'   => Alignment::VERTICAL_CENTER,
                    ],
                ]);

                if (is_numeric($ev[$value])) {
                    if (strpos($ev[$value], '.') !== false) {
                        $sheet
                            ->getStyle($dataCol . $row)
                            ->getNumberFormat()
                            ->setFormatCode(NumberFormat::FORMAT_NUMBER_00);
                    } else {
                        $sheet
                            ->getStyle($dataCol . $row)
                            ->getNumberFormat()
                            ->setFormatCode(NumberFormat::FORMAT_NUMBER);
                    }
                }

                $sheet->getColumnDimension($dataCol)
                      ->setAutoSize(true);
                $sheet->setCellValue($dataCol . $row, $ev[$value] ?? '');
                $dataCol++;
            }

            $row++;
        }
        $filePath = resource_path(
            "/downloads/bill/" . $billDate . "广东壳牌(新)账单.xlsx"
        );
        $writer = IOFactory::createWriter($spreadsheet, "Xlsx");
        $writer->save($filePath);
        Mail::send("emails/bill/simple", [
            "billDate"     => $billDate,
            "tradeCount"   => count($exportData),
            "tradeTotal"   => array_sum(array_column($exportData, 'pay_money')),
            "platformName" => '广东壳牌(新)',
            "remark"       => "此账单为广东壳牌(新)通过接口提供，次日08:00发送。详细账单请见附件。",
        ], function ($message) use ($filePath, $billDate) {
            $message->subject($billDate . "广东壳牌(新)账单");
            $message->to(
                json_decode(
                    AuthConfigData::getAuthConfigValByName("BILL_MAIL_RECEIVE"),
                    true
                )
            );
            $message->attach($filePath);
        });
        @unlink($filePath);
    }
}
