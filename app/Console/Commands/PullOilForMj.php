<?php

namespace App\Console\Commands;


use App\Jobs\BasicJob;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\StationPrice as StationPriceData;
use Request\MJ as MJRequest;
use Throwable;
use Tool\Alarm\FeiShu;


class PullOilForMj extends PullOil
{
    protected $nameAbbreviation = 'mj';
    protected $platformCode     = '';
    protected $platformName     = '';

    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-oil:mj';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull oil for mj';

    protected $authData = [];

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->authData = AuthInfoData::getAuthInfoFieldByNameAbbreviation(
            $this->nameAbbreviation
        );
        $this->platformCode = $this->authData['role_code'] ?? '';
        $this->platformName = DockingPlatformInfoData::getFieldsByNameAbbreviation(
            $this->nameAbbreviation,
            "platform_name",
            ""
        )['platform_name'];
    }

    /**
     * @return void
     * ---------------------------------------------------
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/2/4 4:23 下午
     */
    public function handle($parameters = [])
    {
        $existsStationIds = [];
        $stationData = MJRequest::handle("/api/v1/g7/station/list", [])['decrypt_data']['rows'];
        $waitCoordinates = [];
        foreach ($stationData as $v) {
            $waitCoordinates[] = "{$v["longitude"]},{$v["latitude"]}";
        }
        $convertedCoordinates = convertOtherCoordinateToGcJ02ByGdApi($waitCoordinates);
        $waitCoordinates = [];
        foreach ($stationData as &$sv) {
            $convertedCoordinatesTemp = $convertedCoordinates["{$sv["longitude"]},{$sv["latitude"]}"] ?? [];
            $sv['lngGcj02'] = $convertedCoordinatesTemp['lng'] ?? '';
            $sv['latGcj02'] = $convertedCoordinatesTemp['lat'] ?? '';
            if (!empty($convertedCoordinatesTemp)) {
                $waitCoordinates[] = "{$sv["lngGcj02"]},{$sv["latGcj02"]}";
            }
        }
        if ($waitCoordinates) {
            $regionData = getRegionForCoordinateByGdApi($waitCoordinates);
        }
        foreach ($stationData as $v) {
            try {
                //油站数据处理
                $oilTemp = [];
                $oilTemp['price_list'] = [];
                $oilTemp['station_name'] = $v['sname'];
                $oilTemp['station_type'] = 4;
                $existsStationIds[] = $v['identifier'];
                $oilTemp['assoc_id'] = $v['identifier'];
                $oilTemp['lng'] = $v['lngGcj02'];
                $oilTemp['lat'] = $v['latGcj02'];
                if (strpos($oilTemp['lng'], '.') !== false) {
                    $dealLng = explode('.', $oilTemp['lng']);
                    $oilTemp['lng'] = $dealLng[0] . '.' . substr($dealLng[1], 0, 6);
                }
                if (strpos($oilTemp['lat'], '.') !== false) {
                    $dealLat = explode('.', $oilTemp['lat']);
                    $oilTemp['lat'] = $dealLat[0] . '.' . substr($dealLat[1], 0, 6);
                }

                $oilTemp['province_code'] = $regionData["{$v["lngGcj02"]},{$v["latGcj02"]}"]['provinceCode'] ?? '';
                $oilTemp['city_code'] = $regionData["{$v["lngGcj02"]},{$v["latGcj02"]}"]['cityCode'] ?? '';
                if (checkIsMunicipality($oilTemp['city_code'])) {
                    $oilTemp['province_code'] = $oilTemp['city_code'];
                }

                $oilTemp['address'] = $v['saddress'];
                $oilTemp['contact_phone'] = $v['tel'];
                $oilTemp['contact'] = '冼世文';
                $oilTemp['is_stop'] = (int)($v['open'] != 0);
                $oilTemp['station_oil_unit'] = 2;
                $oilTemp['allow_switch_unit'] = 0;
                $oilTemp['name_abbreviation'] = $this->nameAbbreviation;
                $oilTemp['station_brand'] = 8;
                $oilTemp['business_hours'] = '24小时';
                $oilTemp['id'] = $v['identifier'];
                $oilTemp['trade_type'] = 3;
                $insertStationPriceData = $oilTemp;
                $insertStationPriceData['enabled_state'] = $oilTemp['is_stop'] + 1;
                $insertStationPriceData['platform_code'] = $this->platformCode;
                $insertStationPriceData['station_id'] = $v['identifier'];
                //如果油站油品价格数据不存在则舍弃该数据
                if (isset($v['stationPrices']) and is_array($v['stationPrices'])) {
                    $gunNos = [];
                    $mjPrices = [];
                    $listedPrices = [];
                    foreach ($v['stationGuns'] as $sgv) {
                        if (!is_array($sgv) or !array_has($sgv, ['gasType', 'mjPrice', 'listedPrice', 'gunNo']
                            )) {
                            continue;
                        }
                        if (!isset($gunNos[$sgv['gasType']])) {
                            $gunNos[$sgv['gasType']] = [];
                        }
                        if (!isset($mjPrices[$sgv['gasType']])) {
                            $mjPrices[$sgv['gasType']] = [];
                        }
                        if (!isset($listedPrices[$sgv['gasType']])) {
                            $listedPrices[$sgv['gasType']] = [];
                        }
                        $gunNos[$sgv['gasType']][] = $sgv['gunNo'];
                        $mjPrices[$sgv['gasType']][] = $sgv['mjPrice'];
                        $mjPrices[$sgv['gasType']] = array_unique($mjPrices[$sgv['gasType']]);
                        $listedPrices[$sgv['gasType']][] = $sgv['listedPrice'];
                        $listedPrices[$sgv['gasType']] = array_unique($listedPrices[$sgv['gasType']]);
                    }
                    //油站价格数据
                    foreach ($v['stationPrices'] as $cv) {
                        if (!isset($gunNos[$cv['gasType']]) or empty($gunNos[$cv['gasType']])) {
                            Log::handle(
                                "Oil's gun is empty",
                                [
                                    "data" => $v
                                ],
                                $this->platformName,
                                "oil_station_data",
                                "error"
                            );
                            continue;
                        }
                        $realOilInfo = explode(
                            "_",
                            config("oil.oil_mapping.$this->nameAbbreviation.{$cv['gasType']}", "")
                        );
                        if (count($realOilInfo) < 2) {
                            Log::handle(
                                "Oil's attribute is invalid",
                                [
                                    "data" => $v
                                ],
                                $this->platformName,
                                "oil_station_data",
                                "error"
                            );
                            continue;
                        }
                        $oilPriceTemp = [];
                        $insertOilPriceTemp = [];
                        $oilPriceTemp['oil_type'] = $realOilInfo[1] ?? '';
                        $oilPriceTemp['oil_level'] = $realOilInfo[2] ?? '';
                        $oilPriceTemp['oil_name'] = $realOilInfo[0] ?? '';
                        if (count($listedPrices[$cv['gasType']]) > 1) {
                            (new FeiShu())->gunListedPriceNoUnique([
                                'platform_name'    => $this->platformName,
                                'oil'              => array_flip(config("oil.oil_type"))[$realOilInfo[0]] ?? '',
                                'gun_listed_price' => implode(',', $listedPrices[$cv['gasType']]),
                            ]);
                            continue;
                        }
                        if (count($mjPrices[$cv['gasType']]) > 1) {
                            (new FeiShu())->gunSalePriceNoUnique([
                                'platform_name'  => $this->platformName,
                                'oil'            => array_flip(config("oil.oil_type"))[$realOilInfo[0]] ?? '',
                                'gun_sale_price' => implode(',', $mjPrices[$cv['gasType']]),
                            ]);
                            continue;
                        }
                        if (bccomp($cv['listedPrice'], $listedPrices[$cv['gasType']][0], 2) != 0) {
                            (new FeiShu())->oilAndGunListedPriceNotMatch([
                                'platform_name'    => $this->platformName,
                                'oil'              => array_flip(config("oil.oil_type"))[$realOilInfo[0]] ?? '',
                                'oil_listed_price' => $cv['listedPrice'],
                                'gun_listed_price' => implode(',', $listedPrices[$cv['gasType']]),
                            ]);
                            continue;
                        }
                        if (bccomp($cv['mjPrice'], $mjPrices[$cv['gasType']][0], 2) != 0) {
                            (new FeiShu())->oilAndGunSalePriceNotMatch([
                                'platform_name'  => $this->platformName,
                                'oil'            => array_flip(config("oil.oil_type"))[$realOilInfo[0]] ?? '',
                                'oil_sale_price' => $cv['mjPrice'],
                                'gun_sale_price' => implode(',', $mjPrices[$cv['gasType']]),
                            ]);
                            continue;
                        }
                        $insertOilPriceTemp['oil_type'] = array_flip(config("oil.oil_type"))[$realOilInfo[0]] ?? '';
                        $insertOilPriceTemp['oil_no'] = str_replace(
                            $insertOilPriceTemp['oil_type'],
                            '',
                            array_flip(config("oil.oil_no"))[$realOilInfo[1]] ?? ''
                        );
                        $insertOilPriceTemp['oil_level'] = array_flip(config("oil.oil_level"))[$realOilInfo[2]] ?? '';
                        $insertOilPriceTemp['gun_no'] = implode(',', $gunNos[$cv['gasType']]);
                        $oilPriceTemp['price'] = $cv['mjPrice'];
                        $oilPriceTemp['mac_price'] = $cv['mjPrice'];
                        $insertOilPriceTemp['sale_price'] = bcmul(100, $cv['mjPrice']);
                        $insertOilPriceTemp['listing_price'] = bcmul(100, $cv['listedPrice']);
                        $oilTemp['price_list'][] = $oilPriceTemp;
                        $insertStationPriceData['oil_price_list'][] = $insertOilPriceTemp;
                    }
                } else {
                    Log::handle(
                        "Oil's price data doesn't exist",
                        [
                            "data" => $v
                        ],
                        $this->platformName,
                        "oil_station_data",
                        "error"
                    );
                }
                if (empty($oilTemp['price_list'])) {
                    $oilTemp['is_stop'] = 1;
                }
                $oilTemp['clearGunCache'] = true;
                $oilTemp['gunCacheKeyPrefix'] = "gun_number_";
                BasicJob::pushStationToStationHub($oilTemp);
                StationPriceData::create($insertStationPriceData);
            } catch (Throwable $exception) {
                Log::handle(
                    "Pull station failed",
                    [
                        'stationData' => $v,
                        'exception'   => json_decode(
                                             Log::getMessage([
                                                 'exception' => $exception,
                                             ]),
                                             true
                                         )['exception'],
                    ],
                    $this->platformName,
                    "oil_station_data",
                    "error"
                );
            }
        }
        $this->dealNotExistsStation($existsStationIds);
    }
}
