<?php

namespace App\Console\Commands;


use App\Jobs\BasicJob;
use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\RegionalInfo as RegionalInfoData;
use App\Models\Data\StationPrice as StationPriceData;
use Request\SG as SGRequest;
use Throwable;

class PullOilForSg extends PullOil
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-oil:sg';
    protected $name = 'pull oil for sg';
    protected $oilTypeMapping = [
        '0' => '柴油',
        '1' => '汽油',
    ];

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull oil for sg';

    protected $nameAbbreviation = 'sg';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->platformCode = AuthInfoData::getAuthInfoFieldByNameAbbreviation($this->nameAbbreviation)['role_code'] ?? '';
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/2/4 11:50 上午
     */
    public function handle()
    {
        try {

            $data = SGRequest::handle("oil.station.whole.query", [
                'partner_id' => AuthConfigData::getAuthConfigValByName("SG_PARTNER_ID")
            ]);
            $platformCode = AuthInfoData::getAuthInfoFieldByNameAbbreviation(
                "sg")['role_code'];

            Log::handle("Pull oil data by sg", [
                "data" => $data
            ], "山高", "oil_station_data", "info");

            if (!is_array($data)) {

                Log::handle("Pull oil data failed by sg", [
                    "data" => $data
                ], "山高", "oil_station_data", "error");
                return;
            }

            if (!isset($data['code'])) {

                Log::handle("Pull oil data failed by sg", [
                    "data" => $data
                ], "山高", "oil_station_data", "error");
                return;
            }

            if ($data['code'] != "000000") {

                Log::handle("Pull oil data failed by sg", [
                    "data" => $data
                ], "山高", "oil_station_data", "error");
                return;
            }

            if (!isset($data['biz_content'])) {

                Log::handle("Pull oil data failed by sg", [
                    "data" => $data
                ], "山高", "oil_station_data", "error");
                return;
            }

            if (!$data['biz_content'] = json_decode($data['biz_content'], true)) {

                Log::handle("Pull oil data failed by sg", [
                    "data" => $data
                ], "山高", "oil_station_data", "error");
                return;
            }

            $provinceData = array_column($data['biz_content']['station_list'], 'province_name');
            $cityData = array_column($data['biz_content']['station_list'], 'city_name');
            $countryData = array_column($data['biz_content']['station_list'], 'country_name');
            $provinceStr = str_replace('省', '', implode(',', $provinceData));
            $provinceStr = str_replace('市', '', $provinceStr);
            $cityStr = str_replace('市', '', implode(',', $cityData));
            $provinceData = explode(',', $provinceStr);
            $cityData = explode(',', $cityStr);
            $provinceMapping = RegionalInfoData::getCityCodeByName($provinceData, true);
            $cityMapping = RegionalInfoData::getCityCodeByName($cityData);
            $countryMapping = RegionalInfoData::getCityCodeByName($countryData);
            $coordinates = array_columns($data['biz_content']['station_list'], ['longitude', 'latitude']);
            $waitCoordinates = [];

            foreach ($coordinates as &$v) {

                if ((is_null($v['longitude']) or is_null($v['latitude'])) or
                    (empty($v['longitude']) or empty($v['latitude'])) or
                    ($v['longitude'] == 'null' or $v['latitude'] == 'null')) {

                    continue;
                }

                $v["longitude"] = trim($v["longitude"], " .");
                $v["latitude"] = trim($v["latitude"], " .");
                $waitCoordinates[] = "{$v["longitude"]},{$v["latitude"]}";
            }

            $convertedCoordinates = convertOtherCoordinateToGcJ02ByGdApi($waitCoordinates);
            $existsStationIds = [];

            unset($v);
            foreach ($data['biz_content']['station_list'] as $v) {

                try {

                    //油站数据
                    $oilTemp = [];
                    $oilTemp['price_list'] = [];
                    $oilTemp['id'] = $v['station_id'];
                    $existsStationIds[] = $oilTemp['id'];
                    $oilTemp['full_name'] = $v['full_name'];
                    $oilTemp['station_name'] = $v['station_name'];
                    $mappingProvinceName = str_replace('省', '', $v['province_name']);
                    $mappingProvinceName = str_replace('市', '', $mappingProvinceName);
                    $mappingCityName = str_replace('市', '', $v['city_name']);
                    $oilTemp['name_abbreviation'] = 'sg';

                    if (strpos($v['province_name'], "市")) {

                        $oilTemp['province_code'] = $provinceMapping[$mappingProvinceName . "1"]['city_code'] ?? '';
                        $oilTemp['city_code'] = $provinceMapping[$mappingProvinceName . "2"]['city_code'] ?? '';
                    } else {

                        $oilTemp['province_code'] = $provinceMapping[$mappingProvinceName . "2"]['city_code'] ?? '';
                        $oilTemp['city_code'] = $cityMapping[$mappingCityName]['city_code'] ?? '';
                    }

                    if (checkIsMunicipality($oilTemp['city_code'])) {

                        $oilTemp['province_code'] = $oilTemp['city_code'];
                    }

                    $oilTemp['area_code'] = $countryMapping[$v['country_name']]['city_code'] ?? '';
                    $v["longitude"] = trim($v["longitude"], " .");
                    $v["latitude"] = trim($v["latitude"], " .");
                    $convertedCoordinatesTemp = $convertedCoordinates["{$v["longitude"]},{$v["latitude"]}"] ?? [];
                    $oilTemp['lng'] = '';
                    $oilTemp['lat'] = '';

                    if (empty($oilTemp['province_code']) or empty($oilTemp['city_code'])) {

                        Log::handle("The address code of oil station doesn't exist", [
                            "data" => $v
                        ], "山高", "oil_station_data", "warning");
                        continue;
                    }

                    if (!empty($convertedCoordinatesTemp)) {

                        $oilTemp['lng'] = $convertedCoordinatesTemp['lng'] ?? '';
                        $oilTemp['lat'] = $convertedCoordinatesTemp['lat'] ?? '';
                    }

                    if (strpos($oilTemp['lng'], '.') !== false) {

                        $dealLng = explode('.', $oilTemp['lng']);
                        $oilTemp['lng'] = $dealLng[0] . '.' . substr($dealLng[1], 0, 6);
                    }

                    if (strpos($oilTemp['lat'], '.') !== false) {

                        $dealLat = explode('.', $oilTemp['lat']);
                        $oilTemp['lat'] = $dealLat[0] . '.' . substr($dealLat[1], 0, 6);
                    }

                    if (empty($oilTemp['lng']) or empty($oilTemp['lat'])) {

                        Log::handle("The Coordinates of oil station doesn't exist", [
                            "data" => $v
                        ], "山高", "oil_station_data", "warning");
                        continue;
                    }

                    $oilTemp['address'] = $v['address'];
                    $oilTemp['is_stop'] = (int)$v['status'];
                    $insertStationPriceData = $oilTemp;
                    $insertStationPriceData['station_id'] = $v['station_id'];
                    $insertStationPriceData['platform_code'] = $platformCode;
                    $insertStationPriceData['enabled_state'] = $insertStationPriceData['is_stop'] + 1;
                    $insertStationPriceData['oil_price_list'] = [];

                    //如果油站油品价格数据不存在则舍弃该数据
                    if (isset($v['station_price']) and is_array($v['station_price'])) {

                        //油站价格数据
                        foreach ($v['station_price'] as $cv) {

                            $toMappingOilNo = "{$this->oilTypeMapping[$cv['oil_type']]}{$cv['oil_no']}";

                            if (!isset(config("oil.oil_no")[$toMappingOilNo])) {

                                continue;
                            }

                            $oilPriceTemp = [];
                            $oilPriceTemp['oil_type'] = config("oil.oil_no")[$toMappingOilNo];
                            $oilPriceTemp['oil_level'] = '';
                            $oilPriceTemp['oil_name'] = config("oil.oil_type")[$this->oilTypeMapping[$cv['oil_type']]];
                            $oilPriceTemp['price'] = $cv['xl_price'];
                            $oilTemp['price_list'][] = $oilPriceTemp;
                            $insertStationPriceData['oil_price_list'][] = [
                                'sale_price'    => bcmul($cv['xl_price'], 100),
                                'oil_no'        => str_replace($this->oilTypeMapping[$cv['oil_type']], '', $toMappingOilNo),
                                'oil_type'      => $this->oilTypeMapping[$cv['oil_type']],
                                'oil_level'     => '',
                                'gun_no'        => '',
                                'listing_price' => bcmul($cv['list_price'], 100),
                            ];
                        }
                    } else {

                        Log::handle("Oil's price data doesn't exist", [
                            "data" => $v
                        ], "山高", "oil_station_data", "warning");
                    }

                    if (empty($oilTemp['price_list'])) {

                        continue;
                    }

                    BasicJob::pushStationToStationHub($oilTemp);
                    StationPriceData::create($insertStationPriceData);
                } catch (Throwable $exception) {

                    Log::handle("Push oil to stationHub has exception", [
                        'exception' => json_decode(Log::getMessage([
                            'exception' => $exception,
                        ]), true)['exception'],
                        'data'      => $v,
                    ], "山高", "oil_station_data", "error");
                }
            }

            $cacheStationIds = app('redis')->hkeys(self::OIL_STATION_CACHE_CHECK . "_sg");

            if ($delStationIds = array_diff($cacheStationIds, $existsStationIds)) {

                $delStationInfos = app('redis')->hmget(self::OIL_STATION_CACHE_CHECK . "_sg", $delStationIds);

                foreach ($delStationInfos as $v) {

                    $realStationData = json_decode($v, true);
                    $realStationData['is_stop'] = 1;
                    $realStationData['name_abbreviation'] = 'sg';
                    Log::handle("Pushing the sg to delete the petrol station", [
                        "data" => $realStationData
                    ], "山高", "oil_station_data", "info");
                    BasicJob::pushStationToStationHub($realStationData, false);
                    Log::handle("Pushing the end of sg to delete the gas station", [
                        "data" => $realStationData
                    ], "山高", "oil_station_data", "info");
                }

                app('redis')->hdel(self::OIL_STATION_CACHE_CHECK . "_sg", $delStationIds);
            }
        } catch (Throwable $exception) {

            Log::handle("Pull oil for sg has exception", [
                'exception' => json_decode(Log::getMessage([
                    'exception' => $exception,
                ]), true)['exception'],
            ], "山高", "oil_station_data", "error");
        }
    }
}
