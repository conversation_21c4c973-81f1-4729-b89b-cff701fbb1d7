<?php
/**
 * Created by PhpStorm.
 * User: zhanglingxiang
 * Date: 2019/1/16
 * Time: 下午3:14
 */

namespace App\Console\Commands;


use App\Models\Data\AuthInfo as AuthInfoData;
use Illuminate\Console\Command;
use Request\AD as ADRequest;
use Request\FOSS;
use Throwable;


class PushAccountBalanceToAD extends Command
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'push-account-balance:ad';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'push account balance to AD.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/3/3 10:35 下午
     */
    public function handle()
    {
        $creditAccountMoneyInfo = FOSS::handle(
            'oil_adapter.oil_credit_account.getCreditAccountMoneyInfoForTree',
            [
                'org_code' => AuthInfoData::getAuthInfoFieldByNameAbbreviation(
                    "ad",
                    ['role_code'],
                    true
                )->role_code,
            ]
        );
        ADRequest::handle('bop/T201904230000000014/nop/syncCreditLimit', [
            'reputationQuota' => $creditAccountMoneyInfo['data']['credit_use_limit'],
            'usedQuota' => $creditAccountMoneyInfo['data']['credit_account_used_total'],
            'availableBalance' => $creditAccountMoneyInfo['data']['credit_balance'],
        ]);
    }
}
