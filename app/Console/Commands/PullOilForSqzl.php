<?php

namespace App\Console\Commands;


use App\Jobs\BasicJob;
use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\StationPrice as StationPriceData;
use Request\SQZL as SQZLRequest;
use Throwable;


class PullOilForSqzl extends PullOil
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-oil:sqzl';
    protected $name      = 'pull oil for sqzl';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull oil for sqzl';

    protected $nameAbbreviation = 'sqzl';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->platformCode = AuthInfoData::getAuthInfoFieldByNameAbbreviation(
            $this->nameAbbreviation
        )['role_code'] ?? '';
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/2/4 11:50 上午
     */
    public function handle()
    {
        $data = SQZLRequest::handle('station/queryOilStation', [
            'companyCode' => AuthConfigData::getAuthConfigValByName('SQZL_CHANNEL_CODE'),
        ])['data'];

        $waitCoordinates = [];
        foreach ($data as $v) {
            $waitCoordinates[] = "{$v["lng"]},{$v["lat"]}";
        }
        if ($waitCoordinates) {
            $regionData = getRegionForCoordinateByGdApi($waitCoordinates);
        }

        $existsStationIds = [];
        foreach ($data as $v) {
            try {
                //油站数据
                $oilTemp = [];
                $oilTemp['price_list'] = [];
                $oilTemp['id'] = $v['stationId'];
                $oilTemp['assoc_id'] = $v['stationId'];
                $existsStationIds[] = $v['stationId'];
                $oilTemp['station_name'] = $v['stationName'];
                $oilTemp['name_abbreviation'] = $this->nameAbbreviation;
                $oilTemp['province_code'] = $regionData["{$v["lng"]},{$v["lat"]}"]['provinceCode'] ?? '';
                $oilTemp['city_code'] = $regionData["{$v["lng"]},{$v["lat"]}"]['cityCode'] ?? '';
                $oilTemp['station_type'] = 2;
                $oilTemp['trade_type'] = 3;
                $oilTemp['station_brand'] = config("brand.sqzl.{$v['type']}", 8);
                $oilTemp['station_oil_unit'] = 1;
                $oilTemp['allow_switch_unit'] = 0;
                $oilTemp['business_hours'] = $v['openTime'];
                if (checkIsMunicipality($oilTemp['city_code'])) {
                    $oilTemp['province_code'] = $oilTemp['city_code'];
                }

                $oilTemp['lng'] = $v['lng'];
                $oilTemp['lat'] = $v['lat'];
                if (strpos($oilTemp['lng'], '.') !== false) {
                    $dealLng = explode('.', $oilTemp['lng']);
                    $oilTemp['lng'] = $dealLng[0] . '.' . substr($dealLng[1], 0, 6);
                }
                if (strpos($oilTemp['lat'], '.') !== false) {
                    $dealLat = explode('.', $oilTemp['lat']);
                    $oilTemp['lat'] = $dealLat[0] . '.' . substr($dealLat[1], 0, 6);
                }

                $oilTemp['address'] = $v['address'];
                $oilTemp['is_stop'] = $v['status'] == 'offline' ? 1 : 0;
                $insertStationPriceData = $oilTemp;
                $insertStationPriceData['oil_price_list'] = [];
                $insertStationPriceData['enabled_state'] = 1;
                //如果油站油品价格数据不存在则舍弃该数据
                if (isset($v['oilGunBeans']) and is_array($v['oilGunBeans'])) {
                    //油站价格数据
                    $gunNo = 1;
                    foreach ($v['oilGunBeans'] as $cv) {
                        $realOilInfo = explode("_", config("oil.oil_mapping.sqzl.mapping.{$cv['fuelNo']}"));
                        $oilPriceTemp = [];
                        $insertOilPriceTemp = [];
                        $oilPriceTemp['oil_type'] = $realOilInfo[1] ?? "";
                        $oilPriceTemp['oil_level'] = $realOilInfo[2] ?? "";
                        $oilPriceTemp['oil_name'] = $realOilInfo[0] ?? "";
                        if (empty($oilPriceTemp['oil_name'])) {
                            Log::handle("Oil's attribute is invalid", [
                                "data" => $v
                            ], "上汽直连", "oil_station_data", "error");
                            continue;
                        }
                        $insertOilPriceTemp['oil_type'] = array_flip(
                                                              config("oil.oil_type")
                                                          )[$oilPriceTemp['oil_name']] ?? '';
                        $insertOilPriceTemp['oil_no'] = str_replace(
                            $insertOilPriceTemp['oil_type'],
                            '',
                            array_flip(
                                config("oil.oil_no")
                            )[$oilPriceTemp['oil_type']] ?? ''
                        );
                        $insertOilPriceTemp['oil_level'] = array_flip(
                                                               config("oil.oil_level")
                                                           )[$oilPriceTemp['oil_level']] ?? '';

                        $oilPriceTemp['price'] = bcsub($cv['lvPrice'], $cv['downValue'] ?? 0, 2);
                        $oilPriceTemp['mac_price'] = $oilPriceTemp['price'];
                        $insertOilPriceTemp['gun_no'] = $gunNo;
                        $insertOilPriceTemp['sale_price'] = bcmul($oilPriceTemp['price'], 100);
                        $insertOilPriceTemp['listing_price'] = bcmul($oilPriceTemp['price'], 100);
                        $oilTemp['price_list'][] = $oilPriceTemp;
                        $insertStationPriceData['oil_price_list'][] = $insertOilPriceTemp;
                        $gunNo++;
                    }

                    $insertStationPriceData['platform_code'] = $this->platformCode;
                } else {
                    Log::handle("Oil's price data doesn't exist", [
                        "data" => $v
                    ], "上汽直连", "oil_station_data", "error");
                }
                if (empty($oilTemp['price_list'])) {
                    continue;
                }

                $insertStationPriceData['station_id'] = $v['stationId'];
                $oilTemp['clearGunCache'] = true;
                $oilTemp['gunCacheKeyPrefix'] = "gun_number_";
                StationPriceData::create($insertStationPriceData);
                BasicJob::pushStationToStationHub($oilTemp);
            } catch (Throwable $throwable) {
                Log::handle("Push station failed", [
                    'exception'   => $throwable,
                    'stationData' => $v,
                ], "上汽直连", "oil_station_data", "error");
            }
        }
        $this->dealNotExistsStation($existsStationIds);
    }
}
