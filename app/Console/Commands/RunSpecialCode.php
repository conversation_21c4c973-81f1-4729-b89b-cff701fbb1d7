<?php

namespace App\Console\Commands;

use App\Jobs\BasicJob;
use App\Models\Dao\StationPushSwitch as StationPushSwitchDao;
use Illuminate\Console\Command;
use Throwable;

class RunSpecialCode extends Command
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'run-special-code';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'Run special code.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/3/3 10:35 下午
     */
    public function handle()
    {
        $redis = app('redis');
        $model = new StationPushSwitchDao();
        $model = $model
            ->leftJoin(
                "auth_info",
                "station_push_switch.name_abbreviation",
                "=",
                "auth_info.name_abbreviation"
            );
        $data = $model->get(['station_push_switch.name_abbreviation', 'auth_info.role_code'])->toArray();
        foreach ($data as $value) {
            if (in_array($value['name_abbreviation'], ['ad', 'mb', 'zj'])) {
                continue;
            }
            $data = $redis->hgetall(BasicJob::OIL_STATION_CACHE_CHECK . '_' . $value['name_abbreviation']);
            $num = ceil(count($data) / 100);
            for ($i = 0; $i < $num; $i++) {
                $updateCacheData = [];
                $pageData = array_slice($data, $i * 100, 100, true);
                foreach ($pageData as $dk => $dv) {
                    if ($dv[0] == "{") {
                        $updateCacheData[$dk] = md5($dv);
                    }
                }
                if ($updateCacheData) {
                    $redis->hmset(
                        BasicJob::OIL_STATION_CACHE_CHECK . '_' . $value['name_abbreviation'],
                        $updateCacheData
                    );
                }
            }
        }
    }
}