<?php

namespace App\Console\Commands;


use App\Jobs\BasicJob;
use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\StationPrice as StationPriceData;
use Exception;
use Request\DT as DTRequest;
use Throwable;

class PullOilForDt extends PullOil
{
    protected $nameAbbreviation = 'dt_';
    protected $platformCode = '';

    private $provinceNameAbbreviationToSnMapping = [
        'zj' => 'Q00',
    ];

    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-oil:dt {--provinceNameAbbreviation=}';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull oil for dt';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @return void
     * ---------------------------------------------------
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/2/4 4:23 下午
     */
    public function handle()
    {
        if (empty($this->option('provinceNameAbbreviation'))) {

            throw new Exception("the provinceNameAbbreviation is invalid");
        }
        $this->nameAbbreviation .= $this->option('provinceNameAbbreviation');
        $this->platformCode = AuthInfoData::getAuthInfoFieldByNameAbbreviation($this->nameAbbreviation)['role_code'] ?? '';
        $stationData = DTRequest::handle("shop/syncShopList", [
            'orgSn'    => $this->provinceNameAbbreviationToSnMapping[$this->option('provinceNameAbbreviation')],
            'deptType' => 2,
        ]);
        //通过高德API进行逆地理编码
        $coordinates = array_columns($stationData['shopList'], ['longitude', 'latitude']);
        $waitCoordinates = [];
        $existsStationIds = [];
        foreach ($coordinates as $v) {

            if ((empty($v['longitude']) or empty($v['latitude'])) or ($v['longitude'] == 'null' or
                    $v['latitude'] == 'null')) {

                continue;
            }
            $waitCoordinates[] = "{$v["longitude"]},{$v["latitude"]}";
        }
        $regionData = getRegionForCoordinateByGdApi($waitCoordinates);

        foreach ($stationData['shopList'] as $sv) {

            try {

                $existsStationIds[] = $sv['sn'];
                $skipUpdateStationList = json_decode(AuthConfigData::getAuthConfigValByName(
                        "DT_SKIP_UPDATE_STATION_LIST"), true) ?? [];
                if (in_array($sv['sn'], $skipUpdateStationList)) {

                    continue;
                }
                //油站数据处理
                $oilTemp = [];
                $oilTemp['price_list'] = [];
                $oilTemp['station_name'] = $sv['name'];
                $oilTemp['assoc_id'] = $sv['sn'];
                $oilTemp["lng"] = $sv["longitude"];
                $oilTemp["lat"] = $sv["latitude"];
                $oilTemp['address'] = $sv['address'];
                $oilTemp['is_stop'] = 0;
                $oilTemp['name_abbreviation'] = $this->nameAbbreviation;
                $oilTemp['id'] = $sv['sn'];
                $oilTemp['province_code'] = $regionData["{$sv["longitude"]},{$sv["latitude"]}"]['provinceCode'] ?? '';
                $oilTemp['city_code'] = $regionData["{$sv["longitude"]},{$sv["latitude"]}"]['cityCode'] ?? '';
                if (strpos($oilTemp['lng'], '.') !== false) {

                    $dealLng = explode('.', $oilTemp['lng']);
                    $oilTemp['lng'] = $dealLng[0] . '.' . substr($dealLng[1], 0, 6);
                }
                if (strpos($oilTemp['lat'], '.') !== false) {

                    $dealLat = explode('.', $oilTemp['lat']);
                    $oilTemp['lat'] = $dealLat[0] . '.' . substr($dealLat[1], 0, 6);
                }
                if (checkIsMunicipality($oilTemp['province_code'])) {

                    $oilTemp['city_code'] = $oilTemp['province_code'];
                }
                $oilTemp['trade_type'] = 3;
                $oilTemp['station_brand'] = 2;
                $insertStationPriceData = $oilTemp;
                $insertStationPriceData['enabled_state'] = 1;
                $insertStationPriceData['station_id'] = $sv['sn'];
                $insertStationPriceData['oil_price_list'] = [];
                $insertStationPriceData['platform_code'] = $this->platformCode;
                $gunNumber = 0;
                foreach ($sv['itemList'] as $iv) {

                    $oilPriceTemp = [];
                    $oilAttributesMapped = config("oil.oil_mapping.dt.{$iv['innerItemCode']}");
                    if (empty($oilAttributesMapped)) {

                        continue;
                    }
                    if (empty($iv['price'])) {

                        continue;
                    }
                    $oilAttributesMappedArr = explode('_', $oilAttributesMapped);
                    $oilPriceTemp['oil_type'] = $oilAttributesMappedArr[1];
                    $oilPriceTemp['oil_level'] = $oilAttributesMappedArr[2];
                    $oilPriceTemp['oil_name'] = $oilAttributesMappedArr[0];
                    $oilPriceTemp['price'] = $iv['price'];
                    $oilPriceTemp['mac_price'] = $iv['price'];
                    $oilTemp['price_list'][] = $oilPriceTemp;
                    $insertOilPriceTemp = $oilPriceTemp;
                    $insertOilPriceTemp['oil_type'] = array_flip(config("oil.oil_type"))[$oilAttributesMappedArr[0]] ?? '';
                    $insertOilPriceTemp['oil_no'] = str_replace($insertOilPriceTemp['oil_type'], '',
                        array_flip(config("oil.oil_no"))[$oilAttributesMappedArr[1]] ?? '');
                    $insertOilPriceTemp['oil_level'] = array_flip(config("oil.oil_level"))[$oilAttributesMappedArr[2]] ?? '';
                    $insertOilPriceTemp['sale_price'] = bcmul(100, $iv['price']);
                    $insertOilPriceTemp['listing_price'] = bcmul(100, $iv['price']);
                    $insertOilPriceTemp['gun_no'] = $gunNumber;
                    $insertStationPriceData['oil_price_list'][] = $insertOilPriceTemp;
                    $gunNumber++;
                }
                $oilTemp['clearGunCache'] = true;
                $oilTemp['gunCacheKeyPrefix'] = "gun_number_";
                BasicJob::pushStationToStationHub($oilTemp);
                StationPriceData::create($insertStationPriceData);
            } catch (Throwable $exception) {

                Log::handle("Pull station failed", [
                    'stationData' => $sv,
                    'exception'   => json_decode(Log::getMessage([
                        'exception' => $exception,
                    ]), true)['exception'],
                ], DockingPlatformInfoData::getFieldsByNameAbbreviation(explode('_',
                    $this->nameAbbreviation)[0], "platform_name", "")['platform_name'],
                    "oil_station_data", "error");
            }
        }
        $this->dealNotExistsStation($existsStationIds);
    }
}
