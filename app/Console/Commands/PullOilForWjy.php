<?php

namespace App\Console\Commands;


use App\Jobs\BasicJob;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\RegionalInfo as RegionalInfoData;
use App\Models\Data\StationPrice as StationPriceData;
use Request\WJY as WJYRequest;
use Throwable;


class PullOilForWjy extends PullOil
{
    protected $nameAbbreviation = 'wjy';
    protected $platformCode     = '';

    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-oil:wjy {--stationId=}';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull oil for wjy';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->platformCode = AuthInfoData::getAuthInfoFieldByNameAbbreviation(
            $this->nameAbbreviation
        )['role_code'] ?? '';
    }

    /**
     * @return void
     * ---------------------------------------------------
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/2/4 4:23 下午
     */
    public function handle()
    {
        $stationData = WJYRequest::handle("station/getStationAndFuels")['result'];
        $regionCodes = [];
        $waitCoordinates = [];
        $existsStationIds = [];
        foreach ($stationData as &$ov) {
            $ov['province'] = "{$ov['provinceCode']}000000";
            $ov['city'] = "{$ov['cityCode']}000000";
            $regionCodes[] = $ov['province'];
            $regionCodes[] = $ov['city'];
        }
        $existsRegionCodes = RegionalInfoData::filterCode(array_unique($regionCodes));
        foreach ($stationData as $v) {
            if (!in_array($v['province'], $existsRegionCodes) or !in_array($v['city'], $existsRegionCodes)) {
                $waitCoordinates[] = "{$v["lng"]},{$v["lat"]}";
            }
        }
        if ($waitCoordinates) {
            $regionData = getRegionForCoordinateByGdApi($waitCoordinates);
        }

        foreach ($stationData as $v) {
            try {
                //油站数据处理
                $oilTemp = [];
                $oilTemp['price_list'] = [];
                $oilTemp['station_name'] = $v['stationName'];
                $oilTemp['station_type'] = 4;
                $existsStationIds[] = $v['stationId'];
                $oilTemp['assoc_id'] = $v['stationId'];
                $oilTemp['lng'] = $v['lng'];
                $oilTemp['lat'] = $v['lat'];
                if (strpos($oilTemp['lng'], '.') !== false) {
                    $dealLng = explode('.', $oilTemp['lng']);
                    $oilTemp['lng'] = $dealLng[0] . '.' . substr($dealLng[1], 0, 6);
                }
                if (strpos($oilTemp['lat'], '.') !== false) {
                    $dealLat = explode('.', $oilTemp['lat']);
                    $oilTemp['lat'] = $dealLat[0] . '.' . substr($dealLat[1], 0, 6);
                }

                if (!in_array($v['province'], $existsRegionCodes) or !in_array($v['city'], $existsRegionCodes)) {
                    $oilTemp['province_code'] = $regionData["{$v["lng"]},{$v["lat"]}"]['provinceCode'] ?? '';
                    $oilTemp['city_code'] = $regionData["{$v["lng"]},{$v["lat"]}"]['cityCode'] ?? '';
                } else {
                    $oilTemp['province_code'] = $v['province'];
                    $oilTemp['city_code'] = $v['city'];
                }
                if (checkIsMunicipality($oilTemp['city_code'])) {
                    $oilTemp['province_code'] = $oilTemp['city_code'];
                }

                $oilTemp['address'] = $v['address'];
                $oilTemp['is_stop'] = $v['isStop'];
                $oilTemp['is_highway'] = $v['isHighspeed'];
                $oilTemp['business_hours'] = $v['open_time'];
                $oilTemp['name_abbreviation'] = $this->nameAbbreviation;
                $oilTemp['id'] = $v['stationId'];
                $oilTemp['trade_type'] = 1;
                $insertStationPriceData = $oilTemp;
                $insertStationPriceData['enabled_state'] = $v['isStop'] + 1;
                $insertStationPriceData['platform_code'] = $this->platformCode;
                $insertStationPriceData['station_id'] = $v['stationId'];
                //如果油站油品价格数据不存在则舍弃该数据
                if (isset($v['fuels']) and is_array($v['fuels'])) {
                    $checkOilRepeat = [];
                    //油站价格数据
                    foreach ($v['fuels'] as $cv) {
                        if (in_array($cv['fuel_no'], $checkOilRepeat)) {
                            continue;
                        }
                        $realOilInfo = explode("_", config("oil.oil_mapping.wjy.{$cv['fuel_no']}"));
                        $oilPriceTemp = [];
                        $insertOilPriceTemp = [];
                        $oilPriceTemp['oil_type'] = $realOilInfo[1] ?? '';
                        $oilPriceTemp['oil_level'] = $realOilInfo[2] ?? '';
                        $oilPriceTemp['oil_name'] = $realOilInfo[0] ?? '';
                        if (empty($oilPriceTemp['oil_name'])) {
                            Log::handle(
                                "Oil's attribute is invalid",
                                [
                                    "data" => $v
                                ],
                                DockingPlatformInfoData::getFieldsByNameAbbreviation(
                                    $this->nameAbbreviation,
                                    "platform_name",
                                    ""
                                )['platform_name'],
                                "oil_station_data",
                                "error"
                            );
                            continue;
                        }
                        $insertOilPriceTemp['oil_no'] = config("oil.oil_no_simple.$realOilInfo[1]", "");
                        $insertOilPriceTemp['oil_level'] = array_flip(config("oil.oil_level"))[$realOilInfo[2]];
                        $insertOilPriceTemp['oil_type'] = array_flip(config("oil.oil_type"))[$realOilInfo[0]];

                        $oilPriceTemp['price'] = bcdiv($cv['price'], 100, 2);
                        $insertOilPriceTemp['sale_price'] = $cv['price'];
                        $insertOilPriceTemp['issue_price'] = $cv['guide_price'];
                        $oilTemp['price_list'][] = $oilPriceTemp;
                        $insertStationPriceData['oil_price_list'][] = $insertOilPriceTemp;
                        $checkOilRepeat[] = $cv['fuel_no'];
                    }
                } else {
                    Log::handle(
                        "Oil's price data doesn't exist",
                        [
                            "data" => $v
                        ],
                        DockingPlatformInfoData::getFieldsByNameAbbreviation(
                            $this->nameAbbreviation,
                            "platform_name",
                            ""
                        )['platform_name'],
                        "oil_station_data",
                        "error"
                    );
                }

                BasicJob::pushStationToStationHub($oilTemp);
                StationPriceData::create($insertStationPriceData);
            } catch (Throwable $exception) {
                Log::handle(
                    "Pull station failed",
                    [
                        'stationData' => $v,
                        'exception'   => json_decode(
                                             Log::getMessage([
                                                 'exception' => $exception,
                                             ]),
                                             true
                                         )['exception'],
                    ],
                    DockingPlatformInfoData::getFieldsByNameAbbreviation(
                        $this->nameAbbreviation,
                        "platform_name",
                        ""
                    )['platform_name'],
                    "oil_station_data",
                    "error"
                );
            }
        }

        $this->dealNotExistsStation($existsStationIds);
    }
}
