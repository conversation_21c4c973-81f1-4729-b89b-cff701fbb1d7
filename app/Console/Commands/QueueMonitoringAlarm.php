<?php
/**
 * Created by PhpStorm.
 * User: zhanglingxiang
 * Date: 2019/1/16
 * Time: 下午3:14
 */

namespace App\Console\Commands;


use Exception;
use Illuminate\Console\Command;
use Throwable;
use Tool\Alarm\FeiShu;

class QueueMonitoringAlarm extends Command
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'monitoring-alarm:queue {name}';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'queue monitoring alarm.
                                  -name: supplier_station_queue|.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/3/3 10:35 下午
     */
    public function handle()
    {
        $name = $this->argument("name");
        if (empty($name)) {
            throw new Exception("the name is invalid");
        }
        $taskNum = app('redis')->llen($name);
        if ($taskNum > 5000) {
            (new FeiShu())->queueMonitoringAlarm([
                'title'  => '队列长度超过预警长度',
                'name'   => $name,
                'length' => $taskNum,
                'time'   => date('Y-m-d H:i:s'),
            ]);
        }
    }
}
