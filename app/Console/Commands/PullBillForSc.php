<?php
/**
 * Created by PhpStorm.
 * User: zhanglingxiang
 * Date: 2019/1/16
 * Time: 下午3:14
 */

namespace App\Console\Commands;

use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Data\SupplierInfo as SupplierInfoData;
use Illuminate\Support\Facades\Mail;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use Request\SC as SCRequest;
use Throwable;

class PullBillForSc extends PullBill
{
    protected $nameAbbreviation = 'sc';

    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-bill:sc {--billDate=}';
    protected $name      = 'pull bill for sc';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull bill for sc';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @return void
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/4/9 5:05 下午
     */
    public function handle()
    {
        $billDate = $this->option("billDate");

        if (empty($billDate)) {
            $billDate = date("Ymd", strtotime('-1 day'));
        }

        $billFileData = SCRequest::handle("reconciliation/info", [
            "date" => $billDate,
        ]);

        if (!array_has($billFileData, ['fileName', 'fileMD5', 'transationNumber', 'transationAmount'])) {
            Log::handle(
                'Bill of sc is empty.',
                $billFileData,
                DockingPlatformInfoData::getPlatformNameByNameAbbreviation('sc'),
                'pull_bill',
                'info'
            );
            return;
        }

        if (empty($billFileData['fileName']) or empty($billFileData['fileMD5'])) {
            Log::handle(
                'Bill of sc is empty.',
                $billFileData,
                DockingPlatformInfoData::getPlatformNameByNameAbbreviation('sc'),
                'pull_bill',
                'info'
            );
            return;
        }

        $billData = SCRequest::handle("reconciliation/file", [
            "date" => $billDate,
        ]);

        if (empty($billData['responseRawData'])) {
            Log::handle(
                'Bill of sc is empty.',
                $billData,
                DockingPlatformInfoData::getPlatformNameByNameAbbreviation('sc'),
                'pull_bill',
                'info'
            );
            return;
        }

        $realBillData = explode("\r\n", $billData['responseRawData']);
        $realBillDataTitleMapping = array_flip(explode('|', $realBillData[0]));
        $realBillData = array_slice($realBillData, 1, count($realBillData));
        $dealBillData = [];

        foreach ($realBillData as $v) {
            if (!empty($v)) {
                $billTemp = explode('|', $v);
                $dealBillData[] = [
                    'platform_order_id' => $billTemp[$realBillDataTitleMapping['壳牌网关交易跟踪号']],
                    'station_id'        => $billTemp[$realBillDataTitleMapping['油站ID']],
                    'trade_time'        => $billTemp[$realBillDataTitleMapping['交易时间']],
                    'listing_price'     => $billTemp[$realBillDataTitleMapping['油机价-应付']],
                    'money'             => $billTemp[$realBillDataTitleMapping['交易金额-应付']],
                    'pay_money'         => $billTemp[$realBillDataTitleMapping['交易金额-实付']],
                    'discount'          => $billTemp[$realBillDataTitleMapping['壳牌优惠金额']],
                ];
            }
        }

        $platformOrderIds = array_column($dealBillData, 'platform_order_id');
        $orderAssocData = convertListToDict(
            OrderAssocData::getOrderInfoByWhere([
                [
                    'field'    => 'platform_order_id',
                    'operator' => 'in',
                    'value'    => $platformOrderIds,
                ],
            ], [
                'self_order_id',
                'platform_order_id',
                'extend'
            ], false),
            'platform_order_id'
        );
        $exportData = [];

        foreach ($dealBillData as $v) {
            $extend = json_decode($orderAssocData[$v['platform_order_id']]['extend'] ?? '', true);
            $v['self_order_id'] = $extend["owner"]["trades_no"] ?? '';
            $v['self_money'] = $extend['owner']['trade_money'] ?? 0.00;
            $v['station_name'] = $extend['owner']['trade_place'] ?? '';
            $v['province_name'] = $extend['owner']['trade_place_provice_name'];
            $v['city_name'] = $extend['owner']['trade_place_city_name'];

            if (!isset($exportData[$extend['owner']['pcode']])) {
                $exportData[$extend['owner']['pcode']] = [];
            }

            $exportData[$extend['owner']['pcode']][] = $v;
        }

        $supplierMapping = convertListToDict(
            SupplierInfoData::getSupplierNameBySupplierCode(
                array_keys(
                    $exportData
                )
            ),
            ['supplier_code']
        );
        $title = [
            '壳牌网关交易跟踪号' => 'platform_order_id',
            '油站ID'             => 'station_id',
            '油站名称'           => 'station_name',
            '油站供应商'         => 'supplier_name',
            '省'                 => 'province_name',
            '市'                 => 'city_name',
            '交易时间'           => 'trade_time',
            '油机价-应付'        => 'listing_price',
            '交易金额-应付'      => 'money',
            '交易金额-实付'      => 'pay_money',
            '壳牌优惠金额 '      => 'discount',
            'G7能源订单号'       => 'self_order_id',
            'G7能源司机实付金额' => 'self_money',
        ];

        foreach ($exportData as $ek => $ev) {
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();
            $titCol = 'A';

            foreach ($title as $key => $value) {
                $sheet->getStyle($titCol . '1')->applyFromArray([
                    'alignment' => [
                        'horizontal' => Alignment::HORIZONTAL_CENTER,
                        'vertical'   => Alignment::VERTICAL_CENTER,
                    ],
                ]);
                $sheet->setCellValue($titCol . '1', $key);
                $titCol++;
            }

            $row = 2;

            foreach ($ev as $item) {
                $item['supplier_name'] = $supplierMapping[$ek]['supplier_name'];
                $dataCol = 'A';

                foreach ($title as $value) {
                    $sheet->getStyle($dataCol . $row)->applyFromArray([
                        'alignment' => [
                            'horizontal' => Alignment::HORIZONTAL_CENTER,
                            'vertical'   => Alignment::VERTICAL_CENTER,
                        ],
                    ]);

                    if (is_numeric($item[$value])) {
                        if (strpos($item[$value], '.') !== false) {
                            $sheet
                                ->getStyle($dataCol . $row)
                                ->getNumberFormat()
                                ->setFormatCode(NumberFormat::FORMAT_NUMBER_00);
                        } else {
                            $sheet
                                ->getStyle($dataCol . $row)
                                ->getNumberFormat()
                                ->setFormatCode(NumberFormat::FORMAT_NUMBER);
                        }
                    }

                    $sheet
                        ->getColumnDimension($dataCol)
                        ->setAutoSize(true);
                    $sheet->setCellValue($dataCol . $row, $item[$value] ?? '');
                    $dataCol++;
                }

                $row++;
            }

            $filePath = resource_path(
                "/downloads/bill/" . $billDate . "{$supplierMapping[$ek]['supplier_name']}账单.xlsx"
            );
            $writer = IOFactory::createWriter($spreadsheet, "Xlsx");
            $writer->save($filePath);
            Mail::send("emails/bill/simple", [
                "billDate"     => $billDate,
                "tradeCount"   => count($ev),
                "tradeTotal"   => array_sum(array_column($ev, 'pay_money')),
                "platformName" => $supplierMapping[$ek]['supplier_name'],
                "remark"       => "此账单为{$supplierMapping[$ek]['supplier_name']}通过接口提供，次日08:00发送。详细账单请见附件。",
            ], function ($message) use ($filePath, $billDate, $supplierMapping, $ek) {
                $message->subject($billDate . "{$supplierMapping[$ek]['supplier_name']}账单");
                $message->to(
                    json_decode(
                        AuthConfigData::getAuthConfigValByName("BILL_MAIL_RECEIVE"),
                        true
                    )
                );
                $message->attach($filePath);
            });
            @unlink($filePath);
        }
    }
}
