<?php

namespace App\Console;

use App\Console\Commands\CheckFyOrderUseTime;
use App\Console\Commands\CheckPushOrderStatus;
use App\Console\Commands\CheckReservationOrderStatus;
use App\Console\Commands\CreateLogTable;
use App\Console\Commands\CreateStationPushRecordTable;
use App\Console\Commands\DeleteLogTable;
use App\Console\Commands\PullBillForGdqp;
use App\Console\Commands\PullBillForGs;
use App\Console\Commands\PullBillForKl;
use App\Console\Commands\PullBillForSc;
use App\Console\Commands\PullBillForYc;
use App\Console\Commands\PullOilForCy;
use App\Console\Commands\PullOilForCzb;
use App\Console\Commands\PullOilForDt;
use App\Console\Commands\PullOilForEzt;
use App\Console\Commands\PullOilForGaoDeng;
use App\Console\Commands\PullOilForGB;
use App\Console\Commands\PullOilForHsy;
use App\Console\Commands\PullOilForHyt;
use App\Console\Commands\PullOilForJH;
use App\Console\Commands\PullOilForJQ;
use App\Console\Commands\PullOilForJT;
use App\Console\Commands\PullOilForJTX;
use App\Console\Commands\PullOilForMj;
use App\Console\Commands\PullOilForMtlSy;
use App\Console\Commands\PullOilForQK;
use App\Console\Commands\PullOilForSaic;
use App\Console\Commands\PullOilForSh;
use App\Console\Commands\PullOilForShSx;
use App\Console\Commands\PullOilForSqzl;
use App\Console\Commands\PullOilForSqZsh;
use App\Console\Commands\PullOilForTBJX;
use App\Console\Commands\PullOilForWjy;
use App\Console\Commands\PullOilForXYN;
use App\Console\Commands\PullOilForYunDaTong;
use App\Console\Commands\PullOilForZdc;
use App\Console\Commands\PullOilForZhyk;
use App\Console\Commands\PullOilForZwl;
use App\Console\Commands\PullOilForZy;
use App\Console\Commands\PullPriceForCzb;
use App\Console\Commands\PullPriceForGS;
use App\Console\Commands\PullVerificationResultForCnpc;
use App\Console\Commands\PullVerificationResultForHbkj;
use App\Console\Commands\PullVerificationResultForHsy;
use App\Console\Commands\PushAccountBalanceToAD;
use App\Console\Commands\PushBillToSFFY;
use App\Console\Commands\QueueMonitoringAlarm;
use App\Console\Commands\ReleaseFrozenQuotaForDt;
use App\Console\Commands\RunSpecialCode;
use Illuminate\Console\Scheduling\Schedule;
use Laravel\Lumen\Console\Kernel as ConsoleKernel;


class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        PullOilForZy::class,
        PullOilForCzb::class,
        PullPriceForCzb::class,
        CreateLogTable::class,
        PullOilForEzt::class,
        PullBillForYc::class,
        PullBillForSc::class,
        PullPriceForGS::class,
        PullBillForGs::class,
        DeleteLogTable::class,
        PullOilForHyt::class,
        PullBillForKl::class,
        PullOilForWjy::class,
        PullOilForSaic::class,
        PullOilForGB::class,
        PullOilForDt::class,
        PushBillToSFFY::class,
        PullOilForCy::class,
        PullVerificationResultForCnpc::class,
        CreateStationPushRecordTable::class,
        PullVerificationResultForHbkj::class,
        PullOilForHsy::class,
        PullVerificationResultForHsy::class,
        PullOilForSqzl::class,
        PullBillForGdqp::class,
        QueueMonitoringAlarm::class,
        PullOilForZhyk::class,
        ReleaseFrozenQuotaForDt::class,
        PullOilForZdc::class,
        CheckPushOrderStatus::class,
        PullOilForZwl::class,
        CheckReservationOrderStatus::class,
        PullOilForXYN::class,
        PullOilForSqZsh::class,
        PullOilForMtlSy::class,
        PullOilForSh::class,
        PullOilForShSx::class,
        PullOilForJT::class,
        PullOilForJH::class,
        PullOilForQK::class,
        PullOilForJTX::class,
        PullOilForMj::class,
        PullOilForYunDaTong::class,
        PullOilForGaoDeng::class,
        PushAccountBalanceToAD::class,
        RunSpecialCode::class,
        PullOilForTBJX::class,
        CheckFyOrderUseTime::class,
        PullOilForJQ::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param Schedule $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        //每周创建接收请求日志表
        $schedule->command("log-table:create receive")
                 ->description("定时创建接收请求日志表")
                 ->weekly()
                 ->runInBackground();
        //每周创建请求日志表
        $schedule->command("log-table:create request")
                 ->description("定时创建请求日志表")
                 ->weekly()
                 ->runInBackground();
        //每周创建队列日志表
        $schedule->command("log-table:create queue")
                 ->description("定时创建队列日志表")
                 ->weekly()
                 ->runInBackground();
        //每周创建响应日志表
        $schedule->command("log-table:create response")
                 ->description("定时创建响应日志表")
                 ->weekly()
                 ->runInBackground();
        //每三个小时拉取一次车主邦油站数据
        $schedule->command("pull-oil:czb")
                 ->description("拉取车主邦油站数据")
                 ->hourly()
                 ->runInBackground();
        //每十五钟拉取一次车主邦油站价格数据
        $schedule->command("pull-price:czb")
                 ->description("拉取车主邦油站价格数据")
                 ->everyFifteenMinutes()
                 ->runInBackground();
        //每一个小时拉取一次E站途油站数据
        $schedule->command('pull-oil:ezt')
                 ->description("拉取E站途油站数据")
                 ->hourly()
                 ->runInBackground();
        //每天8点拉取四川壳牌账单数据
        $schedule->command("pull-bill:sc")
                 ->description("拉取四川壳牌账单数据")
                 ->dailyAt('08:00')
                 ->runInBackground();
        //每天8点拉取广东壳牌账单数据
        $schedule->command("pull-bill:yc")
                 ->description("拉取广东壳牌账单数据")
                 ->dailyAt('08:00')
                 ->runInBackground();
        //每十五分钟拉取一次港森油站价格数据
        $schedule->command("pull-price:gs")
                 ->description("拉取港森油站价格数据")
                 ->everyTenMinutes()
                 ->runInBackground();
        //每天8点拉取港森账单数据
        $schedule->command("pull-bill:gs")
                 ->description("拉取港森账单数据")
                 ->dailyAt('08:00')
                 ->runInBackground();
        //每天8点拉取昆仑润滑油账单数据
        $schedule->command("pull-bill:kl")
                 ->description("拉取昆仑润滑油账单数据")
                 ->dailyAt('08:00')
                 ->runInBackground();
        //每周删除接收请求日志表
        $schedule->command("log-table:delete receive")
                 ->description("定时创建接收请求日志表")
                 ->weekly()
                 ->runInBackground();
        //每周删除请求日志表
        $schedule->command("log-table:delete request")
                 ->description("定时创建请求日志表")
                 ->weekly()
                 ->runInBackground();
        //每周删除队列日志表
        $schedule->command("log-table:delete queue")
                 ->description("定时创建队列日志表")
                 ->weekly()
                 ->runInBackground();
        //每周删除响应日志表
        $schedule->command("log-table:delete response")
                 ->description("定时创建响应日志表")
                 ->weekly()
                 ->runInBackground();
        //每5分钟拉取一次惠运通气站数据
        $schedule->command("pull-oil:hyt")
                 ->everyFiveMinutes()
                 ->description("拉取惠运通气站数据")
                 ->runInBackground();
        //每5分钟拉取一次万金油站点数据
        $schedule->command("pull-oil:wjy")
                 ->everyFiveMinutes()
                 ->description("拉取万金油站点数据")
                 ->runInBackground();
        //每1小时拉取一次上汽-安吉站点数据
        $schedule->command("pull-oil:saic --supplier_identifier=aj")
                 ->hourly()
                 ->description("拉取上汽-安吉站点数据")
                 ->runInBackground();
        //每1小时拉取一次固本能源站点数据
        $schedule->command("pull-oil:gb --name_abbreviation=gb")
                 ->hourly()
                 ->description("拉取固本能源站点数据")
                 ->runInBackground();
        //每1小时拉取一次上汽-复岭站点数据
        $schedule->command("pull-oil:saic --supplier_identifier=fl")
                 ->hourly()
                 ->description("拉取上汽-复岭站点数据")
                 ->runInBackground();
        //每半小时启动浙江中石油站点数据爬取程序
        $schedule->command("pull-oil:dt --provinceNameAbbreviation=zj")
                 ->description("定时爬取浙江中石油站点数据")
                 ->everyFifteenMinutes()
                 ->runInBackground();
        //每天凌晨2点推送账单到丰油
        $schedule->command("push-bill:sffy")
                 ->description("定时推送账单到丰油")
                 ->dailyAt("02:00")
                 ->runInBackground();
        //每1小时拉取一次上汽-安吉四维站点数据
        $schedule->command("pull-oil:saic --supplier_identifier=ajsw")
                 ->hourly()
                 ->description("拉取上汽-安吉四维站点数据")
                 ->runInBackground();
        //每3,9,15,21整点拉取一次畅油站点数据
        $schedule->command("pull-oil:cy")
                 ->cron("0 3,9,15,21 * * *")
                 ->description("拉取畅油站点数据")
                 ->runInBackground();
        //每1小时校对一次河北中石油订单是否核销
        $schedule->command("pull-verification-result:cnpc")
                 ->hourly()
                 ->description("拉取河北中石油订单电子券状态")
                 ->runInBackground();
        //每月创建站点数据推送记录表
        $schedule->command("push-station-record-table:create")
                 ->description("定时创建站点数据推送记录表")
                 ->monthly()
                 ->runInBackground();
        //每1小时校对一次湖北中石油跨界券订单是否核销
        $schedule->command("pull-verification-result:hbkj")
                 ->hourly()
                 ->description("拉取湖北中石油跨界券订单电子券状态")
                 ->runInBackground();
        //每1小时拉取一次上汽-羿驰站点数据
        $schedule->command("pull-oil:saic --supplier_identifier=yc")
                 ->hourly()
                 ->description("拉取上汽-羿驰站点数据")
                 ->runInBackground();
        //每3,9,15,21整点拉取一次华商云站点数据
        $schedule->command("pull-oil:hsy")
                 ->cron("0 3,9,15,21 * * *")
                 ->description("拉取华商云站点数据")
                 ->runInBackground();
        //每1小时校对一次华商云订单是否核销
        $schedule->command("pull-verification-result:hsy")
                 ->hourly()
                 ->description("拉取华商云订单核销状态")
                 ->runInBackground();
        //每小时第20分分钟拉取一次上汽直联油站数据
        $schedule->command("pull-oil:sqzl")
                 ->hourlyAt(20)
                 ->description("拉取上汽直联油站数据")
                 ->runInBackground();
        //每天8点拉取广东壳牌(新)账单数据
        $schedule->command("pull-bill:gdqp")
                 ->description("拉取广东壳牌(新)账单数据")
                 ->dailyAt('08:00')
                 ->runInBackground();
        //每1小时拉取一次固本能源(天津)站点数据
        $schedule->command("pull-oil:gb --name_abbreviation=gb_tj")
                 ->hourly()
                 ->description("拉取固本能源(天津)站点数据")
                 ->runInBackground();
        //每5分钟检查上游站点推送GMS队列长度
        $schedule->command("monitoring-alarm:queue supplier_station_queue")
                 ->everyFiveMinutes()
                 ->description("检查上游站点推送GMS队列长度")
                 ->runInBackground();
        //每0,6,12,18的30分拉取一次智慧油客站点数据
        $schedule->command("pull-oil:zhyk")
                 ->cron("30 0,6,12,18 * * *")
                 ->description("拉取智慧油客站点数据")
                 ->runInBackground();
        //每3,15点整拉取一次掌多车站点数据
        $schedule->command("pull-oil:zdc")
                 ->cron("0 3,15 * * *")
                 ->description("拉取掌多车站点数据")
                 ->runInBackground();
        //每5分钟对比推送订单状态与当前订单状态是否一致
        $schedule->command("check-order-push-status")
                 ->everyFiveMinutes()
                 ->description("对比推送订单状态与当前订单状态是否一致")
                 ->runInBackground();
        //每天凌晨1点拉取中物流站点数据
        $schedule->command("pull-oil:zwl")
                 ->description("拉取中物流站点数据")
                 ->dailyAt("01:00")
                 ->runInBackground();
        //每3点整拉取一次星油(新)站点数据
        $schedule->command("pull-oil:xyn")
                 ->cron("0 3 * * *")
                 ->description("拉取星油(新)站点数据")
                 ->runInBackground();
        //每1点整拉取一次上汽中石化站点数据
        $schedule->command("pull-oil:sqZsh")
                 ->cron("0 1 * * *")
                 ->description("拉取上汽中石化站点数据")
                 ->runInBackground();
        //每天凌晨1点拉取梦驼铃(上游)站点数据
        $schedule->command("pull-oil:mtlSy")
                 ->description("拉取梦驼铃(上游)站点数据")
                 ->dailyAt("01:00")
                 ->runInBackground();
        //每天凌晨1点拉取善宏站点数据
        $schedule->command("pull-oil:sh")
                 ->description("拉取善宏站点数据")
                 ->dailyAt("01:00")
                 ->runInBackground();
        //每天凌晨1点拉取善宏(山西)站点数据
        $schedule->command("pull-oil:shsx")
                 ->description("拉取善宏(山西)站点数据")
                 ->dailyAt("01:00")
                 ->runInBackground();
        //每天凌晨1点拉取江投站点数据
        $schedule->command("pull-oil:jt")
                 ->description("拉取江投站点数据")
                 ->dailyAt("01:00")
                 ->runInBackground();
        //每天凌晨1点拉取九和站点数据
        $schedule->command("pull-oil:jh")
                 ->description("拉取九和站点数据")
                 ->dailyAt("01:00")
                 ->runInBackground();
        //每天凌晨1点拉取汽开站点数据
        $schedule->command("pull-oil:qk")
                 ->description("拉取汽开站点数据")
                 ->dailyAt("01:00")
                 ->runInBackground();
        //每天凌晨1点拉取江投(新)站点数据
        $schedule->command("pull-oil:jtx")
                 ->description("拉取江投(新)站点数据")
                 ->dailyAt("01:00")
                 ->runInBackground();
        //每天凌晨1点拉取秒加站点数据
        $schedule->command("pull-oil:mj")
                 ->description("拉取秒加站点数据")
                 ->dailyAt("01:00")
                 ->runInBackground();
        //每天凌晨1点拉取运达通站点数据
        $schedule->command("pull-oil:yundatong")
                 ->description("拉取运达通站点数据")
                 ->dailyAt("01:00")
                 ->runInBackground();
        //每天凌晨1点拉取高灯站点数据
        $schedule->command("pull-oil:gaodeng")
                 ->description("拉取高灯站点数据")
                 ->dailyAt("01:00")
                 ->runInBackground();
        //每六分钟同步一次账户资金信息给安得
        $schedule->command("push-account-balance:ad")
                 ->description("同步账户资金信息给安得")
                 ->cron("*/6 * * * *")
                 ->runInBackground();
        //每天0点5分拉取通宝吉祥站点数据
        $schedule->command("pull-oil:tbjx")
                 ->description("拉取通宝吉祥站点数据")
                 ->dailyAt("00:05")
                 ->runInBackground();
        //每天3、12、18点整拉取通宝吉祥站点数据
        $schedule->command("pull-oil:tbjx")
                 ->description("拉取通宝吉祥站点数据")
                 ->cron("0 3,12,18 * * *")
                 ->runInBackground();
        $schedule->command("check-fy-order-use-time")
                 ->everyFiveMinutes()
                 ->description("检查福佑订单使用时长")
                 ->runInBackground();
        //每3,15点整拉取一次鲸启站点数据
        $schedule->command("pull-oil:jq")
                 ->cron("0 3,15 * * *")
                 ->description("拉取鲸启站点数据")
                 ->runInBackground();
    }
}
