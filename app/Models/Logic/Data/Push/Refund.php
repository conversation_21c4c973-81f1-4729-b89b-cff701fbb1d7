<?php
/**
 * Created by PhpStorm.
 * User: zdl
 * Date: 2019-01-28
 * Time: 15:22
 */

namespace App\Models\Logic\Data\Push;


use App\Models\Data\AuthInfo;
use App\Models\Logic\Base;
use App\Models\Logic\Data\Push\Refund\DT;
use App\Models\Logic\Data\Push\Refund\JQ;
use App\Models\Logic\Data\Push\Refund\MTLSY;
use App\Models\Logic\Data\Push\Refund\SH;
use App\Models\Logic\Data\Push\Refund\SHSX;
use App\Models\Logic\Data\Push\Refund\SQZL;
use App\Models\Logic\Data\Push\Refund\TBJX;
use App\Models\Logic\Data\Push\Refund\ZDC;
use Illuminate\Http\JsonResponse;

class Refund extends Base
{
    private $jobMapping = [
        'sqzl'  => SQZL::class,
        'dt'    => DT::class,
        'zdc'   => ZDC::class,
        'mtlSy' => MTLSY::class,
        'sh'    => SH::class,
        'shsx'  => SHSX::class,
        'tbjx'  => TBJX::class,
        'jq'    => JQ::class,
    ];

    /**
     * @return JsonResponse
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-03-25 19:39
     */
    public function handle(): JsonResponse
    {
        $authInfo = AuthInfo::getAuthInfoByRoleCode([$this->data["data"]["order_holder_code"]], true);
        if (empty($authInfo)) {
            return responseFormat(5000007, []);
        }
        if (!isset($this->jobMapping[$authInfo['name_abbreviation']])) {
            $realNameAbbreviation = explode('_', $authInfo['name_abbreviation'])[0];
            if (!isset($this->jobMapping[$realNameAbbreviation])) {
                $realNameAbbreviation = explode('|', $authInfo['name_abbreviation'])[0];
                if (!isset($this->jobMapping[$realNameAbbreviation])) {
                    return responseFormat(5000007, []);
                }
            }
            $authInfo = AuthInfo::getAuthInfoFieldByNameAbbreviation($realNameAbbreviation);
        }
        $this->data['auth_data'] = $authInfo;
        (new $this->jobMapping[$authInfo['name_abbreviation']]($this->data))->handle();
        return responseFormat();
    }
}
