<?php


namespace App\Models\Logic\Data\Push\OnlinePayOrder;


use App\Models\Logic\Base;

abstract class ThirdParty extends Base
{
    public function __construct(array $parameter)
    {
        parent::__construct($parameter);
        $this->accessKey = $this->data['data']['auth_info_data']['access_key'];
        $this->secret = $this->data['data']['auth_info_data']['secret'];
        $this->data = $this->data['data']; //把真实数据替换到实例中
    }
}
