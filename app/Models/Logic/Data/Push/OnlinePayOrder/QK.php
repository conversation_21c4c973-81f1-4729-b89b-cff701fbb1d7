<?php
// 汽开

namespace App\Models\Logic\Data\Push\OnlinePayOrder;


use App\Models\Dao\DriverMapping;
use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\Log\ResponseLog;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Data\StationUniqueMapping as StationUniqueMappingData;
use Request\QK as QKRequest;
use Throwable;
use Tool\Alarm\FeiShu;


class QK extends ThirdParty
{
    /**
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/12/1 7:32 下午
     */
    public function handle()
    {
        $pushExtends = json_decode($this->data['pushExtends'] ?? '', true);
        if (!isset($pushExtends['amountGun'])) {
            $this->cancelToGasAndUpdateOrder($this->data['id'], '', 5000030);
        }
        if (empty($this->data['drivertel']) or empty($this->data['truck_no']) or empty($this->data['driver_name'])) {
            try {
                $orgNameAbbreviation = AuthInfoData::getAuthInfoByRoleCode(
                    $this->data['org_code'],
                    true
                )['name_abbreviation'];
                $mainOrgCode = AuthInfoData::getAuthInfoFieldByNameAbbreviation(
                    explode('_', $orgNameAbbreviation)[0],
                    ['role_code'],
                    true
                )->role_code;
                $orgDriverInfo = (json_decode(
                                      AuthConfigData::getAuthConfigValByName(
                                          'QK_DRIVER_INFO_MAPPING'
                                      ),
                                      true
                                  ) ?? [])[$mainOrgCode] ?? [];
                if (empty($this->data['drivertel'])) {
                    $this->data['drivertel'] = $orgDriverInfo['driver_phone'] ?? '';
                }
                if (empty($this->data['driver_name'])) {
                    $this->data['driver_name'] = $orgDriverInfo['driver_name'] ?? '';
                }
                if (empty($this->data['truck_no'])) {
                    $this->data['truck_no'] = $orgDriverInfo['truck_no'] ?? '';
                }
            } catch (Throwable $throwable) {
                ResponseLog::handle([
                    'exception' => $throwable,
                ]);
            }
        }
        $this->checkDriverRegistered();
        $orderAssocModel = OrderAssocData::insert(
            2,
            2,
            $this->data['id'],
            '',
            $this->data['auth_info_data']['name_abbreviation'],
            '',
            "",
            json_encode($this->data)
        );
        $oilType = array_flip(config('oil.oil_type'))[$this->data['oil_name_id']] ?? '';
        $oilNo = str_replace(
            array_flip(config('oil.oil_type'))[$this->data['oil_name_id']],
            "",
            config('oil.oil_no_simple')[$this->data['oil_type_id']] ?? ''
        );
        $oilLevel = array_flip(config('oil.oil_level'))[$this->data['oil_level_id']] ?? '';
        $stationPriceInfo = StationUniqueMappingData::getStationPriceByStationId([
            'station_id' => $this->data['station_id'],
            [
                'field'    => 'oil_type',
                'operator' => '=',
                'value'    => $oilType,
            ],
            [
                'field'    => 'oil_no',
                'operator' => '=',
                'value'    => $oilNo,
            ],
            [
                'field'    => 'oil_level',
                'operator' => '=',
                'value'    => $oilLevel,
            ],
            [
                'field'    => 'platform_code',
                'operator' => '=',
                'value'    => $this->data['auth_info_data']['role_code'],
            ]
        ]);
        if (empty($stationPriceInfo)) {
            $this->cancelToGasAndUpdateOrder($this->data['id'], '', 5000027);
        }
        try {
            $skuCode = array_flip(
                           config("oil.oil_mapping.{$this->data['auth_info_data']['name_abbreviation']}.oil")
                       )[$this->data['oil_name_id'] .
                         "_" .
                         $this->data['oil_type_id'] .
                         "_" .
                         $this->data['oil_level_id']] ?? '';
            $gunId = explode(',', $stationPriceInfo['gun_no'])[0] ?? '';
            $orderData = QKRequest::handle("dispense.oilMerOnePay", [
                "orderId"     => $this->data['id'],
                "orderTime"   => date('Y-m-d H:i:s'),
                "stationId"   => $stationPriceInfo['station_id'],
                "gunId"       => $gunId,
                "consumerNum" => $this->data['oil_num'],
                "amount"      => $pushExtends['amountGun'],
                "oilPrice"    => $pushExtends['priceGun'],
                "skuCode"     => $skuCode,
                "gasType"     => config(
                    "oil.oil_mapping.{$this->data['auth_info_data']['name_abbreviation']}.type.{$this->data['oil_name_id']}"
                ),
                "mobileid"    => $this->data['drivertel'],
                "carNo"       => $this->data['truck_no'],
                "driverName"  => $this->data['driver_name'],
            ]);
        } catch (Throwable $exception) {
            (new FeiShu())->supplierOrderFailed([
                'platform_name'  => DockingPlatformInfoData::getPlatformNameByNameAbbreviation(
                    $this->data['auth_info_data']['name_abbreviation']
                ),
                'self_order_id'  => $this->data['id'],
                'station_name'   => $this->data['trade_place'],
                'oil_name'       => $this->data['oil_name'],
                'supplier_price' => $pushExtends['price'],
                'oil_num'        => $this->data['oil_num'],
                'supplier_money' => $this->data['supplier_money'],
                'plate_number'   => $this->data['truck_no'],
                'driver_phone'   => $this->data['drivertel'],
                'reason'         => $exception->getMessage(),
            ]);
            $this->cancelToGasAndUpdateOrder($this->data['id'], $exception->getMessage(), $exception->getCode());
        }
        if ($orderData['data']['resultStatus'] == 'fail') {
            $this->cancelToGasAndUpdateOrder($this->data['id'], '供应商下单失败', 5000999);
        }
        $orderAssocModel->platform_order_id = $orderData['data']['oilPlatSerial'];
        $orderData['data']['gSevenExtends']['skuCode'] = $skuCode;
        $orderData['data']['gSevenExtends']['gunId'] = $gunId;
        $orderData['data']['gSevenExtends']['gunName'] = json_decode(
                                                             $stationPriceInfo['extends'],
                                                             true
                                                         )['gun_mapping'][$skuCode][$gunId] ?? '';
        $orderAssocModel->extend = json_encode([
            'owner'  => $this->data,
            'either' => $orderData['data'],
        ]);
        $orderAssocModel->save();
        responseFormat(0, [
            'order_id' => $orderData['data']['oilPlatSerial'],
        ], true);
    }

    public function checkDriverRegistered()
    {
        $redis = app('redis');
        if (!json_decode(
            $redis->hget(
                $this->data['auth_info_data']['name_abbreviation'] . "_driver_mapping",
                $this->data['drivertel']
            ),
            true
        )) {
            $driverInfoFromDb = DriverMapping::query()
                                             ->where('phone', '=', $this->data['drivertel'])
                                             ->where(
                                                 'platform_name',
                                                 '=',
                                                 $this->data['auth_info_data']['name_abbreviation']
                                             )
                                             ->first();
            if (!$driverInfoFromDb) {
                $openCardResult = QKRequest::handle("dispense.userAdd", [
                    'userInfo' => [
                        [
                            'userName'  => $this->data['driver_name'],
                            'userPhone' => $this->data['drivertel'],
                            'carNo'     => $this->data['truck_no'],
                        ]
                    ]
                ]);
                (new DriverMapping())->updateOrCreate([
                    'third_party_uniq' => $openCardResult['data']['carInfo'][0]['fleetDriverId'],
                    'phone'            => $this->data['drivertel'],
                    'platform_name'    => $this->data['auth_info_data']['name_abbreviation'],
                ], [
                    'third_party_uniq' => $openCardResult['data']['carInfo'][0]['fleetDriverId'],
                    'phone'            => $this->data['drivertel'],
                    'platform_name'    => $this->data['auth_info_data']['name_abbreviation'],
                ]);
                $redis->hset(
                    $this->data['auth_info_data']['name_abbreviation'] . "_driver_mapping",
                    $this->data['drivertel'],
                    json_encode($openCardResult['data']['carInfo'][0])
                );
            }
        }
    }
}
