<?php


namespace App\Models\Logic\Data\Push\OnlinePayOrder;


use App\Jobs\SubPullPriceInfoForCzb;
use App\Models\Data\Log\ReceiveLog as Log;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Data\StationUniqueMapping as StationUniqueMappingData;
use Illuminate\Support\Facades\Queue;
use Request\CZB as CZBRequest;
use Throwable;
use Tool\Alarm\FeiShu;
use Tool\CzbLogin;

class CZB extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 15:31
     */
    public function handle()
    {
        OrderAssocData::insert(
            2,
            2,
            $this->data['id'],
            '',
            'czb',
            '',
            "",
            json_encode([
                'owner' => $this->data,
            ])
        );
        $driverPhone = $this->data['drivertel'] ?? '';
        if (empty($driverPhone)) {
            $this->cancelToGasAndUpdateOrder($this->data['id'], '', 5000026);
        }

        if (empty($this->data['oil_type_id'])) {
            $oilNameId = '';
        } else {
            $oilNameId = str_replace(
                array_flip(config('oil.oil_type'))[$this->data['oil_name_id']],
                "",
                config('oil.oil_no_simple')[$this->data['oil_type_id']]
            );
        }
        $stationPriceInfo = StationUniqueMappingData::getStationPriceByStationId([
            'station_id' => $this->data['station_id'],
            [
                'field'    => 'oil_type',
                'operator' => '=',
                'value'    => array_flip(config('oil.oil_type'))[$this->data['oil_name_id']],
            ],
            [
                'field'    => 'oil_no',
                'operator' => '=',
                'value'    => $oilNameId,
            ],
            [
                'field'    => 'oil_level',
                'operator' => '=',
                'value'    => '',
            ],
            [
                'field'    => 'platform_code',
                'operator' => '=',
                'value'    => $this->data['auth_info_data']['role_code'],
            ]
        ]);
        if (empty($stationPriceInfo)) {
            $this->cancelToGasAndUpdateOrder($this->data['id'], '', 5000027);
        }
        $pushExtends = json_decode($this->data['pushExtends'] ?? '', true);
        if (!$pushExtends or !isset($pushExtends['price']) or !isset($pushExtends['priceGun'])) {
            $this->cancelToGasAndUpdateOrder($this->data['id'], '', 5000025);
        }
        if (!in_array($pushExtends['gunNumber'] ?? '', explode(',', $stationPriceInfo['gun_no']))) {
            $this->cancelToGasAndUpdateOrder(
                $this->data['id'],
                "枪号不正确，输入枪号：{$pushExtends['gunNumber']}",
                5000031
            );
        }
        if ($pushExtends['price'] != $stationPriceInfo['sale_price']) {
            app('redis')->hdel(
                SubPullPriceInfoForCzb::OIL_STATION_PRICE_CACHE_CHECK . '_czb',
                $stationPriceInfo['station_id']
            );
            Queue::push(new SubPullPriceInfoForCzb([$stationPriceInfo['station_id']]));
            $this->cancelToGasAndUpdateOrder(
                $this->data['id'],
                "站点应付单价异常,无法下单。传入价格：" .
                "{$pushExtends['price']}，当前价格：{$stationPriceInfo['sale_price']}",
                5000032
            );
        }
        if ($pushExtends['priceGun'] != $stationPriceInfo['listing_price']) {
            app('redis')->hdel(
                SubPullPriceInfoForCzb::OIL_STATION_PRICE_CACHE_CHECK . '_czb',
                $stationPriceInfo['station_id']
            );
            Queue::push(new SubPullPriceInfoForCzb([$stationPriceInfo['station_id']]));
            $this->cancelToGasAndUpdateOrder(
                $this->data['id'],
                "枪价异常,无法下单。传入价格：" .
                "{$pushExtends['priceGun']}，当前价格：{$stationPriceInfo['listing_price']}",
                5000032
            );
        }
        if (bccomp($stationPriceInfo['sale_price'], $stationPriceInfo['listing_price']) == 1) {
            (new FeiShu())->saleGeGunForPrice([
                'platformName' => '车主邦',
                'stationName'  => $stationPriceInfo['station_name'],
                'oilInfo'      => "{$stationPriceInfo['oil_no']}{$stationPriceInfo['oil_type']}{$stationPriceInfo['oil_level']}",
                'gunPrice'     => $stationPriceInfo['listing_price'],
                'salePrice'    => $stationPriceInfo['sale_price'],
            ]);
            $this->cancelToGasAndUpdateOrder($this->data['id'], '', 5000029);
        }
        if (isset($pushExtends['amountGun'])) {
            $amountGun = $pushExtends['amountGun'];
        } else {
            $this->cancelToGasAndUpdateOrder($this->data['id'], '', 5000030);
        }

        try {
            $token = CzbLogin::handle($driverPhone, false);
            $genOrderData = [
                "token"        => $token,
                "gasId"        => $stationPriceInfo['station_id'],
                "gunNo"        => $pushExtends['gunNumber'],
                "amountGun"    => $amountGun,
                "couponId"     => -1,
                "outerOrderId" => $this->data['id'],
            ];
            $orderData = CZBRequest::handle("orderws/createOrder", $genOrderData);
            if (empty($orderData['result'])) {
                Log::handle(
                    "Failure to place an order of czb",
                    "车主邦",
                    $this->data["data"],
                    "error"
                );
                return;
            }
        } catch (Throwable $exception) {
            if ($exception->getCode() == 5000999) {
                if (strpos($exception->getMessage(), "价错误") !== false) {
                    app('redis')->hdel(
                        SubPullPriceInfoForCzb::OIL_STATION_PRICE_CACHE_CHECK . '_czb',
                        $stationPriceInfo['station_id']
                    );
                    Queue::push(
                        new SubPullPriceInfoForCzb([$stationPriceInfo['station_id']]),
                        '',
                        'supplier_station_queue'
                    );
                    $this->cancelToGasAndUpdateOrder(
                        $this->data['id'],
                        "油站信息有更新，请您多刷新几次后再次支付",
                        5000999
                    );
                }

                $this->cancelToGasAndUpdateOrder($this->data['id'], $exception->getMessage(), 5000001);
            }
        }

        OrderAssocData::updateOrderInfoByOrderId($this->data['id'], [
            'platform_order_status' => 2,
            'platform_order_id'     => $orderData['result']['orderSn'],
            'extend'                => json_encode([
                'owner'  => $this->data,
                'either' => $orderData['result'],
            ]),
        ]);
        responseFormat(0, [
            'order_id' => $orderData['result']['orderSn'],
        ], true);
    }
}
