<?php


namespace App\Models\Logic\Data\Push\OnlinePayOrder;


use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Data\StationUniqueMapping as StationUniqueMappingData;
use Request\ZDC as ZDCRequest;
use Throwable;

class ZDC extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 15:31
     */
    public function handle()
    {
        OrderAssocData::insert(
            2,
            2,
            $this->data['id'],
            '',
            $this->data['auth_info_data']['name_abbreviation'],
            '',
            "",
            json_encode([
                'owner' => $this->data,
            ])
        );
        $oilType = array_flip(config('oil.oil_type'))[$this->data['oil_name_id']] ?? '';
        $oilNo = str_replace(
            array_flip(config('oil.oil_type'))[$this->data['oil_name_id']],
            "",
            config('oil.oil_no_simple')[$this->data['oil_type_id']] ?? ''
        );
        $oilLevel = array_flip(config('oil.oil_level'))[$this->data['oil_level_id']] ?? '';
        $stationPriceInfo = StationUniqueMappingData::getStationPriceByStationId([
            'station_id' => $this->data['station_id'],
            [
                'field'    => 'oil_type',
                'operator' => '=',
                'value'    => $oilType,
            ],
            [
                'field'    => 'oil_no',
                'operator' => '=',
                'value'    => $oilNo,
            ],
            [
                'field'    => 'oil_level',
                'operator' => '=',
                'value'    => $oilLevel,
            ],
            [
                'field'    => 'platform_code',
                'operator' => '=',
                'value'    => $this->data['auth_info_data']['role_code'],
            ]
        ]);
        if (empty($stationPriceInfo)) {
            $this->cancelToGasAndUpdateOrder($this->data['id'], '', 5000027);
        }
        $pushExtends = json_decode($this->data['pushExtends'] ?? '', true);
        if (!$pushExtends or !isset($pushExtends['price']) or !isset($pushExtends['priceGun'])) {
            $this->cancelToGasAndUpdateOrder($this->data['id'], '', 5000025);
        }
        if (!in_array($pushExtends['gunNumber'] ?? '', explode(',', $stationPriceInfo['gun_no']))) {
            $this->cancelToGasAndUpdateOrder(
                $this->data['id'],
                "枪号不正确，输入枪号：{$pushExtends['gunNumber']}",
                5000031
            );
        }
        if ($pushExtends['price'] != $stationPriceInfo['sale_price']) {
            $this->cancelToGasAndUpdateOrder(
                $this->data['id'],
                "站点应付单价异常,无法下单。传入价格：" .
                "{$pushExtends['price']}，当前价格：{$stationPriceInfo['sale_price']}",
                5000032
            );
        }
        if ($pushExtends['priceGun'] != $stationPriceInfo['listing_price']) {
            $this->cancelToGasAndUpdateOrder(
                $this->data['id'],
                "枪价异常,无法下单。传入价格：" .
                "{$pushExtends['priceGun']}，当前价格：{$stationPriceInfo['listing_price']}",
                5000032
            );
        }
        if (isset($pushExtends['amountGun'])) {
            $amountGun = $pushExtends['amountGun'];
        } else {
            $this->cancelToGasAndUpdateOrder($this->data['id'], '', 5000030);
        }

        try {
            $orderData = ZDCRequest::handle("shell/services/createOrder", 'put', [
                "stationId"    => (string)$stationPriceInfo['station_id'],
                "gunCode"      => (string)$pushExtends['gunNumber'],
                "orderOilMass" => (string)($this->data['oil_unit'] == 1 ? round(
                    bcdiv($amountGun, $pushExtends['priceGun'], 3),
                    2
                ) : $this->data['oil_num']),
                "orderMoney"   => (string)$this->data['oil_unit'] == 1 ? $amountGun : bcmul(
                    $pushExtends['priceGun'],
                    $this->data['oil_num'],
                    2
                ),
                "oilCode"      => (string)array_flip(
                                              config(
                                                  "oil.oil_mapping.zdc.mapping"
                                              )
                                          )[$this->data['oil_name_id'] . '_' .
                                            $this->data['oil_type_id'] . '_' .
                                            $this->data['oil_level_id']] ?? '',
                "outTradeNo"   => (string)$this->data['id'],
                'settlePrice'  => (string)$pushExtends['price'],
                "stationPrice" => (string)$pushExtends['priceGun'],
                'driverPhone'  => (string)$this->data['drivertel'],
                'carNumber'    => $this->data['truck_no'],
            ]);
        } catch (Throwable $exception) {
            if ($exception->getCode() == 5000999) {
                $this->cancelToGasAndUpdateOrder($this->data['id'], $exception->getMessage(), 5000001);
            }
        }

        OrderAssocData::updateOrderInfoByOrderId($this->data['id'], [
            'platform_order_status' => 2,
            'platform_order_id'     => $orderData['result']['orderId'],
            'extend'                => json_encode([
                'owner'  => $this->data,
                'either' => $orderData['result'],
            ]),
        ]);
        responseFormat(0, [
            'order_id' => $orderData['result']['orderId'],
        ], true);
    }
}
