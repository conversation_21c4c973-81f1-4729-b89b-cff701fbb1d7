<?php


namespace App\Models\Logic\Data\Push\PaySuccess;


use App\Jobs\RunTaskByJob;
use Carbon\Carbon;
use Illuminate\Support\Facades\Queue;
use Request\ZY as ZYRequest;
use Throwable;


class ZY extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/11/7 10:04 上午
     */
    public function handle()
    {
        Queue::later(Carbon::now()->addSeconds(1), new RunTaskByJob($this, "realHandle", [],
            false, -1));
    }

    /**
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2021/3/12 6:43 下午
     */
    public function realHandle()
    {
        try {

            $extend = json_decode($this->orderInfoModel->extend, true);
            ZYRequest::handle("order.payOrder", [
                'orderSn'       => $this->orderInfoModel->platform_order_id,
                'thirdPrice'    => $extend['either']['price'],
                'thirdLitre'    => $extend['either']['oilNum'],
                'thirdOrderSn'  => $this->orderInfoModel->self_order_id,
                'paymentAmount' => $extend['either']['money'],
            ]);
            $this->orderInfoModel->self_order_status = 1;
            $this->orderInfoModel->platform_order_status = 1;
            $this->orderInfoModel->save();
        } catch (Throwable $exception) {

            if ($exception->getCode() == 5000999) {

                $this->refundToTradeCenterForPayCallbackFailed($this->orderInfoModel,
                    $exception->getMessage());
            }

            throw $exception;
        }
    }
}
