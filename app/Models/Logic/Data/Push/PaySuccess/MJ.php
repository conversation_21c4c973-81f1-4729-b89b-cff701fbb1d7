<?php


namespace App\Models\Logic\Data\Push\PaySuccess;


use Request\MJ as MJRequest;
use Throwable;


class MJ extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 15:31
     */
    public function handle()
    {
        $extends = json_decode($this->orderInfoModel->extend, true);
        try {
            $requestData["orderNo"] = $this->orderInfoModel->platform_order_id;
            $requestData["orderSn"] = $this->orderInfoModel->self_order_id;
            MJRequest::handle("/api/v1/g7/order/pay", $requestData);
            $this->orderInfoModel->platform_order_status = 1;
            $this->orderInfoModel->self_order_status = 1;
            $this->orderInfoModel->save();
        } catch (Throwable $exception) {
            $this->refundToTradeCenterForPayCallbackFailed(
                $this->orderInfoModel,
                $exception->getMessage()
            );
        }
    }
}
