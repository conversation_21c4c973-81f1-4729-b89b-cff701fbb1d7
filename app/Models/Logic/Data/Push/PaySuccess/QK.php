<?php
// 汽开

namespace App\Models\Logic\Data\Push\PaySuccess;


use Request\QK as QKRequest;
use Throwable;


class QK extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019-07-12 15:31
     */
    public function handle()
    {
        try {
            QKRequest::handle("dispense.oilMerPushOrderState", [
                "oilPlatSerial" => $this->orderInfoModel->platform_order_id,
                "payTime"       => date('Y-m-d H:i:s'),
                "orderStatus"   => "0",
            ]);
            $this->orderInfoModel->platform_order_status = 1;
            $this->orderInfoModel->self_order_status = 1;
            $this->orderInfoModel->save();
        } catch (Throwable $exception) {
            $this->refundToTradeCenterForPayCallbackFailed($this->orderInfoModel,
                $exception->getMessage());
        }
    }
}
