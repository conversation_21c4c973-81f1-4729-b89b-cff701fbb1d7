<?php


namespace App\Models\Logic\Data\Push\AutonomousOrder;


use App\Models\Data\OrderAssoc as OrderAssocData;
use Throwable;

class SC extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019-07-12 15:31
     */
    public function handle()
    {
        OrderAssocData::insert(1, 2, $this->data['id'],
            '', $this->name_abbreviation, '', "", json_encode([
                "owner" => $this->data
            ]));
        responseFormat(0, [
            'order_id'         => $this->data['id'],
            'secondaryPayment' => true,
        ], true);
    }
}
