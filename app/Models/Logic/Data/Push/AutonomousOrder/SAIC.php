<?php


namespace App\Models\Logic\Data\Push\AutonomousOrder;


use App\Jobs\QueryOrderToTradeCenter;
use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Data\StationUniqueMapping as StationUniqueMappingData;
use Illuminate\Support\Facades\Queue;
use Request\SAIC as SAICRequest;
use Throwable;

class SAIC extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/11/7 10:04 上午
     */
    public function handle()
    {
        $orderModel = OrderAssocData::insert(
            1,
            2,
            $this->data['id'],
            '',
            explode('_', $this->name_abbreviation)[0],
            '',
            "",
            json_encode([
                "owner" => $this->data
            ])
        );
        if (!$this->data['pushExtends'] or !isset($this->data['pushExtends']['price'])) {
            $this->refundToTradeCenterForPayCallbackFailed($orderModel, 4120007);
        }
        if (empty($this->data['oil_type_id'])) {
            $oilNo = '';
        } else {
            $oilNo = str_replace(
                array_flip(config('oil.oil_type'))[$this->data['oil_name_id']] ?? '',
                '',
                array_flip(config('oil.oil_no'))[$this->data['oil_type_id']] ?? ''
            );
        }
        $stationPriceInfo = StationUniqueMappingData::getStationPriceByStationId([
            'station_id' => $this->data['station_id'],
            [
                'field'    => 'oil_type',
                'operator' => '=',
                'value'    => array_flip(config('oil.oil_type'))[$this->data['oil_name_id']],
            ],
            [
                'field'    => 'oil_no',
                'operator' => '=',
                'value'    => $oilNo,
            ],
            [
                'field'    => 'oil_level',
                'operator' => '=',
                'value'    => '',
            ],
            [
                'field'    => 'platform_code',
                'operator' => '=',
                'value'    => $this->data['auth_info_data']['role_code'],
            ]
        ]);
        if (empty($stationPriceInfo)) {
            $this->refundToTradeCenterForPayCallbackFailed($orderModel, 5000027);
        }
        if ($this->data['pushExtends']['price'] != $stationPriceInfo['sale_price']) {
            $this->refundToTradeCenterForPayCallbackFailed($orderModel, 5000032);
        }
        try {
            $skuInfo = json_decode($stationPriceInfo['extends'], true)[$oilNo .
                                                                       array_flip(
                                                                           config('oil.oil_type')
                                                                       )[$this->data['oil_name_id']]];
            $loseLen = 28 - strlen($this->data['id']);
            $outOrderId = '0505' . $this->data['id'] . mt_rand(
                    str_pad("1", $loseLen, "0"),
                    str_pad("", $loseLen, "9")
                );
            $requestData = [
                'outOrderId' => $outOrderId,
                'timesTamp'  => date("YmdHis"),
                'petrolId'   => $this->data['app_station_id'],
                'amount'     => $this->data['supplier_money'],
                'oilType'    => $skuInfo['type'],
                'modeName'   => $skuInfo['fuelName'],
                'oilGun'     => "1",
                'notifyUrl'  => AuthConfigData::getAuthConfigValByName('APP_DOMAIN') . "saic/trade/pay/"
                                . explode('_', $this->name_abbreviation)[1],
            ];
            $thirdAuthInfo = json_decode(
                                 AuthConfigData::getAuthConfigValByName(
                                     "SAIC_APP_AUTH_" .
                                     $this->data['pcode']
                                 ),
                                 true
                             ) ?? [];
            $realNameAbbreviation = (json_decode(
                                         AuthConfigData::getAuthConfigValByName(
                                             'SAIC_APP_ID_TO_SUPPLIER_IDENTIFIER_MAPPING'
                                         ),
                                         true
                                     ) ??
                                     [])[$thirdAuthInfo['app_id'] ?? ''] ?? '';
            if (!$path = AuthConfigData::getAuthConfigValByName(
                "SAIC_" . strtoupper($realNameAbbreviation) .
                "_ORDER_PUSH_API_PATH"
            )) {
                $path = AuthConfigData::getAuthConfigValByName("SAIC_ORDER_PUSH_API_PATH");
            }
            $result = SAICRequest::handle($thirdAuthInfo, $path, $requestData);
            $orderModel->platform_order_id = $outOrderId;
            $orderModel->extend = json_encode([
                'owner'  => $this->data,
                'either' => $result['data'],
            ]);
            $orderModel->save();
            Queue::later(
                30,
                new QueryOrderToTradeCenter([
                    'name_abbreviation' => 'saic',
                    'platform_order_id' => $outOrderId
                ]),
                "",
                "adapter_deal_trade"
            );
            responseFormat(0, [
                'order_id'         => $outOrderId,
                'secondaryPayment' => true,
            ], true);
        } catch (Throwable $exception) {
            $this->refundToTradeCenterForPayCallbackFailed($orderModel, $exception->getMessage());
        }
    }
}
