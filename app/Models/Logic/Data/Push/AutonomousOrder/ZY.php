<?php


namespace App\Models\Logic\Data\Push\AutonomousOrder;


use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Data\StationUniqueMapping as StationUniqueMappingData;
use Request\ZY as ZYRequest;
use Throwable;
use Tool\Alarm\FeiShu;


class ZY extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/11/7 10:04 上午
     */
    public function handle()
    {
        try {

            $orderModel = OrderAssocData::insert(1, 2,
                $this->data['id'], '', $this->name_abbreviation, '', "",
                json_encode([
                    "owner" => $this->data
                ]));
            if (!$this->data['pushExtends'] or !isset($this->data['pushExtends']['price']) or
                !isset($this->data['pushExtends']['priceGun'])) {

                $this->refundToTradeCenterForPayCallbackFailed($orderModel, config("error.4120007"));
            }

            if (empty($this->data['oil_type_id'])) {

                $oilNameId = '';
            } else {

                $oilNameId = config('oil.oil_no_simple')[$this->data['oil_type_id']];
            }
            $stationPriceInfo = StationUniqueMappingData::getStationPriceByStationId([
                'station_id' => $this->data['station_id'],
                [
                    'field'    => 'oil_type',
                    'operator' => '=',
                    'value'    => array_flip(config('oil.oil_type'))[$this->data['oil_name_id']],
                ],
                [
                    'field'    => 'oil_no',
                    'operator' => '=',
                    'value'    => $oilNameId,
                ],
                [
                    'field'    => 'oil_level',
                    'operator' => '=',
                    'value'    => array_flip(config("oil.oil_level_arabic_number"))[$this->data['oil_level_id']],
                ],
                [
                    'field'    => 'platform_code',
                    'operator' => '=',
                    'value'    => $this->data['auth_info_data']['role_code'],
                ]
            ]);
            if (empty($stationPriceInfo)) {

                $this->refundToTradeCenterForPayCallbackFailed($orderModel, 5000027);
            }
            if ($this->data['pushExtends']['price'] != $stationPriceInfo['sale_price']) {

                $this->refundToTradeCenterForPayCallbackFailed($orderModel, 5000032);
            }
            if ($this->data['pushExtends']['priceGun'] != $stationPriceInfo['listing_price']) {

                $this->refundToTradeCenterForPayCallbackFailed($orderModel, 5000032);
            }
            if (bccomp($stationPriceInfo['sale_price'], $stationPriceInfo['listing_price']) == 1) {

                $this->refundToTradeCenterForPayCallbackFailed($orderModel, 5000029);
            }

            if (!isset($this->data['pushExtends']['amountGun'])) {

                $this->refundToTradeCenterForPayCallbackFailed($orderModel, 5000030);
            }
            $skuCode = json_decode($stationPriceInfo['extends'], true)[$oilNameId .
            array_flip(config('oil.oil_type'))[$this->data['oil_name_id']] .
            array_flip(config("oil.oil_level_arabic_number"))[$this->data['oil_level_id']] .
            "_skuCode"];
            $requestData = [
                'stationId'     => $this->data['app_station_id'],
                'thirdSubCode'  => $skuCode == 'T100L01' ? 'G7-LNG' : 'G7',
                'litre'         => bcdiv($this->data['supplier_money'], $this->data["supplier_price"],
                    8),
                'thirdPrice'    => $this->data["supplier_price"],
                'paymentAmount' => $this->data['supplier_money'],
                'oilAmount'     => $this->data['pushExtends']['amountGun'],
                'skuCode'       => $skuCode,
                'truckerSn'     => $this->data['vice_no'],
                'thirdOrderSn'  => $this->data['id'],
            ];
            $result = ZYRequest::handle('order.channelDokey', $requestData);
            $orderModel->platform_order_id = $result["data"]["zywOrderSn"];
            $orderModel->platform_order_status = 1;
            $orderModel->save();
            responseFormat(0, [
                'order_id'         => $orderModel->platform_order_id,
                'secondaryPayment' => true,
            ], true);
        } catch (Throwable $exception) {

            if ($exception->getCode() == 5000999) {

                (new FeiShu())->getSecondaryCertificateFailed([
                    "title"              => config("error.5000109"),
                    "platform_name"      => DockingPlatformInfoData::getFieldsByNameAbbreviation(
                        $this->name_abbreviation, 'platform_name', '')['platform_name'],
                    "reason"             => $exception->getMessage(),
                    "station_name"       => $this->data["trade_place"],
                    "oil"                => $this->data["oil_name"],
                    "price"              => $this->data["supplier_price"],
                    "oil_num"            => $this->data["trade_num"],
                    "money"              => $this->data["supplier_money"],
                    "plate_number"       => $this->data["truck_no"],
                    "failed_description" => "老吕订单状态：支付失败",
                    "driver_phone"       => $this->data["drivertel"],
                ]);
                throw $exception;
            }

            throw $exception;
        }
    }
}
