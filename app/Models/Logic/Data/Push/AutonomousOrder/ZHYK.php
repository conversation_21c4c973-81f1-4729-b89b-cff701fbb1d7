<?php


namespace App\Models\Logic\Data\Push\AutonomousOrder;


use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Data\StationUniqueMapping as StationUniqueMappingData;
use Request\ZHYK as ZHYKRequest;
use Throwable;
use Tool\Alarm\FeiShu;


class ZHYK extends ThirdParty
{
    /**
     * @return void
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2021/3/10 11:14 上午
     */
    public function handle()
    {
        $orderInfoModel = OrderAssocData::insert(
            1,
            2,
            $this->data['id'],
            '',
            $this->name_abbreviation,
            '',
            "",
            json_encode($this->data)
        );
        if (!$this->data['pushExtends'] or !isset($this->data['pushExtends']['price']) or
            !isset($this->data['pushExtends']['priceGun'])) {
            $this->refundToTradeCenterForPayCallbackFailed($orderInfoModel, "油枪、应收单价不能为空");
            return;
        }
        if (!isset($this->data['pushExtends']['amountGun'])) {
            $this->refundToTradeCenterForPayCallbackFailed($orderInfoModel, '油枪金额不能为空');
            return;
        }
        if (empty($this->data['oil_type_id'])) {
            $oilNo = '';
        } else {
            $oilNo = str_replace(
                array_flip(config('oil.oil_type'))[$this->data['oil_name_id']] ?? '',
                '',
                array_flip(config('oil.oil_no'))[$this->data['oil_type_id']] ?? ''
            );
        }
        $stationPriceInfo = StationUniqueMappingData::getStationPriceByStationId([
            'station_id' => $this->data['station_id'],
            [
                'field'    => 'oil_type',
                'operator' => '=',
                'value'    => array_flip(config('oil.oil_type'))[$this->data['oil_name_id']],
            ],
            [
                'field'    => 'oil_no',
                'operator' => '=',
                'value'    => $oilNo,
            ],
            [
                'field'    => 'oil_level',
                'operator' => '=',
                'value'    => array_flip(config("oil.oil_level"))[$this->data['oil_level_id']],
            ],
            [
                'field'    => 'platform_code',
                'operator' => '=',
                'value'    => $this->data['auth_info_data']['role_code'],
            ]
        ]);
        if (empty($stationPriceInfo)) {
            $this->refundToTradeCenterForPayCallbackFailed($orderInfoModel, '站点不存在');
            return;
        }
        if ($this->data['pushExtends']['price'] != $stationPriceInfo['sale_price']) {
            $this->refundToTradeCenterForPayCallbackFailed(
                $orderInfoModel,
                "结算价异常,无法下单。传入价格：" .
                "{$this->data['pushExtends']['price']}，当前价格：{$stationPriceInfo['sale_price']}"
            );
            return;
        }
        //判断车牌号是否为空,为空填充约定的默认车牌号
        $genOrderData = [
            'openId'      => $this->data['vice_no'],
            "outTradeNo"  => $this->data['id'],
            "stationCode" => $stationPriceInfo['station_id'],
            "oilCode"     => array_flip(
                                 config('oil.oil_mapping.zhyk')
                             )["{$this->data['oil_name_id']}_{$this->data['oil_type_id']}_{$this->data['oil_level_id']}"] ?? '',
            "gunNo"       => $this->data['pushExtends']['gunNumber'],
            "totalFee"    => (int)bcmul($this->data['pushExtends']['amountGun'], 100),
            "payFee"      => (int)bcmul($this->data['supplier_money'], 100),
            "discountFee" => (int)bcmul(
                bcsub($this->data['pushExtends']['amountGun'], $this->data['supplier_money'], 2),
                100
            ),
        ];

        try {
            $orderData = ZHYKRequest::handle(
                "order/unifiedorder.action",
                $genOrderData
            );
            $orderInfoModel->platform_order_status = 1;
            $orderInfoModel->platform_order_id = $orderData['data']['tradeNo'];
            $orderInfoModel->save();
        } catch (Throwable $exception) {
            (new FeiShu())->writtenOffFailed([
                "title"        => DockingPlatformInfoData::getFieldsByNameAbbreviation(
                        $this->name_abbreviation,
                        'platform_name',
                        ''
                    )['platform_name'] . "站点支付失败",
                "reason"       => $exception->getMessage(),
                "station_name" => $this->data["trade_place"],
                "oil"          => $this->data["oil_name"],
                "price"        => $this->data["supplier_price"],
                "oil_num"      => $this->data["trade_num"],
                "money"        => $this->data["supplier_money"],
                "plate_number" => $this->data["truck_no"],
                "driver_phone" => $this->data["drivertel"],
            ]);
            if ($exception->getCode() == 5000999) {
                $this->refundToTradeCenterForPayCallbackFailed($orderInfoModel, $exception->getMessage());
            }
        }
    }
}
