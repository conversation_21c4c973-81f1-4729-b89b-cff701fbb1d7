<?php


namespace App\Models\Logic\Data\Push\AutonomousOrder;


use App\Models\Data\OrderAssoc as OrderAssocData;
use Exception;
use Request\ZDC as ZDCRequest;
use Throwable;

class ZDC extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 15:31
     */
    public function handle()
    {
        $orderInfoModel = OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'self_order_id',
                'operator' => '=',
                'value'    => $this->data['id'],
            ],
            [
                'field'    => 'platform_name',
                'operator' => '=',
                'value'    => $this->name_abbreviation,
            ],
        ], [
            'id',
            'platform_order_id',
            'self_order_id',
            'extend',
        ], true, true);
        if (!$orderInfoModel) {
            throw new Exception("", 5000018);
        }
        try {
            ZDCRequest::handle("shell/services/payOrder", 'post', [
                "outTradeNo" => $orderInfoModel->self_order_id,
                "orderId"    => $orderInfoModel->platform_order_id,
            ]);
            $orderInfoModel->self_order_status = 1;
            $orderInfoModel->platform_order_status = 2;
            $extend = json_decode($orderInfoModel->extend, true);
            $extend['owner']['pushExtends'] = json_decode($extend['owner']['pushExtends'], true);
            $this->data = array_merge($extend['owner'], $this->data);
            $this->data['pushExtends'] = array_merge($extend['owner']['pushExtends'], $this->data['pushExtends']);
            $orderInfoModel->extend = json_encode([
                "owner"  => $this->data,
                'either' => $extend['either'],
            ]);
            $orderInfoModel->save();
        } catch (Throwable $exception) {
            $this->refundToTradeCenterForPayCallbackFailed(
                $orderInfoModel,
                $exception->getMessage()
            );
        }
        responseFormat(0, [
            'order_id'         => $orderInfoModel->platform_order_id,
            'secondaryPayment' => true,
        ], true);
    }
}
