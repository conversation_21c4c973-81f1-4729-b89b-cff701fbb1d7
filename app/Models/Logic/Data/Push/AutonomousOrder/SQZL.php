<?php


namespace App\Models\Logic\Data\Push\AutonomousOrder;


use App\Jobs\QueryOrderToTradeCenter;
use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Data\StationUniqueMapping as StationUniqueMappingData;
use Illuminate\Support\Facades\Queue;
use Request\SQZL as SQZLRequest;
use Throwable;

class SQZL extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019/11/7 10:04 上午
     */
    public function handle()
    {
        $orderModel = OrderAssocData::insert(
            1,
            2,
            $this->data['id'],
            '',
            $this->name_abbreviation,
            '',
            "",
            json_encode([
                "owner" => $this->data
            ])
        );
        if (!$this->data['pushExtends'] or !isset($this->data['pushExtends']['price'])) {
            $this->refundToTradeCenterForPayCallbackFailed($orderModel, config("error.4120007"));
        }
        if (empty($this->data['oil_type_id'])) {
            $oilNo = '';
        } else {
            $oilNo = str_replace(
                array_flip(config('oil.oil_type'))[$this->data['oil_name_id']] ?? '',
                '',
                array_flip(config('oil.oil_no'))[$this->data['oil_type_id']] ?? ''
            );
        }
        $stationPriceInfo = StationUniqueMappingData::getStationPriceByStationId([
            'station_id' => $this->data['station_id'],
            [
                'field'    => 'oil_type',
                'operator' => '=',
                'value'    => array_flip(
                                  config(
                                      'oil.oil_type'
                                  )
                              )[$this->data['oil_name_id']],
            ],
            [
                'field'    => 'oil_no',
                'operator' => '=',
                'value'    => $oilNo,
            ],
            [
                'field'    => 'oil_level',
                'operator' => '=',
                'value'    => array_flip(config("oil.oil_level"))[$this->data['oil_level_id']],
            ],
            [
                'field'    => 'platform_code',
                'operator' => '=',
                'value'    => $this->data['auth_info_data']['role_code'],
            ]
        ]);
        if (empty($stationPriceInfo)) {
            $this->refundToTradeCenterForPayCallbackFailed($orderModel, 5000027);
        }
        if ($this->data['pushExtends']['price'] != $stationPriceInfo['sale_price']) {
            $this->refundToTradeCenterForPayCallbackFailed($orderModel, 5000032);
        }
        try {
            $requestData = [
                'orderId'     => $this->data['id'],
                'stationId'   => $this->data['app_station_id'],
                'orderAmount' => $this->data['pushExtends']['amountGun'],
                'fuelNo'      => array_flip(
                                     config(
                                         "oil.oil_mapping.sqzl.mapping"
                                     )
                                 )[$this->data['oil_name_id'] . "_" .
                                   $this->data['oil_type_id'] . "_" .
                                   $this->data['oil_level_id']] ?? '',
                'plateCode'   => AuthConfigData::getAuthConfigValByName('SQZL_PLATE_CODE'),
                'driverName'  => AuthConfigData::getAuthConfigValByName('SQZL_DRIVER_NAME'),
                'driverPhone' => AuthConfigData::getAuthConfigValByName('SQZL_DRIVER_PHONE'),
                'companyCode' => AuthConfigData::getAuthConfigValByName('SQZL_CHANNEL_CODE'),
            ];
            $result = SQZLRequest::handle('order/orderPay', $requestData);
            $orderModel->platform_order_id = $result['uniqueStr'];
            $orderModel->extend = json_encode([
                'owner'  => $this->data,
                'either' => $result,
            ]);
            $orderModel->save();
            Queue::later(
                2,
                new QueryOrderToTradeCenter([
                    'name_abbreviation' => 'sqzl',
                    'platform_order_id' => $result['uniqueStr']
                ]),
                "",
                "adapter_deal_trade"
            );
            responseFormat(0, [
                'order_id'         => $this->data['id'],
                'secondaryPayment' => true,
            ], true);
        } catch (Throwable $exception) {
            $this->refundToTradeCenterForPayCallbackFailed($orderModel, $exception->getMessage());
        }
    }
}
