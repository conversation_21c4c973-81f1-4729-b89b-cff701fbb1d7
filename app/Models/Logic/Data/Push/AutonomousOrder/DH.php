<?php


namespace App\Models\Logic\Data\Push\AutonomousOrder;


use App\Jobs\QueryOrderToTradeCenter;
use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\OrderAssoc as OrderAssocData;
use Illuminate\Support\Facades\Queue;
use Request\DH as DHRequest;
use Throwable;


class DH extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 15:31
     */
    public function handle()
    {
        $orderAssocDao = OrderAssocData::insert(
            1,
            2,
            $this->data['id'],
            '',
            $this->name_abbreviation,
            '',
            "",
            json_encode([
                "owner" => $this->data
            ])
        );
        try {
            $outOrderId = AuthConfigData::getAuthConfigValByName("DH_APP_CHANNEL") . $this->data['id'];
            $response = DHRequest::handle('/createDistributeOrder', [
                'outOrderId' => $outOrderId,
                'asn'        => $this->data['master_card_no'],
                'distribute' => 1,
                'mapStr'     => "{$this->data['supplementary_card_no']}=" . ((int)bcmul(
                        $this->data['pushExtends']['amountGun'],
                        100,
                        0
                    )) . ";",
                'cardUserId' => $this->data['card_user_id'],
            ]);
            $orderAssocDao->platform_order_id = $response['data']['orderId'];
            $orderAssocDao->extend = json_encode([
                "owner"  => $this->data,
                "either" => $response,
            ]);
            $orderAssocDao->save();
            DHRequest::handle('/submitDistributeOrder', [
                'outOrderId' => $outOrderId,
                'asn'        => $this->data['master_card_no'],
                'cardUserId' => $this->data['card_user_id'],
                'notifyUrl'  => AuthConfigData::getAuthConfigValByName('APP_DOMAIN') . "dh/trade/pay",
            ]);
            Queue::later(
                60,
                new QueryOrderToTradeCenter([
                    'name_abbreviation' => 'dh',
                    'platform_order_id' => $response['data']['orderId']
                ]),
                "",
                "adapter_deal_trade"
            );
            responseFormat(0, [
                'order_id'         => $this->data['id'],
                'secondaryPayment' => true,
            ], true);
        } catch (Throwable $throwable) {
            $this->refundToTradeCenterForPayCallbackFailed($orderAssocDao, $throwable->getMessage());
        }
    }
}
