<?php

namespace App\Models\Logic\Data\Push\AutonomousOrder;

use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\OrderAssoc as OrderAssocData;
use Request\JHCX as JHCXRequest;
use Throwable;

class JHCX extends ThirdParty
{
    /**
     * @return void
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2022/4/14 18:47
     */
    public function handle()
    {
        $orderModel = OrderAssocData::insert(1, 2, $this->data['id'],
            '', $this->name_abbreviation, '', "",
            json_encode([
                "owner" => $this->data
            ]));
        if (!isset($this->data['pushExtends']['amountGun'])) {

            $this->refundToTradeCenterForPayCallbackFailed($orderModel, '油机金额不正确');
        }
        if (bccomp($this->data['pushExtends']['amountGun'], 10.00, 2) < 0 or
            bccomp($this->data['pushExtends']['amountGun'], 9999.00, 2) >= 0) {
            $this->refundToTradeCenterForPayCallbackFailed($orderModel, "4120504");
        }
        $redisConn = app('redis');
        $lockKey = "";
        $code = "";
        try {
            do {

                $code = getMillisecond();
                $lockKey = "{$this->name_abbreviation}_coupon_code_$code";
                if (!$redisConn->set($lockKey, $code, 'ex', 10, 'nx')) {

                    $code = 0;
                }
            } while ($code < 0);
            $discountAccountScale = AuthConfigData::getAuthConfigValByName("JHCX_DISCOUNT_ACCOUNT_SCALE");
            $couponInfo = JHCXRequest::handle('/Api/DiyCard/code_up', [
                'code'         => $code,
                'amount'       => $this->data['pushExtends']['amountGun'],
                'code_date'    => (int)AuthConfigData::getAuthConfigValByName("JHCX_COUPON_EXPIRE"),
                'out_trade_no' => $this->data['id'],
                'card_id'      => AuthConfigData::getAuthConfigValByName("JHCX_ACCOUNT_ID"),
                'discount_fee' => round(bcmul($this->data['pushExtends']['amountGun'], $discountAccountScale, 3)),
            ]);
            $orderModel->platform_order_id = $couponInfo['new_code'];
            $orderModel->extend = json_encode([
                'owner'  => $this->data,
                'either' => [
                    'zk'   => round(bcmul($this->data['pushExtends']['amountGun'], $discountAccountScale, 3)),
                    'code' => $code,
                ],
            ]);
            $orderModel->save();
            releaseRedisLock($redisConn, $lockKey, $code);
            responseFormat(0, [
                'order_id'         => $this->data['id'],
                'secondaryPayment' => true,
            ], true);
        } catch (Throwable $throwable) {

            releaseRedisLock($redisConn, $lockKey, $code);
            $this->refundToTradeCenterForPayCallbackFailed($orderModel, $throwable->getMessage());
        }
    }
}
