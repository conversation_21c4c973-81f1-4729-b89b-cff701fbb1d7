<?php


namespace App\Models\Logic\Data\Push\AutonomousOrder;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Data\StationUniqueMapping as StationUniqueMappingData;
use Exception;
use Illuminate\Redis\RedisManager;
use Predis\Client;
use Request\DT as DTRequest;
use Throwable;
use Tool\Alarm\FeiShu;


class DT extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/11/7 10:04 上午
     */
    public function handle()
    {
        $orderModel = null;
        $redis = app('redis');
        $makeOrderNeedPhone = json_decode(
            AuthConfigData::getAuthConfigValByName(
                "NEED_PHONE_POOL_ORG_LIST"
            ),
            true
        );
        $phonePoolName = $this->name_abbreviation . "_phone_pool_" . date("Y_m_d");
        if (in_array($this->data['org_code'], $makeOrderNeedPhone)) {
            $this->data['drivertel'] = $this->getPhoneFromPool($redis, $phonePoolName);
            if (empty($this->data['drivertel'])) {
                (new FeiShu())->writtenOffFailed([
                    "title"        => DockingPlatformInfoData::getFieldsByNameAbbreviation(
                            explode('_', $this->name_abbreviation)[0],
                            'platform_name',
                            ''
                        )['platform_name'] . "站点支付失败",
                    "reason"       => "【同一ID每天最多下单3次】",
                    "station_name" => $this->data["trade_place"],
                    "oil"          => $this->data["oil_name"],
                    "price"        => $this->data["supplier_price"],
                    "oil_num"      => $this->data["trade_num"],
                    "money"        => $this->data["supplier_money"],
                    "plate_number" => $this->data["truck_no"],
                    "driver_phone" => '',
                ]);
                throw new Exception("暂无可用于下单的虚拟手机号");
            }
        }
        try {
            if (!$userInfo = json_decode(
                $redis->hget("dt_user_mapping", $this->data['drivertel']),
                true
            )) {
                $userInfo = DTRequest::handle("user/thirdUserRegister", [
                    'mobile'    => $this->data['drivertel'],
                    'outUserId' => md5($this->data['drivertel']),
                ]);
                $redis->hset("dt_user_mapping", $this->data['drivertel'], json_encode($userInfo));
            }
            $orderModel = OrderAssocData::insert(
                1,
                2,
                $this->data['id'],
                '',
                explode('_', $this->name_abbreviation)[0],
                '',
                "",
                json_encode([
                    "owner" => $this->data
                ])
            );
            if (empty($this->data['oil_type_id'])) {
                $oilNo = '';
            } else {
                $oilNo = config('oil.oil_no_simple')[$this->data['oil_type_id']];
            }
            $oilType = array_flip(config('oil.oil_type'))[$this->data['oil_name_id']];
            $oilLevel = array_flip(config("oil.oil_level"))[$this->data['oil_level_id']];
            $stationPriceInfo = StationUniqueMappingData::getStationPriceByStationId([
                'station_id' => $this->data['station_id'],
                [
                    'field'    => 'oil_type',
                    'operator' => '=',
                    'value'    => $oilType,
                ],
                [
                    'field'    => 'oil_no',
                    'operator' => '=',
                    'value'    => $oilNo,
                ],
                [
                    'field'    => 'oil_level',
                    'operator' => '=',
                    'value'    => $oilLevel,
                ],
                [
                    'field'    => 'platform_code',
                    'operator' => '=',
                    'value'    => $this->data['auth_info_data']['role_code'],
                ]
            ]);
            if (empty($stationPriceInfo)) {
                $this->refundToTradeCenterForPayCallbackFailed($orderModel, 5000027);
            }
            $requestData = [
                'shopSn'       => $this->data['app_station_id'],
                'userId'       => $userInfo['userId'],
                'authAmount'   => $this->data['pushExtends']['amountGun'],
                'payType'      => 6,
                'payNo' => AuthConfigData::getAuthConfigValByName("DT_PAY_NO"),
                'outOrderId'   => $this->data['id'],
                'oilCode'      => array_flip(config("oil.oil_mapping.dt"))[$this->data['oil_name_id'] .
                                                                           '_' .
                                                                           $this->data['oil_type_id'] .
                                                                           '_' .
                                                                           $this->data['oil_level_id']],
                'notifyUrl'    => AuthConfigData::getAuthConfigValByName("APP_DOMAIN") .
                                  AuthConfigData::getAuthConfigValByName("DT_ORDER_CALLBACK_PATH"),
                'userCouponId' => AuthConfigData::getAuthConfigValByName("DT_COUPON_ID"),
            ];
            $result = DTRequest::handle('authOrder/createAuthOrder', $requestData);
            $orderModel->platform_order_id = $result["orderId"];
            $orderModel->extend = json_encode([
                'owner'  => $this->data,
                'either' => $result,
            ]);
            $orderModel->save();
            responseFormat(0, [
                'order_id'         => $orderModel->platform_order_id,
                'secondaryPayment' => true,
            ], true);
        } catch (Throwable $exception) {
            if (in_array($this->data['org_code'], $makeOrderNeedPhone)) {
                $this->releasePhoneToPool($redis, $phonePoolName, $this->data['drivertel']);
            }
            if ($orderModel) {
                $this->refundToTradeCenterForPayCallbackFailed($orderModel, $exception->getMessage());
            }
            throw $exception;
        }
    }

    /**
     * @throws Exception
     */
    public function getPhoneFromPool($redis, string $poolName)
    {
        if (!($redis instanceof Client or $redis instanceof RedisManager)) {
            throw new Exception("The redis instance is invalid");
        }
        $newPhonePool = json_decode(
            AuthConfigData::getAuthConfigValByName(
                "DT_PHONE_POOL"
            ),
            true
        );
        $oilPhonePool = array_values($redis->zrange($poolName, 0, -1));
        $poolExist = $redis->exists($poolName);
        if (!$poolExist or $newPhonePool != $oilPhonePool) {
            $phonePool = array_diff($newPhonePool, $oilPhonePool);
            $delPhonePool = array_diff($oilPhonePool, $newPhonePool);
            if ($poolExist and $delPhonePool) {
                $redis->zrem($poolName, $delPhonePool);
            }
            $phonePoolData = [];
            if ($phonePool) {
                foreach ($phonePool as $p) {
                    $phonePoolData[$p] = 0;
                }
                $redis->zadd($poolName, $phonePoolData);
                $redis->expire(
                    $poolName,
                    strtotime(
                        date(
                            "Y-m-d  00:05:00",
                            strtotime("+1 day")
                        )
                    ) - time()
                );
            }
        }
        return $redis->eval(
            "local p = redis.call('ZRANGEBYSCORE', KEYS[1], 0, 2)
if  next(p) ~= nil then
    local r = p[math.random(#p)]
    redis.call('ZINCRBY', KEYS[1], 1, r)
    return r
end return 0",
            1,
            $poolName
        );
    }

    /**
     * @throws Exception
     */
    public function releasePhoneToPool($redis, string $poolName, $phone)
    {
        if (!($redis instanceof Client or $redis instanceof RedisManager)) {
            throw new Exception("The redis instance is invalid");
        }
        $redis->eval(
            "if  redis.call('ZSCORE', KEYS[1], ARGV[1]) > '0' and redis.call('ZSCORE', KEYS[1], ARGV[1]) < '4' then
    return redis.call('ZINCRBY', KEYS[1], -1, ARGV[1])
end",
            1,
            $poolName,
            $phone
        );
    }
}
