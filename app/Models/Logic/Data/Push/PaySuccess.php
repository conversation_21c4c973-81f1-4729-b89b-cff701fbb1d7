<?php
/**
 * Created by PhpStor<PERSON>.
 * User: zdl
 * Date: 2019-01-28
 * Time: 15:22
 */

namespace App\Models\Logic\Data\Push;


use App\Models\Data\AuthInfo;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Base;
use App\Models\Logic\Data\Push\PaySuccess\BDT;
use App\Models\Logic\Data\Push\PaySuccess\CZB;
use App\Models\Logic\Data\Push\PaySuccess\DIANDI;
use App\Models\Logic\Data\Push\PaySuccess\HYT;
use App\Models\Logic\Data\Push\PaySuccess\JH;
use App\Models\Logic\Data\Push\PaySuccess\JQ;
use App\Models\Logic\Data\Push\PaySuccess\MJ;
use App\Models\Logic\Data\Push\PaySuccess\QK;
use App\Models\Logic\Data\Push\PaySuccess\SP;
use App\Models\Logic\Data\Push\PaySuccess\WJY;
use App\Models\Logic\Data\Push\PaySuccess\XY;
use App\Models\Logic\Data\Push\PaySuccess\XYN;
use App\Models\Logic\Data\Push\PaySuccess\ZY;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Throwable;


class PaySuccess extends Base
{
    private $jobMapping = [
        'czb'   => CZB::class,
        'bdt'   => BDT::class,
        'dd'    => DIANDI::class,
        'zy'    => ZY::class,
        'sp'    => SP::class,
        'xy'    => XY::class,
        'hyt'   => HYT::class,
        'zyLng' => ZY::class,
        'wjy'   => WJY::class,
        'xyn'   => XYN::class,
        'mj'    => MJ::class,
        'jq'    => JQ::class,
        'qk'    => QK::class,
        'jh'    => JH::class,
    ];

    /**
     * @var CZB
     */
    private $worker = null;

    /**
     * PaySuccess constructor.
     * @param array $parameter
     * @return JsonResponse|void
     * @throws Throwable
     */
    public function __construct(array $parameter)
    {
        parent::__construct($parameter);

        $orderInfoModel = OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'self_order_id',
                'operator' => '=',
                'value'    => $this->data['data']['order_id'],
            ],
        ], ["*"], false, true);
        if (!$orderInfoModel) {
            responseFormat(5000018, [], true);
        }
        $authInfo = AuthInfo::getAuthInfoByWhere([
            [
                'field'    => 'name_abbreviation',
                'operator' => 'in',
                'value'    => $orderInfoModel->pluck("platform_name")->toArray(),
            ],
            [
                'field'    => 'role',
                'operator' => '=',
                'value'    => 7,
            ],
        ], ['*']);
        if ($orderInfoModel->count() > 1) {
            foreach ($orderInfoModel as $v) {
                if ($v->platform_name == $authInfo['name_abbreviation']) {
                    $orderInfoModel = $v;
                    break;
                }
            }
        } else {
            $orderInfoModel = $orderInfoModel[0];
        }
        $this->data['data']['order_info_model'] = $orderInfoModel;
        if (!empty($authInfo)) {
            if (isset($this->jobMapping[$authInfo['name_abbreviation']])) {
                //把授权信息数据存入真实数据
                $this->data['data']['auth_info_data'] = $authInfo;
                $this->worker = (new $this->jobMapping[$authInfo['name_abbreviation']]($this->data));
            } else {
                return responseFormat(5000001, []);
            }
        } else {
            return responseFormat(5000001, []);
        }
    }

    /**
     * @return Response
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-08-22 18:02
     */
    public function handle(): Response
    {
        $this->worker->handle();
        return responseFormat(0, []);
    }
}
