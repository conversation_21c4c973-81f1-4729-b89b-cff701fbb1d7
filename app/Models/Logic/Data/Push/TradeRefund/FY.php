<?php
// 福佑

namespace App\Models\Logic\Data\Push\TradeRefund;


use Request\FY as FYRequest;
use Symfony\Component\HttpFoundation\Response;
use Throwable;


class FY extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 15:31
     */
    public function handle(): Response
    {
        FYRequest::handle("refundTrade", [
            "tradeId"           => $this->data['data']['tradeId'],
            "orderId"           => $this->data['data']['orderId'],
            "refundOrderNumber" => $this->data['data']['refundOrderNumber'],
        ], 'post');
        return responseFormat();
    }
}
