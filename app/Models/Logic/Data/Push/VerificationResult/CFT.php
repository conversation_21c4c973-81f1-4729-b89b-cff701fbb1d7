<?php


namespace App\Models\Logic\Data\Push\VerificationResult;


use Request\CFT as CFTRequest;
use Throwable;


class CFT extends ThirdParty
{
    /**
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2021/6/24 10:38 下午
     */
    public function handle()
    {
        CFTRequest::handle("/g7/order/confirm.action", [
            'order_id'            => $this->data['order_info_model']->platform_order_id,
            'verification_status' => $this->data['verification_status'],
        ]);
    }
}
