<?php
// 在途

namespace App\Models\Logic\Data\Push\VerificationResult;


use Request\ZT as ZTRequest;
use Throwable;


class ZT extends ThirdParty
{
    /**
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2021/6/24 10:38 下午
     */
    public function handle()
    {
        ZTRequest::handle("g7/order_verification_certificate", [
            'order_id'            => $this->data['order_info_model']->platform_order_id,
            'verification_status' => $this->data['verification_status'],
        ]);
    }
}
