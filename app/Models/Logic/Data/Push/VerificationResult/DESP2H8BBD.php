<?php


namespace App\Models\Logic\Data\Push\VerificationResult;


use Request\DESP as DESPRequest;
use Throwable;


class DESP2H8BBD extends ThirdParty
{
    /**
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2021/6/24 10:38 下午
     */
    public function handle()
    {
        DESPRequest::handle("open_api/v1/order/getPaymentCertificateResult", [
            'order_id'            => $this->data['order_info_model']->platform_order_id,
            'verification_status' => $this->data['verification_status'],
        ], explode("|", $this->name_abbreviation)[1]);
    }
}
