<?php


namespace App\Models\Logic\Data\Push\VerificationResult;


use Request\ZJ as ZJRequest;
use Throwable;


class ZJ extends ThirdParty
{
    /**
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2021/6/24 10:38 下午
     */
    public function handle()
    {
        $extend = json_decode($this->data['order_info_model']->extend, true);
        ZJRequest::handle("orderVerficiation", 'post', [
            'orderId' => $this->data['order_info_model']->platform_order_id,
            'verificationStatus' => $this->data['verification_status'],
            'driverId' => $extend['driver_id'],
        ]);
    }
}
