<?php

namespace App\Models\Logic\Data\Push\Refund;

use App\Models\Dao\OrderAssoc as OrderAssocDao;
use App\Models\Data\OrderAssoc as OrderAssocData;
use Exception;
use Request\TBJX as TBJXRequest;

class TBJX extends ThirdParty
{

    /**
     * @return void
     * @throws Exception
     */
    public function handle()
    {
        $orderInfo = OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'platform_name',
                'operator' => '=',
                'value'    => $this->name_abbreviation,
            ],
            [
                'field'    => 'self_order_id',
                'operator' => '=',
                'value'    => $this->data['data']['order_id'],
            ],
        ], [
            'id',
            'platform_order_status',
            'platform_order_id',
            'extend',
        ], true, true);
        if (!$orderInfo instanceof OrderAssocDao) {
            throw new Exception("", 5000024);
        }
        $extend = json_decode($orderInfo->extend, true);
        TBJXRequest::handle('update', [
            'serviceType' => 'CARD_EXPEND_REFUND_APPLY',
            'out_trade_no' => $this->data['data']['order_id'],
        ]);
        $extend['order_approve'] = [
            'id' => $this->data['data']['approve_id'],
        ];
        $orderInfo->extend = json_encode($extend);
        $orderInfo->save();
    }
}