<?php

namespace App\Models\Logic\Data\Push\Refund;

use App\Models\Dao\OrderAssoc as OrderAssocDao;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\OrderAssoc as OrderAssocData;
use Exception;
use Request\DT as DTRequest;
use Throwable;
use Tool\Alarm\FeiShu;

class DT extends ThirdParty
{

    /**
     * @return void
     * @throws Exception|Throwable
     */
    public function handle()
    {
        $orderInfo = OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'platform_name',
                'operator' => '=',
                'value'    => $this->name_abbreviation,
            ],
            [
                'field'    => 'self_order_id',
                'operator' => '=',
                'value'    => $this->data['data']['order_id'],
            ],
        ], [
            'id',
            'self_order_id',
            'platform_order_status',
            'platform_order_id',
            'extend',
        ], true, true);
        if (!$orderInfo instanceof OrderAssocDao) {
            throw new Exception("", 5000024);
        }
        if ($orderInfo->platform_order_status != 2) {
            throw new Exception("", 5000122);
        }
        if (empty($orderInfo->platform_order_id)) {
            throw new Exception("", 5000128);
        }
        $extend = json_decode($orderInfo->extend, true);
        $redis = app('redis');
        if (!$userInfo = json_decode(
            $redis->hget("dt_user_mapping", $extend['owner']['drivertel']),
            true
        )) {
            $userInfo = DTRequest::handle("user/thirdUserRegister", [
                'mobile'    => $extend['owner']['drivertel'],
                'outUserId' => md5($extend['owner']['drivertel']),
            ]);
            $redis->hset("dt_user_mapping", $extend['owner']['drivertel'], json_encode($userInfo));
        }
        try {
            DTRequest::handle('authOrder/cancelAuthOrder', [
                'orderId' => $orderInfo->platform_order_id,
                'userId'  => $userInfo['userId'],
            ]);
        } catch (Throwable $throwable) {
            (new FeiShu())->freeOrderMoney([
                'platform_name' => DockingPlatformInfoData::getPlatformNameByNameAbbreviation($this->name_abbreviation),
                'self_order_id' => $orderInfo->self_order_id,
                'reason'        => $throwable->getMessage(),
            ]);
            throw $throwable;
        }

        $orderInfo->self_order_status = 3;
        $orderInfo->platform_order_status = 3;
        $orderInfo->save();
    }
}