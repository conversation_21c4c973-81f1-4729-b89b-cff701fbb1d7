<?php

namespace App\Models\Logic\Data\Push\Refund;

use App\Models\Dao\OrderAssoc as OrderAssocDao;
use App\Models\Data\OrderAssoc as OrderAssocData;
use Exception;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Request\SHSX as SHSXRequest;

class SHSX extends ThirdParty
{

    /**
     * @return void
     * @throws Exception
     */
    public function handle()
    {
        $orderInfo = OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'platform_name',
                'operator' => '=',
                'value'    => $this->name_abbreviation,
            ],
            [
                'field'    => 'self_order_id',
                'operator' => '=',
                'value'    => $this->data['data']['order_id'],
            ],
        ], [
            'id',
            'platform_order_status',
            'platform_order_id',
            'extend',
        ], true, true);
        if (!$orderInfo instanceof OrderAssocDao) {
            throw new Exception("", 5000024);
        }
        if ($orderInfo->platform_order_status == 1) {
            throw new Exception("", 5000141);
        }
        $extend = json_decode($orderInfo->extend, true);
        SHSXRequest::handle('api/channel/refund', [
            'trade_no' => $this->data['data']['order_id'],
        ]);
        $extend['order_approve'] = [
            'id' => $this->data['data']['approve_id'],
        ];
        $orderInfo->extend = json_encode($extend);
        $orderInfo->save();
        $customerOrderInfo = OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'platform_name',
                'operator' => '!=',
                'value'    => $this->name_abbreviation,
            ],
            [
                'field'    => 'self_order_id',
                'operator' => '=',
                'value'    => $this->data['data']['order_id'],
            ],
        ], [
            'id',
        ], true, true);
        if ($customerOrderInfo) {
            return;
        }
        FOSS_ORDERRequest::handle('/api/services/v1/thirdOrder/pushMsg', [
            "msg_type"                   => 4,
            "pre_history_id" => (string)$this->data['data']['order_id'],
            "card_no"                    => (string)$extend['owner']['vice_no'],
            "message"                    => "",
            "platform_name_abbreviation" => $this->name_abbreviation,
        ]);
    }
}