<?php
/**
 * Created by PhpStor<PERSON>.
 * User: zdl
 * Date: 2019-01-28
 * Time: 15:22
 */

namespace App\Models\Logic\Data\Push;


use App\Jobs\PushOilForAd;
use App\Jobs\PushOilForAJSW;
use App\Jobs\PushOilForBBSP;
use App\Jobs\PushOilForBdtOrg;
use App\Jobs\PushOilForBQ;
use App\Jobs\PushOilForCfhy;
use App\Jobs\PushOilForCFT;
use App\Jobs\PushOilForCHTX;
use App\Jobs\PushOilForCIEC;
use App\Jobs\PushOilForCN;
use App\Jobs\PushOilForDDE;
use App\Jobs\PushOilForDESP2A6LA9;
use App\Jobs\PushOilForDESP2C637M;
use App\Jobs\PushOilForDESP2H8BBD;
use App\Jobs\PushOilForFy;
use App\Jobs\PushOilForGBDW;
use App\Jobs\PushOilForHG;
use App\Jobs\PushOilForHK;
use App\Jobs\PushOilForHLJH;
use App\Jobs\PushOilForHLL;
use App\Jobs\PushOilForHR;
use App\Jobs\PushOilForHSL;
use App\Jobs\PushOilForHYJY;
use App\Jobs\PushOilForHytOrg;
use App\Jobs\PushOilForHZ;
use App\Jobs\PushOilForJD;
use App\Jobs\PushOilForJDWC;
use App\Jobs\PushOilForJF;
use App\Jobs\PushOilForJTXY;
use App\Jobs\PushOilForKY;
use App\Jobs\PushOilForLF;
use App\Jobs\PushOilForLHYS;
use App\Jobs\PushOilForLT;
use App\Jobs\PushOilForMB;
use App\Jobs\PushOilForMK;
use App\Jobs\PushOilForMTL;
use App\Jobs\PushOilForMY;
use App\Jobs\PushOilForMYB;
use App\Jobs\PushOilForMYCF;
use App\Jobs\PushOilForPCKJ;
use App\Jobs\PushOilForQDMY;
use App\Jobs\PushOilForRq;
use App\Jobs\PushOilForRRS;
use App\Jobs\PushOilForRY;
use App\Jobs\PushOilForSFFY;
use App\Jobs\PushOilForSFSX;
use App\Jobs\PushOilForShengMan;
use App\Jobs\PushOilForSP;
use App\Jobs\PushOilForSQ;
use App\Jobs\PushOilForSTY;
use App\Jobs\PushOilForTC;
use App\Jobs\PushOilForWSY;
use App\Jobs\PushOilForWZYT;
use App\Jobs\PushOilForXC;
use App\Jobs\PushOilForXM;
use App\Jobs\PushOilForXYDS;
use App\Jobs\PushOilForYB;
use App\Jobs\PushOilForYBT;
use App\Jobs\PushOilForYgj;
use App\Jobs\PushOilForYGY;
use App\Jobs\PushOilForYlz;
use App\Jobs\PushOilForYxt;
use App\Jobs\PushOilForZey;
use App\Jobs\PushOilForZj;
use App\Jobs\PushOilForZjka;
use App\Jobs\PushOilForZLGX;
use App\Jobs\PushOilForZT;
use App\Jobs\PushOilForZTO;
use App\Jobs\PushOilForZZ;
use App\Jobs\PushOilForZZ_AH;
use App\Jobs\PushOilForZZ_BJ;
use App\Jobs\PushOilForZZ_TJ;
use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\AuthInfo;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Log\ReceiveLog as Log;
use App\Models\Data\StationPushSwitch as StationPushSwitchData;
use App\Models\Logic\Base;
use Request\FOSS_STATION as FOSS_STATIONRequest;
use Throwable;


class OilStationData extends Base
{
    public static $oilStationCacheForHubKey = 'oil_station_data_all';
    private       $workerMapping            = [
        'fy'            => PushOilForFy::class,
        'bdtOrg'        => PushOilForBdtOrg::class,
        'ygj'           => PushOilForYgj::class,
        'bbsp'          => PushOilForBBSP::class,
        'jd'            => PushOilForJD::class,
        'hljh'          => PushOilForHLJH::class,
        'lhys'          => PushOilForLHYS::class,
        'hg'            => PushOilForHG::class,
        'yb'            => PushOilForYB::class,
        'jdwc'          => PushOilForJDWC::class,
        'mk'            => PushOilForMK::class,
        'xc'            => PushOilForXC::class,
        'dde'           => PushOilForDDE::class,
        'tc'            => PushOilForTC::class,
        'sq'            => PushOilForSQ::class,
        'myb'           => PushOilForMYB::class,
        'cn'            => PushOilForCN::class,
        'sp|2036HG'     => PushOilForSP::class,
        'lf'            => PushOilForLF::class,
        'hyjy'          => PushOilForHYJY::class,
        'sp|200NYE0103' => PushOilForSP::class,
        'sp|200NYE0102' => PushOilForSP::class,
        'sp|2036HG02'   => PushOilForSP::class,
        'sp|203PMA'     => PushOilForSP::class,
        'sp|203GUN'     => PushOilForSP::class,
        'sp|203TC6'     => PushOilForSP::class,
        'sp|2046TU'     => PushOilForSP::class,
        'sp|20253S'     => PushOilForSP::class,
        'sp|204G5E'     => PushOilForSP::class,
        'sp|200F5803'   => PushOilForSP::class,
        'sp|204DN003'   => PushOilForSP::class,
        'sp|200RI9'     => PushOilForSP::class,
        'sp|204KGO'     => PushOilForSP::class,
        'sp|205CFZ'     => PushOilForSP::class,
        'sp|205CHR'     => PushOilForSP::class,
        'hytOrg'        => PushOilForHytOrg::class,
        'chtx'          => PushOilForCHTX::class,
        'pckj'          => PushOilForPCKJ::class,
        'rq'            => PushOilForRq::class,
        'zlgx'          => PushOilForZLGX::class,
        'xm'            => PushOilForXM::class,
        'jf'            => PushOilForJF::class,
        'sffy'          => PushOilForSFFY::class,
        'ajsw'          => PushOilForAJSW::class,
        'ygy'           => PushOilForYGY::class,
        'ciec'          => PushOilForCIEC::class,
        'zz'            => PushOilForZZ::class,
        'desp|2H8BBD'   => PushOilForDESP2H8BBD::class,
        'desp|2A6LA9'   => PushOilForDESP2A6LA9::class,
        'yxt'           => PushOilForYxt::class,
        'hr'            => PushOilForHR::class,
        'gbdw'          => PushOilForGBDW::class,
        'desp|2C637M'   => PushOilForDESP2C637M::class,
        'zj'            => PushOilForZj::class,
        'my'            => PushOilForMY::class,
        'hll'           => PushOilForHLL::class,
        'wsy'           => PushOilForWSY::class,
        'bq'            => PushOilForBQ::class,
        'zzah'          => PushOilForZZ_AH::class,
        'zzbj'          => PushOilForZZ_BJ::class,
        'zztj'          => PushOilForZZ_TJ::class,
        'cft'           => PushOilForCFT::class,
        'mb'            => PushOilForMB::class,
        'ad'            => PushOilForAd::class,
        'lt'            => PushOilForLT::class,
        'mtl'           => PushOilForMTL::class,
        'cfhy'          => PushOilForCfhy::class,
        'ylz'           => PushOilForYlz::class,
        'ylzZj'         => PushOilForYlz::class,
        'zjka'          => PushOilForZjka::class,
        'jtxy'          => PushOilForJTXY::class,
        'mycf'          => PushOilForMYCF::class,
        'zey'           => PushOilForZEY::class,
        'shengman'      => PushOilForShengMan::class,
        'ry'            => PushOilForRY::class,
        'ybt'           => PushOilForYBT::class,
        'hz'            => PushOilForHZ::class,
        'rrs'           => PushOilForRRS::class,
        'xyds'          => PushOilForXYDS::class,
        'qdmy'          => PushOilForQDMY::class,
        'ky'            => PushOilForKY::class,
        'hsl'           => PushOilForHSL::class,
        'wzyt'          => PushOilForWZYT::class,
        'hk'            => PushOilForHK::class,
        'sty'           => PushOilForSTY::class,
        'zt'            => PushOilForZT::class,
        'zto'           => PushOilForZTO::class,
        'sfsx'          => PushOilForSFSX::class,
    ];

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-19 14:51
     */
    public function handle()
    {
        try {
            $stationPushTaskData = StationPushSwitchData::getOpenAll();
            $redisConn = app('redis');

            if (!array_has($this->data['data'], config('other.reserveFields'))) {
                $stationAllData = json_decode(
                                      $redisConn->hget(
                                          self::$oilStationCacheForHubKey,
                                          $this->data['data']['id']
                                      ),
                                      true
                                  ) ?? [];
                Log::handle("Cache station data", '系统', $stationAllData);
                if (!array_has($stationAllData, config('other.reserveFields'))) {
                    Log::handle("The data's field of the station is missing.", "系统", [
                        "pushedData" => $this->data,
                        "cacheData"  => $stationAllData,
                    ], 'warning');
                    $stationAllData = FOSS_STATIONRequest::handle("v1/station/getStationById", [
                        'id' => $this->data['data']['id'],
                    ])['data']['stationInfo'] ?? [];
                }
            } else {
                $stationAllData = $this->data['data'];
            }

            $this->data['data'] = array_merge($stationAllData, $this->data['data']);
            $this->data['data']['push_stop'] = $this->data['data']['isstop'];

            if (isset($this->data["data"]["card_classify"])) {
                if ((int)$this->data["data"]["card_classify"] != 2) {
                    $this->data["data"]["push_stop"] = 1;
                }
            }

            if (!isset($this->data['data']['push_target']) or empty($this->data['data']['push_target'])) {
                $redisConn->hset(
                    self::$oilStationCacheForHubKey,
                    $this->data['data']['id'],
                    json_encode(
                        $this->data['data']
                    )
                );
            }
            $targetedPushConfig = json_decode(
                                      AuthConfigData::getAuthConfigValByName(
                                          "TARGETED_PUSH_CONFIG"
                                      ),
                                      true
                                  ) ?? [];
            $forcePushDisableBySupplierCodeConfig = json_decode(
                                                        AuthConfigData::getAuthConfigValByName(
                                                            "STATION_FORCE_PUSH_STOP_BY_SUPPLIER_CODE"
                                                        ),
                                                        true
                                                    ) ?? [];
            $forcePushDisableByStationIdConfig = json_decode(
                                                     AuthConfigData::getAuthConfigValByName(
                                                         "STATION_FORCE_PUSH_STOP_BY_STATION_ID"
                                                     ),
                                                     true
                                                 ) ?? [];
            $stationWhiteListConfig = json_decode(
                                          AuthConfigData::getAuthConfigValByName(
                                              "STATION_WHITE_LIST_FOR_DOWNSTREAM"
                                          ),
                                          true
                                      ) ?? [];
            foreach ($stationPushTaskData as $v) {
                if (isset($this->data['data']['push_target']) and !empty($this->data['data']['push_target'])) {
                    $roleCode = $v['role_code'];
                    if (empty($roleCode)) {
                        if (strpos($v['name_abbreviation'], '|') !== false) {
                            $roleCode = explode('|', $v['name_abbreviation'])[1];
                        }
                        if (strpos($v['name_abbreviation'], '_') !== false) {
                            $roleCode = explode('_', $v['name_abbreviation'])[1];
                        }
                    }
                    if ($this->data['data']['push_target'] != $roleCode) {
                        continue;
                    }
                }
                $stationDataTemp = $this->data['data'];
                $forcePushStop = false;
                if (isset($stationWhiteListConfig[$v['name_abbreviation']]) and !in_array(
                        $stationDataTemp['id'],
                        $stationWhiteListConfig[$v['name_abbreviation']]
                    )) {
                    $forcePushStop = true;
                    $stationDataTemp["push_stop"] = 1;
                }
                if (isset($stationDataTemp['pcode']) and in_array(
                        $stationDataTemp['pcode'],
                        $forcePushDisableBySupplierCodeConfig
                    )) {
                    $forcePushStop = true;
                    $stationDataTemp["push_stop"] = 1;
                }
                if (isset($forcePushDisableByStationIdConfig[$stationDataTemp['id']]) and
                    in_array($v['name_abbreviation'], $forcePushDisableByStationIdConfig[$stationDataTemp['id']])) {
                    $forcePushStop = true;
                    $stationDataTemp["push_stop"] = 1;
                }
                if (!$forcePushStop and isset($targetedPushConfig[$stationDataTemp["id"]]) and is_array(
                        $targetedPushConfig[$stationDataTemp["id"]]
                    )) {
                    if (!in_array($v['name_abbreviation'], $targetedPushConfig[$stationDataTemp["id"]])) {
                        continue;
                    }
                }
                if (isset($this->workerMapping[$v['name_abbreviation']])) {
                    $stationDataTemp['push_target_code'] = $v['role_code'];
                    if (empty($v['role_code']) and strpos($v['name_abbreviation'], '|') !== false) {
                        $stationDataTemp['role_code'] = explode('|', $v['name_abbreviation'])[1];
                        $stationDataTemp['push_target_code'] = $stationDataTemp['role_code'];
                    }

                    $this->pushQueueAndScreenExp($this->workerMapping[$v['name_abbreviation']], [
                        $stationDataTemp,
                        $v['name_abbreviation']
                    ]);
                } else {
                    Log::handle("The worker is lost", '系统', [
                        "data"              => $this->data,
                        "name_abbreviation" => $v['name_abbreviation']
                    ], "warning");
                }
            }
        } catch (Throwable $exception) {
            Log::handle("Push queue for push station failed", "系统", [
                "data"      => $this->data,
                "exception" => $exception
            ], "error");
            throw $exception;
        }

        return responseFormat();
    }

    public static function getStationWhiteList(string $orgCode)
    {
        $authInfo = AuthInfoData::getAuthInfoByRoleCode($orgCode, true);
        return json_decode(
                   AuthConfigData::getAuthConfigValByName("STATION_WHITE_LIST_FOR_DOWNSTREAM"),
                   true
               )[$authInfo['name_abbreviation']] ?? [];
    }

    public function getStopStationAndAvailableStationForCustomerConfig()
    {
        $nameAbbreviations = [];
        $data = AuthConfigData::getFieldsByNames([
            'STATION_FORCE_PUSH_STOP_BY_STATION_ID',
            'STATION_WHITE_LIST_FOR_DOWNSTREAM'
        ], ['name', 'val'])->keyBy('name')->map(function ($item, $key) use (&$nameAbbreviations) {
            $temp = json_decode($item->val, true);
            switch ($key) {
                case 'STATION_FORCE_PUSH_STOP_BY_STATION_ID':
                    foreach ($temp as $value) {
                        if (!$value) {
                            continue;
                        }
                        array_push($nameAbbreviations, ...$value);
                    }
                    break;
                case 'STATION_WHITE_LIST_FOR_DOWNSTREAM':
                    foreach ($temp as $key => $value) {
                        $nameAbbreviations[] = $key;
                    }
                    break;
            }
            return $temp;
        });
        $authInfoMapping = AuthInfo::getAuthInfoByWhere([
            [
                'field'    => 'name_abbreviation',
                'operator' => 'in',
                'value'    => array_unique($nameAbbreviations),
            ]
        ], ['name_abbreviation', 'role_code'], false, true)->keyBy('name_abbreviation');
        return responseFormat(
            0,
            $data->map(function (&$item, $key) use ($authInfoMapping) {
                switch ($key) {
                    case 'STATION_FORCE_PUSH_STOP_BY_STATION_ID':
                        foreach ($item as &$value) {
                            foreach ($value as &$vValue) {
                                if (strpos($vValue, '|') !== false) {
                                    $vValue = explode('|', $vValue)[1];
                                    continue;
                                }
                                if (strpos($vValue, '_') !== false) {
                                    $vValue = explode('_', $vValue)[1];
                                    continue;
                                }
                                $vValue = $authInfoMapping[$vValue]->role_code;
                            }
                        }
                        break;
                    case 'STATION_WHITE_LIST_FOR_DOWNSTREAM':
                        foreach ($item as $key => $value) {
                            unset($item[$key]);
                            if (strpos($key, '|') !== false) {
                                $item[explode('|', $key)[1]] = $value;
                                continue;
                            }
                            if (strpos($key, '_') !== false) {
                                $item[explode('_', $key)[1]] = $value;
                                continue;
                            }
                            $item[$authInfoMapping[$key]->role_code] = $value;
                        }
                        break;
                }
                return $item;
            })
        );
    }
}
