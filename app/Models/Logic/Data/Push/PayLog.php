<?php
/**
 * Created by PhpStor<PERSON>.
 * User: zdl
 * Date: 2019-01-28
 * Time: 15:22
 */

namespace App\Models\Logic\Data\Push;


use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\Log\ReceiveLog as Log;
use App\Models\Logic\Base;
use App\Models\Logic\Data\Push\PayLog\AD;
use App\Models\Logic\Data\Push\PayLog\AJSW;
use App\Models\Logic\Data\Push\PayLog\BBSP;
use App\Models\Logic\Data\Push\PayLog\BDT_ORG;
use App\Models\Logic\Data\Push\PayLog\BQ;
use App\Models\Logic\Data\Push\PayLog\CFHY;
use App\Models\Logic\Data\Push\PayLog\CFT;
use App\Models\Logic\Data\Push\PayLog\CHTX;
use App\Models\Logic\Data\Push\PayLog\CIEC;
use App\Models\Logic\Data\Push\PayLog\CN;
use App\Models\Logic\Data\Push\PayLog\DDE;
use App\Models\Logic\Data\Push\PayLog\DESP2A6LA9;
use App\Models\Logic\Data\Push\PayLog\DESP2C637M;
use App\Models\Logic\Data\Push\PayLog\DESP2H8BBD;
use App\Models\Logic\Data\Push\PayLog\FY;
use App\Models\Logic\Data\Push\PayLog\GBDW;
use App\Models\Logic\Data\Push\PayLog\HG;
use App\Models\Logic\Data\Push\PayLog\HK;
use App\Models\Logic\Data\Push\PayLog\HLJH;
use App\Models\Logic\Data\Push\PayLog\HR;
use App\Models\Logic\Data\Push\PayLog\HSL;
use App\Models\Logic\Data\Push\PayLog\HYJY;
use App\Models\Logic\Data\Push\PayLog\HYT_ORG;
use App\Models\Logic\Data\Push\PayLog\HZ;
use App\Models\Logic\Data\Push\PayLog\JDWC;
use App\Models\Logic\Data\Push\PayLog\JF;
use App\Models\Logic\Data\Push\PayLog\JTXY;
use App\Models\Logic\Data\Push\PayLog\KY;
use App\Models\Logic\Data\Push\PayLog\LF;
use App\Models\Logic\Data\Push\PayLog\LHYS;
use App\Models\Logic\Data\Push\PayLog\LT;
use App\Models\Logic\Data\Push\PayLog\MB;
use App\Models\Logic\Data\Push\PayLog\MK;
use App\Models\Logic\Data\Push\PayLog\MY;
use App\Models\Logic\Data\Push\PayLog\MYB;
use App\Models\Logic\Data\Push\PayLog\MYCF;
use App\Models\Logic\Data\Push\PayLog\PCKJ;
use App\Models\Logic\Data\Push\PayLog\QDMY;
use App\Models\Logic\Data\Push\PayLog\RQ;
use App\Models\Logic\Data\Push\PayLog\RRS;
use App\Models\Logic\Data\Push\PayLog\RY;
use App\Models\Logic\Data\Push\PayLog\SFSX;
use App\Models\Logic\Data\Push\PayLog\SHENGMAN;
use App\Models\Logic\Data\Push\PayLog\SM;
use App\Models\Logic\Data\Push\PayLog\SP;
use App\Models\Logic\Data\Push\PayLog\SQ;
use App\Models\Logic\Data\Push\PayLog\TC;
use App\Models\Logic\Data\Push\PayLog\WSY;
use App\Models\Logic\Data\Push\PayLog\WZYT;
use App\Models\Logic\Data\Push\PayLog\XC;
use App\Models\Logic\Data\Push\PayLog\XM;
use App\Models\Logic\Data\Push\PayLog\XYDS;
use App\Models\Logic\Data\Push\PayLog\YB;
use App\Models\Logic\Data\Push\PayLog\YBT;
use App\Models\Logic\Data\Push\PayLog\YGJ;
use App\Models\Logic\Data\Push\PayLog\YGY;
use App\Models\Logic\Data\Push\PayLog\YLZ;
use App\Models\Logic\Data\Push\PayLog\YXT;
use App\Models\Logic\Data\Push\PayLog\ZEY;
use App\Models\Logic\Data\Push\PayLog\ZJ;
use App\Models\Logic\Data\Push\PayLog\ZJKA;
use App\Models\Logic\Data\Push\PayLog\ZLGX;
use App\Models\Logic\Data\Push\PayLog\ZT;
use App\Models\Logic\Data\Push\PayLog\ZTO;
use App\Models\Logic\Data\Push\PayLog\ZZ;
use App\Models\Logic\Data\Push\PayLog\ZZ_AH;
use App\Models\Logic\Data\Push\PayLog\ZZ_BJ;
use App\Models\Logic\Data\Push\PayLog\ZZ_TJ;
use Exception;
use Illuminate\Http\JsonResponse;
use Request\FOSS as FOSSRequest;
use Throwable;
use Tool\Alarm\FeiShu;


class PayLog extends Base
{
    /**
     * @var BBSP|BDT_ORG|FY|YGJ|HLJH|LHYS|YB|HG|JDWC|SM|MK|TC
     */
    protected $worker;
    protected $dockingPlatformName = '';
    protected $workerMapping       = [
        'bdtOrg'      => BDT_ORG::class,
        'fy'          => FY::class,
        'ygj'         => YGJ::class,
        'bbsp'        => BBSP::class,
        'hljh'        => HLJH::class,
        'lhys'        => LHYS::class,
        'yb'          => YB::class,
        'hg'          => HG::class,
        'jdwc'        => JDWC::class,
        'sm'          => SM::class,
        'mk'          => MK::class,
        'xc'          => XC::class,
        'dde'         => DDE::class,
        'tc'          => TC::class,
        'sq'          => SQ::class,
        'myb'         => MYB::class,
        'cn'          => CN::class,
        'SPT5CWE'     => SP::class,
        'zey'         => ZEY::class,
        'lf'          => LF::class,
        'hyjy'        => HYJY::class,
        'SPT3SB7'     => SP::class,
        'SPTXXTZ'     => SP::class,
        'SPTLHLX'     => SP::class,
        'SPTCDNF'     => SP::class,
        'SPT76YH'     => SP::class,
        'SPT5GZG'     => SP::class,
        'SPTMKCF'     => SP::class,
        'SPTHZTD'     => SP::class,
        'SPTG7PR'     => SP::class,
        'SPTRR3N'     => SP::class,
        'SPTNGHH'     => SP::class,
        'SPTYW4A'     => SP::class,
        'SPTHSQW'     => SP::class,
        'SPT5RLT'     => SP::class,
        'hytOrg'      => HYT_ORG::class,
        'chtx'        => CHTX::class,
        'pckj'        => PCKJ::class,
        'zlgx'        => ZLGX::class,
        'rq'          => RQ::class,
        'xm'          => XM::class,
        'jf'          => JF::class,
        'ajsw'        => AJSW::class,
        'ygy'         => YGY::class,
        'ciec'        => CIEC::class,
        'zz'          => ZZ::class,
        'desp|2H8BBD' => DESP2H8BBD::class,
        'desp|2A6LA9' => DESP2A6LA9::class,
        'yxt'         => YXT::class,
        'hr'          => HR::class,
        'desp|2C637M' => DESP2C637M::class,
        'gbdw'        => GBDW::class,
        'zj'          => ZJ::class,
        'my'          => MY::class,
        'wsy'         => WSY::class,
        'bq'          => BQ::class,
        'zzah'        => ZZ_AH::class,
        'zzbj'        => ZZ_BJ::class,
        'zztj'        => ZZ_TJ::class,
        'cft'         => CFT::class,
        'mb'          => MB::class,
        'ad'          => AD::class,
        'lt'          => LT::class,
        'cfhy'        => CFHY::class,
        'zjka'        => ZJKA::class,
        'jtxy'        => JTXY::class,
        'ylzZj'       => YLZ::class,
        'mycf'        => MYCF::class,
        'shengman'    => SHENGMAN::class,
        'ry'          => RY::class,
        'ybt'         => YBT::class,
        'hz'          => HZ::class,
        'rrs'         => RRS::class,
        'xyds'        => XYDS::class,
        'qdmy'        => QDMY::class,
        'ky'          => KY::class,
        'hsl'         => HSL::class,
        'wzyt'        => WZYT::class,
        'hk'          => HK::class,
        'zt'          => ZT::class,
        'zto'         => ZTO::class,
        'sfsx'        => SFSX::class,
    ];

    /**
     * RefuelingStatement constructor.
     * @param array $parameter
     * @throws Throwable
     */
    public function __construct(array $parameter)
    {
        parent::__construct($parameter);

        $authInfo = AuthInfoData::getAuthInfoByRoleCode($this->data["role_code"]);
        if (!empty($authInfo)) {
            $realNameAbbreviation = explode('_', $authInfo[0]['name_abbreviation'])[0];
            $this->dockingPlatformName = DockingPlatformInfoData::getFieldsByNameAbbreviation(
                $realNameAbbreviation,
                'platform_name',
                ''
            )['platform_name'] ?? '';
            if (isset($this->workerMapping[$realNameAbbreviation])) {
                $data = FOSSRequest::handle('gas.org_account.account_balance', [
                    'vice_no'       => $this->data['data']['card_no'],
                    'isCardBalance' => 1,
                ]);
                if (bccomp($data['data']['use_balance'], $this->data['data']['money'], 2) == -1) {
                    throw new Exception("", 5000012);
                }

                $this->data['data']['main_account_balance'] = $data['data']['use_balance'];
                $this->data['auth_data'] = AuthInfoData::getAuthInfoFieldByNameAbbreviation($realNameAbbreviation);
                $this->worker = (new $this->workerMapping[$realNameAbbreviation]($this->data));
            } else {
                Log::handle("非法请求", "系统", $this->data, "warning", "");
                throw new Exception("非法请求");
            }
        } else {
            Log::handle("非法请求", "系统", $this->data, "warning", "");
            throw new Exception("非法请求");
        }
    }

    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/3/12 7:15 下午
     */
    public function handle()
    {
        try {
            $this->worker->handle();
        } catch (Throwable $exception) {
            (new FeiShu())->pushOrderFailed([
                "platform_name" => $this->dockingPlatformName,
                "station_name"  => $this->data["data"]["station_name"],
                "oil"           => $this->data["data"]["oil_type_val"] . $this->data["data"]["oil_name_val"] .
                                   $this->data["data"]["oil_level_val"],
                "price"         => $this->data["data"]["price"],
                "oil_num"       => $this->data["data"]["oil_num"],
                "money"         => $this->data["data"]["money"],
                "plate_number"  => $this->data["data"]["truck_no"],
                "org_code"      => $this->data["data"]["orgcode"],
                "card_no"       => $this->data["data"]["card_no"],
                "reason"        => $exception->getMessage(),
            ]);
            throw $exception;
        }

        return responseFormat();
    }
}
