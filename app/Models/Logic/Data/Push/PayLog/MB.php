<?php


namespace App\Models\Logic\Data\Push\PayLog;


use App\Jobs\BasicJob;
use Request\MB as MBRequest;
use Throwable;


class MB extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019-07-12 15:31
     */
    public function handle()
    {
        $requestData = [];
        $oilGunNo = 1;
        $stationInfo = json_decode(
                           app('redis')->hget(
                               BasicJob::OIL_STATION_CACHE_CHECK . '_' . $this->name_abbreviation,
                               $this->data['data']['station_id']
                           ),
                           true
                       ) ?? [];
        foreach ($stationInfo["bizParams"]['channelPriceList'] as $v) {
            if ($v['channelSpec'] == $this->data['data']['oil_type_val'] . '_' .
                                     $this->data['data']['oil_name_val'] .
                                     $this->data['data']['oil_level_val']) {
                $oilGunNo = $v['oilGunNoList'][0] ?? 1;
            }
        }
        $extends = json_decode($this->data["data"]["extends"], true);
        $realExtends = json_decode($extends['extends'] ?? '', true) ?? [];
        $requestData["idCode"] = $realExtends["idCode"];
        $requestData["merchantGasStationId"] = $this->data["data"]["station_id"];
        $requestData["num"] = (float)$this->data["data"]["oil_num"];
        $requestData["settleAmount"] = (int)bcmul($this->data["data"]["pay_money"], 100);
        $requestData["saleUnitPrice"] = (int)bcmul($this->data["data"]["mac_price"], 100);
        $requestData["originalAmount"] = (int)bcmul($this->data["data"]["mac_money"], 100);
        $requestData["originalOrderNo"] = $this->data["data"]["id"];
        $requestData["oilGunNo"] = $oilGunNo;
        $requestData["spec"] = (int)config(
            "oil.oil_mapping.mb.{$this->data['data']['oil_name']}_{$this->data['data']['oil_type']}_{$this->data['data']['oil_level']}"
        );
        $orderAssocModel = $this->createOrder(
            2,
            2,
            $this->data['data']['id'],
            ''
        );
        try {
            $responseData = MBRequest::handle("/api/order/common/create", $requestData);
            $orderAssocModel->platform_order_id = $responseData["content"]['orderNo'];
            $orderAssocModel->save();
        } catch (Throwable $exception) {
            if ($exception->getCode() == 5000999) {
                $orderAssocModel->platform_reason = $exception->getMessage();
                $orderAssocModel->save();
            }

            throw $exception;
        }
    }
}
