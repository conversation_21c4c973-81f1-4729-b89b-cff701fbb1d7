<?php


namespace App\Models\Logic\Data\Push\PayLog;


use Request\HR as HRRequest;
use Throwable;


class HR extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019-07-12 15:31
     */
    public function handle()
    {
        $requestData = [];
        $extends = json_decode($this->data["data"]["extends"], true);
        $requestData["extends"] = $extends['extends'] ?? '';
        $requestData["station_id"] = $this->data["data"]["station_id"];
        $requestData["oil_num"] = $this->data["data"]["oil_num"];
        $requestData["price"] = $this->data["data"]["price"];
        $requestData["pay_price"] = $this->data["data"]["pay_price"];
        $requestData["money"] = $this->data["data"]["money"];
        $requestData["pay_money"] = $this->data["data"]["pay_money"];
        $requestData["trade_id"] = $this->data["data"]["id"];
        $requestData["oil_type"] = $this->data["data"]["oil_type_val"] ?? '';
        $requestData["oil_name"] = $this->data["data"]["oil_name_val"];
        $requestData["oil_level"] = $this->data["data"]["oil_level_val"] ?? '';
        $requestData["oil_code"] = (int)config("oil.oil_mapping.hr." . $this->data['data']['oil_name'] .
            '_' . $this->data['data']['oil_type'] . '_' . $this->data['data']['oil_level'], "");
        $orderAssocModel = $this->createOrder(2, 2, $this->data['data']['id'],
            '');
        try {

            $responseData = HRRequest::handle("app-service/callback/g7/tradeConfirm/addOrder",
                $requestData);
            $orderAssocModel->platform_reason = $responseData['msg'] ?? '';
            $orderAssocModel->platform_order_id = $responseData["data"]['order_id'];
            $orderAssocModel->save();
        } catch (Throwable $exception) {

            if ($exception->getCode() == 5000999) {

                $orderAssocModel->platform_reason = $exception->getMessage();
                $orderAssocModel->save();
            }

            throw $exception;
        }
    }
}
