<?php


namespace App\Models\Logic\Data\Push\PayLog;


use App\Jobs\RetryOrderToGas;
use App\Models\Logic\Trade\Pay\Simple as TradePaySimpleLogic;
use Request\BBSP as BBSPRequest;
use Throwable;


class BBSP extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 15:31
     */
    public function handle()
    {
        $requestData = [];
        $extends = json_decode($this->data["data"]["extends"], true);
        $requestData["user_id"] = $extends["user_id"];
        $requestData["station_id"] = $this->data["data"]["station_id"];
        $requestData["oil_num"] = $this->data["data"]["oil_num"];
        $requestData["price"] = $this->data["data"]["price"];
        $requestData["pay_price"] = $this->data["data"]["pay_price"];
        $requestData["money"] = $this->data["data"]["money"];
        $requestData["pay_money"] = $this->data["data"]["pay_money"];
        $requestData["trade_id"] = $this->data["data"]["id"];
        $requestData["oil_type"] = $this->data["data"]["oil_type_val"];
        $requestData["oil_name"] = $this->data["data"]["oil_name_val"];
        $requestData["oil_level"] = $this->data["data"]["oil_level_val"];
        $orderAssocModel = $this->createOrder(2, 1, $this->data['data']['id'],
            '');

        try {

            $responseData = BBSPRequest::handle("saveTransactionRecord", $requestData);
            $orderAssocModel->platform_order_id = $responseData["data"]['orderNo'];
            $orderAssocModel->save();
        } catch (Throwable $exception) {

            if ($exception->getCode() == 5000999) {

                $orderAssocModel->platform_reason = $exception->getMessage();
                $orderAssocModel->platform_order_status = 2;
                $orderAssocModel->save();
            }

            throw $exception;
        }

        if (!empty($responseData)) {

            try {

                $orderData = [
                    "trade_id" => $this->data["data"]["id"],
                    "other_id" => $responseData["data"]['orderNo'],
                    "order_id" => $responseData["data"]['orderNo'],
                ];
                $authInfo = [
                    'accessKey'         => $this->accessKey,
                    'secret'            => $this->secret,
                    'name_abbreviation' => $this->name_abbreviation,
                ];
                (new TradePaySimpleLogic([
                    'auth_data'  => $authInfo,
                    "trade_id"   => $this->data["data"]["id"],
                    "order_id"   => $responseData["data"]['orderNo'],
                    "pay_status" => 1,
                ]))->handle(true);
            } catch (Throwable $exception) {

                dispatch(new RetryOrderToGas($orderData, $authInfo));
            }
        }
    }
}
