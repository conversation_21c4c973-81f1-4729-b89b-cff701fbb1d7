<?php


namespace App\Models\Logic\Data\Push\PayLog;

use App\Models\Data\Log\ReceiveLog as Log;
use App\Models\Logic\Trade\Pay\Simple as TradePaySimpleLogic;
use Request\JDWC as JDWCRequest;
use Throwable;


class JDWC extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 15:31
     */
    public function handle()
    {
        $requestData = [];
        $extends = json_decode($this->data["data"]["extends"], true);
        $requestData["accountCode"] = $extends["accountCode"];
        $requestData["driverNo"] = $extends["driverNo"];
        $requestData["driverName"] = $extends["driverName"];
        $requestData['orderId'] = $this->data['data']['id'];
        $requestData["refuelSiteCode"] = $this->data["data"]["station_id"];
        $requestData["refuelVolume"] = (float)$this->data["data"]["oil_num"];
        $requestData["unitPrice"] = (float)$this->data["data"]["price"];
        $requestData["feeType"] = 2;
        $requestData["source"] = 1;
        $requestData["operateTime"] = date("Y-m-d H:i:s");
        $requestData["money"] = (float)$this->data["data"]["money"];
        $requestData["oilsTypeName"] = $this->data["data"]["oil_type_val"] ?? '';
        $requestData["oilsKindName"] = $this->data["data"]["oil_name_val"];
        $requestData["oilsLevelName"] = $this->data["data"]["oil_level_val"] ?? '';
        $orderAssocModel = $this->createOrder(2, 2, $this->data['data']['id'],
            $this->data['data']['id']);

        try {

            $responseData = JDWCRequest::handle("costOrTopUpBalance", $requestData);
            $orderAssocModel->platform_reason = $responseData['message'] ?? '';
            $orderAssocModel->save();
        } catch (Throwable $exception) {

            if ($exception->getCode() == 5000999) {

                $orderAssocModel->platform_reason = $exception->getMessage();
                $orderAssocModel->save();
            }

            throw $exception;
        }

        try {

            (new TradePaySimpleLogic([
                'auth_data'  => [
                    'access_key'        => $this->accessKey,
                    'secret'            => $this->secret,
                    'name_abbreviation' => $this->name_abbreviation,
                ],
                "trade_id"   => $this->data["data"]["id"],
                "order_id"   => $this->data["data"]["id"],
                "pay_status" => 1,
            ]))->handle(true);
        } catch (Throwable $exception) {

            $requestConsumeNotifyData = [];

            try {

                $requestConsumeNotifyData["accountCode"] = $extends["accountCode"];
                $requestConsumeNotifyData["driverNo"] = $extends["driverNo"];
                $requestConsumeNotifyData["driverName"] = $extends["driverName"];
                $requestConsumeNotifyData['orderId'] = $this->data['data']['id'];
                $requestConsumeNotifyData["status"] = 2;
                $requestConsumeNotifyData["operateTime"] = date("Y-m-d H:i:s");
                JDWCRequest::handle("acceptOilCardConsumptionSta", $requestConsumeNotifyData);
            } catch (Throwable $exception) {

                Log::handle("Send consume notify to jdwc failed", "京东无车", [
                    'data'      => $requestConsumeNotifyData,
                    'exception' => $exception,
                ], 'error');
                $orderAssocModel->platform_reason = $exception->getMessage();
                $orderAssocModel->save();
                throw $exception;
            }
            responseFormat(5009999, [], true, $exception->getMessage());
        }

        $orderAssocModel->self_order_status = 1;
        $orderAssocModel->save();
        $requestConsumeNotifyData = [];

        try {

            $requestConsumeNotifyData["accountCode"] = $extends["accountCode"];
            $requestConsumeNotifyData["driverNo"] = $extends["driverNo"];
            $requestConsumeNotifyData["driverName"] = $extends["driverName"];
            $requestConsumeNotifyData['orderId'] = $this->data['data']['id'];
            $requestConsumeNotifyData["status"] = 1;
            $requestConsumeNotifyData["operateTime"] = date("Y-m-d H:i:s");
            JDWCRequest::handle("acceptOilCardConsumptionSta", $requestConsumeNotifyData);
            $orderAssocModel->platform_order_status = 1;
            $orderAssocModel->save();
        } catch (Throwable $exception) {

            Log::handle("Send consume notify to jdwc failed", "京东无车", [
                'data'      => $requestConsumeNotifyData,
                'exception' => $exception,
            ], 'error');
            $orderAssocModel->self_order_status = 3;
            $orderAssocModel->platform_reason = $exception->getMessage();
            $orderAssocModel->save();
            throw $exception;
        }
    }
}
