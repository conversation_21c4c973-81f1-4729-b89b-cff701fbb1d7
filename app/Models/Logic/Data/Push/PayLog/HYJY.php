<?php


namespace App\Models\Logic\Data\Push\PayLog;

use App\Jobs\RetryOrderToGas;
use App\Models\Logic\Trade\Pay\Simple as TradePaySimpleLogic;
use Request\HYJY as HYJYRequest;
use Throwable;


class HYJY extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 15:31
     */
    public function handle()
    {
        $requestData = [];
        $extends = json_decode($this->data["data"]["extends"], true);
        $requestData["driver_id"] = $extends["driver_id"];
        $requestData["pay_type"] = $extends["pay_type"];
        $requestData["rqcode_id"] = $extends["rqcode_id"];
        $requestData["station_id"] = $this->data["data"]["station_id"];
        $requestData["oil_num"] = $this->data["data"]["oil_num"];
        $requestData["price"] = $this->data["data"]["price"];
        $requestData["pay_price"] = $this->data["data"]["pay_price"];
        $requestData["money"] = $this->data["data"]["money"];
        $requestData["pay_money"] = $this->data["data"]["pay_money"];
        $requestData["trade_id"] = $this->data["data"]["id"];
        $requestData["oil_type"] = $this->data["data"]["oil_type_val"];
        $requestData["oil_name"] = $this->data["data"]["oil_name_val"];
        $requestData["oil_level"] = $this->data["data"]["oil_level_val"];
        $orderAssocModel = $this->createOrder(2, 2, $this->data['data']['id'],
            '');

        try {

            $responseData = HYJYRequest::handle("ht/HlmdPay", $requestData);
            $orderAssocModel->platform_order_id = $responseData["data"]['order_id'];
            $orderAssocModel->platform_reason = $responseData['message'];
            $orderAssocModel->save();
        } catch (Throwable $exception) {

            if ($exception->getCode() == 5000999) {

                $orderAssocModel->platform_reason = $exception->getMessage();
                $orderAssocModel->save();
            }

            throw $exception;
        }

        if ($responseData['data']['pay_status'] == 'SUCCESS') {

            try {

                $orderData = [
                    "trade_id" => $this->data["data"]["id"],
                    "other_id" => $responseData["data"]['order_id'],
                    "order_id" => $responseData["data"]['order_id'],
                ];
                $authInfo = [
                    'access_key'        => $this->accessKey,
                    'secret'            => $this->secret,
                    'name_abbreviation' => $this->name_abbreviation,
                ];
                (new TradePaySimpleLogic([
                    'auth_data'  => $authInfo,
                    "trade_id"   => $this->data["data"]["id"],
                    "order_id"   => $responseData["data"]['order_id'],
                    "pay_status" => 1,
                ]))->handle(true);
            } catch (Throwable $exception) {

                dispatch(new RetryOrderToGas($orderData, $authInfo));
                throw $exception;
            }
        }
    }
}
