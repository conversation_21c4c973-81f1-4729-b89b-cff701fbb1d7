<?php


namespace App\Models\Logic\Data\Push\PayLog;


use App\Jobs\RetryOrderToGas;
use App\Models\Logic\Trade\Pay\Simple as TradePaySimpleLogic;
use Exception;
use Request\FY as FYRequest;
use Throwable;


class FY extends ThirdParty
{
    /**
     * @return void
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/12/11 5:13 下午
     */
    public function handle()
    {
        $requestData = [];
        $fyExtends = json_decode(($this->data["data"]["extends"] ?? ''), true);
        $requestData["cardNo"] = $fyExtends["fy_card_no"];
        $requestData["oilId"] = "";
        $requestData["stationId"] = $this->data["data"]["station_id"];
        $requestData["oilNum"] = $this->data["data"]["oil_num"];
        $requestData["price"] = $this->data["data"]["price"];
        $requestData["money"] = $this->data["data"]["money"];
        $oilMapped = config(
            "oil.oil_mapping.{$this->name_abbreviation}." .
            $this->data["data"]["oil_name"] .
            "_" .
            $this->data["data"]["oil_type"] .
            "_" .
            $this->data["data"]["oil_level"],
            ""
        );
        $oilMappedArr = explode('_', $oilMapped);
        if (empty($oilMapped)) {
            throw new Exception("福佑未支持该油品", 5000001);
        }
        $requestData["oilType"] = $oilMappedArr[1];
        $requestData["oilLevel"] = $oilMappedArr[2];
        $requestData["oilName"] = $oilMappedArr[0];
        $oilGroup = "{$requestData["oilName"]}_{$requestData["oilType"]}_{$requestData["oilLevel"]}";
        foreach ($fyExtends["fuel_info"] as $v) {
            if ($v["group"] == $oilGroup) {
                $requestData["oilId"] = $v["id"];
            }
        }

        $requestData["orderId"] = $this->data["data"]["id"];
        $orderAssocModel = $this->createOrder(2, 2, $this->data['data']['id'],
            '');
        $responseData = FYRequest::handle("tradeInfo", $requestData, "post");
        $orderAssocModel->platform_order_id = $responseData["data"][0]["tradeId"];
        $orderAssocModel->platform_reason = $responseData['status']['desc'] ?? '';
        $orderAssocModel->save();

        if ($responseData["status"]["code"] == 0) {

            $orderData = [
                "trade_id" => $this->data["data"]["id"],
                "other_id" => $responseData["data"][0]["tradeId"],
                "order_id" => $responseData["data"][0]["tradeId"],
            ];
            $authInfo = [
                'access_key'        => $this->accessKey,
                'secret'            => $this->secret,
                'name_abbreviation' => $this->name_abbreviation,
            ];

            try {

                (new TradePaySimpleLogic([
                    'auth_data'  => $authInfo,
                    "trade_id"   => $this->data["data"]["id"],
                    "order_id"   => $responseData["data"][0]["tradeId"],
                    "pay_status" => 1,
                ]))->handle(true);
            } catch (Throwable $exception) {

                dispatch(new RetryOrderToGas($orderData, $authInfo));
                throw $exception;
            }
        }
    }
}
