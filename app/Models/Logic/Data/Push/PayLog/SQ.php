<?php


namespace App\Models\Logic\Data\Push\PayLog;

use App\Jobs\QueryOrderToTradeCenter;
use App\Models\Logic\Trade\Pay\Simple as TradePaySimpleLogic;
use Illuminate\Support\Facades\Queue;
use Request\SQ as SQRequest;
use Throwable;


class SQ extends ThirdParty
{
    /**
     * @return void
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 15:31
     */
    public function handle()
    {
        $requestData = [];
        $extends = json_decode($this->data["data"]["extends"], true);
        $realExtends = $extends['extends'] ?? [];
        $requestData["payno"] = $realExtends["qr_code"] ?? '';
        $requestData["oilBlance"] = (float)$this->data['data']['main_account_balance'];
        $requestData["stationId"] = $this->data["data"]["station_id"];
        $requestData["stationName"] = $this->data["data"]["station_name"];
        $requestData["litre"] = (float)$this->data["data"]["oil_num"];
        $requestData["price"] = (float)$this->data["data"]["price"];
        $requestData["amount"] = (float)$this->data["data"]["money"];
        $requestData["paymentAmount"] = (float)$this->data["data"]["pay_money"];
        $requestData["orderSn"] = $this->data["data"]["id"];
        $requestData["orderTime"] = $this->data["data"]["oil_time"];
        $requestData["skuCode"] = $this->data["data"]["oil_type_val"] . $this->data["data"]["oil_name_val"] .
            $this->data["data"]["oil_level_val"];
        $requestData["skuName"] = $this->data["data"]["oil_type_val"] . $this->data["data"]["oil_name_val"] .
            $this->data["data"]["oil_level_val"];
        $requestData["discountId"] = 0;
        $orderAssocModel = $this->createOrder(2, 2, $this->data['data']['id'],
            '');

        try {

            $responseData = SQRequest::handle("WriteOilChargingInfo", $requestData);
            (new TradePaySimpleLogic([
                'auth_data'  => [
                    'access_key'        => $this->accessKey,
                    'secret'            => $this->secret,
                    'name_abbreviation' => $this->name_abbreviation,
                ],
                "trade_id"   => $this->data["data"]["id"],
                "order_id"   => $responseData["data"]['SerialNo'],
                "pay_status" => 1,
            ]))->handle(true);
        } catch (Throwable $exception) {

            if ($exception->getCode() == 5000666) {

                $orderAssocModel->platform_reason = $exception->getMessage();
                $orderAssocModel->save();
            }

            if ($exception->getCode() == 5000999) {

                Queue::later(30, new QueryOrderToTradeCenter([
                    'name_abbreviation' => 'sq',
                    'qr_code'           => $realExtends['qr_code'],
                    'self_order_id'     => $this->data['data']['id'],
                ]));
            }

            throw $exception;
        }
    }
}
