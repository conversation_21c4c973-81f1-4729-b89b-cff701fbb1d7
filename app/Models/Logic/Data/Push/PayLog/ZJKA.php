<?php


namespace App\Models\Logic\Data\Push\PayLog;

use Request\ZJKA as ZJKARequest;
use Throwable;

class ZJKA extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhu<PERSON><PERSON>@zhongqixing.com>
     * @since 2019-07-12 15:31
     */
    public function handle()
    {
        $requestData = [];
        $extends = json_decode($this->data["data"]["extends"], true);
        $requestData["extends"] = $extends["extends"] ?? '';
        $requestData["station_id"] = $this->data["data"]["station_id"];
        $requestData["oil_num"] = $this->data["data"]["oil_num"];
        $requestData["price"] = $this->data["data"]["price"];
        $requestData["pay_price"] = $this->data["data"]["pay_price"];
        $requestData["money"] = $this->data["data"]["money"];
        $requestData["pay_money"] = $this->data["data"]["pay_money"];
        $requestData["gun_price"] = (float)$this->data["data"]["mac_price"];
        $requestData["gun_money"] = (float)$this->data["data"]["mac_money"];
        $requestData["trade_id"] = $this->data["data"]["id"];
        $requestData["oil_type"] = $this->data["data"]["oil_type_val"];
        $requestData["oil_name"] = $this->data["data"]["oil_name_val"];
        $requestData["oil_level"] = $this->data["data"]["oil_level_val"];
        $orderAssocModel = $this->createOrder(
            2,
            2,
            $this->data['data']['id'],
            ''
        );

        try {
            $responseData = ZJKARequest::handle("receiveOrder", $requestData);
            $orderAssocModel->platform_order_id = $responseData["data"]['order_id'];
            $orderAssocModel->save();
        } catch (Throwable $exception) {
            if ($exception->getCode() == 5000999) {
                $orderAssocModel->platform_reason = $exception->getMessage();
                $orderAssocModel->save();
            }

            throw $exception;
        }
    }
}
