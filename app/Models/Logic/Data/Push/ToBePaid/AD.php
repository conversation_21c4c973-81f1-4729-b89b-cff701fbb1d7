<?php


namespace App\Models\Logic\Data\Push\ToBePaid;

use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Data\Push\PayLog\ThirdParty;
use App\Models\Logic\Trade\Pay\Simple as TradePaySimpleLogic;
use Exception;
use Request\AD as ADRequest;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Throwable;
use Tool\Alarm\FeiShu;


class AD extends ThirdParty
{
    /**
     * @return void
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019-07-12 15:31
     */
    public function handle()
    {
        $extends = json_decode($this->data["extends"], true);
        $realExtends = $extends['extends'] ?? [];
        if (bccomp($this->data['money'], $realExtends['cardAvailableBalance'], 2) === 1) {
            throw new Exception("司机账户余额不足", 5000999);
        }
        $goodsNo = array_flip(
                       config('oil.oil_mapping.ad.short_oil_mapping')
                   )[$this->data['oil_name'] .
                     "_" .
                     $this->data['oil_type'] .
                     "_" .
                     $this->data['oil_level']] ?? '';
        if (empty($goodsNo)) {
            throw new Exception(
                "安得没有" .
                $this->data['gas_oil_type'] .
                $this->data['gas_oil_name'] .
                $this->data['gas_oil_level'] . "油品，下单失败。",
                5000888
            );
        }
        $orderInfo = OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'platform_order_id',
                'operator' => '=',
                'value'    => $realExtends['orderNo'],
            ],
            [
                'field'    => 'platform_order_status',
                'operator' => '=',
                'value'    => 1,
            ],
            [
                'field'    => 'self_order_status',
                'operator' => '=',
                'value'    => 1,
            ],
            [
                'field'    => 'platform_name',
                'operator' => '=',
                'value'    => $this->name_abbreviation,
            ]
        ], ['id', 'self_order_id', 'platform_order_id']);
        if (!empty($orderInfo)) {
            throw new Exception("该订单已支付,不可重复使用", 5000999);
        }
        $this->data['trade_type'] = 1;
        $this->createOrder(
            2,
            2,
            $this->data['id'],
            $realExtends['orderNo'],
            '',
            '',
            $this->data
        );
        $andeOrderData = ADRequest::handle('bop/T201904230000000014/nop/orderConfirm', [
            'orderNo'         => $realExtends['orderNo'],
            'orderAmount'     => $this->data["realMacMoney"],
            'supplierOrderNo' => $this->data['id'],
        ]);
        if ("{$this->data['gas_oil_name']}_{$this->data['gas_oil_type']}_{$this->data['gas_oil_level']}" !=
            config("oil.oil_mapping.ad.short_oil_mapping.{$andeOrderData['decryptData']['goodsNo']}")) {
            throw new Exception("订单商品不一致,请与司机核实后重试", 5000888);
        }
        if ($this->data['gasStationId'] != $andeOrderData['decryptData']['thirdStationNo']) {
            throw new Exception("订单加油站点不一致,请与司机核实后重试", 5000888);
        }
        if ($this->data['money'] != $andeOrderData['decryptData']['settleAmount']) {
            throw new Exception("订单结算金额不一致,请与司机核实后重试", 5000888);
        }
        if ($this->data['realPrice'] != $andeOrderData['decryptData']['settleUnitPrice']) {
            throw new Exception("订单结算单价不一致,请与司机核实后重试", 5000888);
        }
        $data = FOSS_ORDERRequest::handle('/api/oil_adapter/getOrderItem', [
            'order_id' => $this->data['id'],
        ]);
        $requestData = [
            'requestType'          => 2,
            'orderNo'              => $realExtends['orderNo'],
            'supplierOrderNo'      => $this->data['id'],
            'supplierOrderStatus'  => 30,
            'stationName'          => $data['data']['station_name'],
            'stationNo'            => $data['data']['station_id'],
            'goodsNo'              => array_flip(
                                          config('oil.oil_mapping.ad.short_oil_mapping')
                                      )["{$data['data']['oil_name']}_{$data['data']['oil_type']}_{$data['data']['oil_level']}"],
            'goodsNumber'          => $data['data']['oil_num'],
            'goodsUnit'            => config(
                "oil.oil_mapping.ad.unit_mapping.{$data['data']['oil_name']}",
                ''
            ),
            'orderAmount'          => $data['data']['mac_amount'],
            'settleAmount'         => $data['data']['oil_money'],
            'settleUnitPrice'      => $data['data']['oil_price'],
            'orderUnitPrice'       => $data['data']['mac_price'],
            'plateNumber'          => $data['data']['truck_no'],
            'driverName'           => $data['data']['driver_name'],
            'driverPhone'          => $data['data']['_driver_phone'],
            'orderCreateTime'      => $data['data']['create_time'],
            'orderPayTime'         => $data['data']['update_time'],
            'accountingEntityCode' => self::getCustomerEntityCodeByOrgCodeAndConfigName(
                $data['data']['org_code'],
                'AD_ORG_TO_ORG_MAPPING',
                'AD_DEFAULT_ENTITY_CODE',
                true
            ),
        ];
        ADRequest::handle('bop/T201904230000000014/nop/paymentCallBack', $requestData);
        try {
            (new TradePaySimpleLogic([
                'auth_data'  => [
                    'access_key'        => $this->accessKey,
                    'secret'            => $this->secret,
                    'name_abbreviation' => $this->name_abbreviation,
                ],
                "trade_id"   => $this->data["id"],
                "order_id"   => $realExtends["orderNo"],
                "pay_status" => 1,
            ]))->handle(true);
        } catch (Throwable $exception) {
            (new FeiShu())->deductionMainAccountFailed([
                'platform_name' => '安得',
                "station_name"  => $this->data["orderData"]["stationInfo"]["station_name"],
                'oil'           => $this->data["orderData"]["oil_type_val"] . $this->data['orderData']["oil_name_val"] .
                                   $this->data["orderData"]["oil_level_val"],
                "price"         => $this->data["orderData"]["price"],
                "oil_num"       => $this->data["orderData"]["oil_num"],
                "money"         => $this->data["orderData"]["money"],
                "plate_number"  => $this->data["orderData"]["truck_no"] ?? '',
                "reason"        => $exception->getMessage(),
                "org_code"      => $this->data["orderData"]["orgcode"],
                "card_no"       => $this->data["orderData"]["card_no"],
            ]);
            if ($exception->getCode() == 5000999) {
                throw new Exception($exception->getMessage(), 5000888);
            }
            throw $exception;
        }
    }
}
