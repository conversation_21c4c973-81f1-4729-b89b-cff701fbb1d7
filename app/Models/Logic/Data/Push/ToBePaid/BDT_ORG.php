<?php


namespace App\Models\Logic\Data\Push\ToBePaid;


use Request\BDT as BDTRequest;
use Throwable;

class BDT_ORG extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhu<PERSON><PERSON>@zhongqixing.com>
     * @since 2019-07-26 17:53
     */
    public function handle()
    {
        $requestData = [];
        $this->data['extends'] = json_decode($this->data['extends'], true);
        $requestData["qr_code"] = $this->data['extends']['qr_code'];
        $requestData["station_id"] = $this->data["gasStationId"];
        $requestData["oil_num"] = (string)$this->data["realOilNum"];
        $requestData["price"] = (string)$this->data["realPrice"];
        $requestData["money"] = (string)$this->data['money'];
        $requestData["trade_id"] = $this->data["id"];
        $requestData["oil_type"] = $this->data["gas_oil_type"];
        $requestData["oil_name"] = $this->data["gas_oil_name"];
        $requestData["oil_level"] = $this->data["gas_oil_level"];
        $orderAssocModel = $this->createOrder(2, 2, $this->data['id'],
            '');
        try {

            BDTRequest::handle("7", [
                'dealInfo' => json_encode($requestData),
            ]);
        } catch (Throwable $exception) {

            if ($exception->getCode() == 5000999) {

                $orderAssocModel->platform_reason = $exception->getMessage();
                $orderAssocModel->save();
            }
            throw $exception;
        }
    }
}
