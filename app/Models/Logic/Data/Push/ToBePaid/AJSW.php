<?php


namespace App\Models\Logic\Data\Push\ToBePaid;


use Request\AJSW as AJSWRequest;
use Throwable;


class AJSW extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 15:31
     */
    public function handle()
    {
        $requestData = [];
        $extends = json_decode($this->data["extends"], true);
        $requestData["extends"] = $extends['extends'] ?? '';
        $requestData["station_id"] = $this->data["gasStationId"];
        $requestData["oil_num"] = (float)$this->data["realOilNum"];
        $requestData["price"] = (float)$this->data["realPrice"];
        $requestData["pay_price"] = (float)$this->data["realPrice"];
        $requestData["money"] = (float)$this->data["money"];
        $requestData["pay_money"] = $this->data["money"];
        $requestData["trade_id"] = $this->data["id"];
        $requestData["oil_type"] = $this->data["gas_oil_type"] ?? '';
        $requestData["oil_name"] = $this->data["gas_oil_name"];
        $requestData["oil_level"] = $this->data["gas_oil_level"] ?? '';
        $orderAssocModel = $this->createOrder(2, 2, $this->data['id'],
            '');
        try {

            $responseData = AJSWRequest::handle("updateOrderDetail", $requestData);
            $orderAssocModel->platform_reason = $responseData['message'] ?? '';
            $orderAssocModel->platform_order_id = $responseData["data"]['order_id'];
            $orderAssocModel->save();
        } catch (Throwable $exception) {

            if ($exception->getCode() == 5000999) {

                $orderAssocModel->platform_reason = $exception->getMessage();
                $orderAssocModel->save();
            }

            throw $exception;
        }
    }
}
