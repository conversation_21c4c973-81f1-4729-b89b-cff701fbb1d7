<?php


namespace App\Models\Logic\Data\Push\ToBePaid;

use Request\ZLGX as ZLGXRequest;
use Throwable;


class ZLGX extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019-07-12 15:31
     */
    public function handle()
    {
        $requestData = [];
        $extends = json_decode($this->data["extends"], true);
        $requestData["extendsStr"] = $extends['extends'] ?? '';
        $requestData["stationId"] = $this->data["gasStationId"];
        $requestData["oilNum"] = (float)$this->data["realOilNum"];
        $requestData["price"] = (float)$this->data["realPrice"];
        $requestData["money"] = (float)$this->data["money"];
        $requestData["tradeId"] = $this->data["id"];
        $requestData["oilType"] = $this->data["gas_oil_type"];
        $requestData["oilName"] = $this->data["gas_oil_name"];
        $requestData["oilLevel"] = $this->data["gas_oil_level"];
        $requestData["gunPrice"] = (float)$this->data["realMacPrice"];
        $requestData["gunMoney"] = (float)$this->data["realMacMoney"];
        $orderAssocModel = $this->createOrder(
            2,
            2,
            $this->data['id'],
            ''
        );
        try {
            $responseData = ZLGXRequest::handle("api/thirdparty/G7/receiveOrder", $requestData);
            $orderAssocModel->platform_reason = $responseData['message'] ?? '';
            $orderAssocModel->platform_order_id = $responseData["data"];
            $orderAssocModel->save();
        } catch (Throwable $exception) {

            if ($exception->getCode() == 5000999) {

                $orderAssocModel->platform_reason = $exception->getMessage();
                $orderAssocModel->save();
            }

            throw $exception;
        }
    }
}
