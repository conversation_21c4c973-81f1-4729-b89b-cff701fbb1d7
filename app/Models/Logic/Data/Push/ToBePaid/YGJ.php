<?php


namespace App\Models\Logic\Data\Push\ToBePaid;


use Request\YGJ as YGJRequest;
use Throwable;

class YGJ extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhu<PERSON><PERSON>@zhongqixing.com>
     * @since 2019-07-31 19:09
     */
    public function handle()
    {
        $requestData = [];
        $extends = json_decode($this->data["extends"], true);
        $requestData["customer_id"] = $extends["customer_id"];
        $requestData["vehicle_number"] = $extends["vehicle_number"];
        $requestData["station_id"] = $this->data["gasStationId"];
        $requestData["oil_num"] = $this->data["realOilNum"];
        $requestData["price"] = $this->data["realPrice"];
        $requestData["money"] = $this->data["money"];
        $requestData["trade_id"] = $this->data["id"];
        $requestData["oil_type"] = $this->data["gas_oil_type"];
        $requestData["oil_name"] = $this->data["gas_oil_name"];
        $requestData["oil_level"] = $this->data["gas_oil_level"];
        $orderAssocModel = $this->createOrder(2, 2, $this->data['id'],
            '');
        try {

            YGJRequest::handle("93010003", $requestData);
        } catch (Throwable $exception) {

            if ($exception->getCode() == 5000999) {

                $orderAssocModel->platform_reason = $exception->getMessage();
                $orderAssocModel->save();
            }

            throw $exception;
        }
    }
}
