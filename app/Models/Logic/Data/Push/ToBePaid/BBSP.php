<?php


namespace App\Models\Logic\Data\Push\ToBePaid;


use App\Jobs\RetryOrderToGas;
use App\Models\Data\OrderAssoc as OrderAssocData;
use Request\BBSP as BBSPRequest;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Throwable;

class BBSP extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-17 16:33
     */
    public function handle()
    {
        $requestData = [];
        $this->data['extends'] = json_decode($this->data['extends'], true);
        $requestData["user_id"] = $this->data['extends']["user_id"];
        $requestData["station_id"] = $this->data["gasStationId"];
        $requestData["oil_num"] = $this->data["realOilNum"];
        $requestData["price"] = $this->data['realPrice'];
        $requestData["pay_price"] = $this->data['realPrice'];
        $requestData["money"] = $this->data['money'];
        $requestData["pay_money"] = $this->data['money'];
        $requestData["trade_id"] = $this->data["id"];
        $requestData["oil_type"] = $this->data["gas_oil_type"];
        $requestData["oil_name"] = $this->data["gas_oil_name"];
        $requestData["oil_level"] = $this->data["gas_oil_level"];

        try {

            $responseData = BBSPRequest::handle("saveTransactionRecord", $requestData);
            $this->createOrder(2, 2, $this->data['id'],
                $responseData["data"]['orderNo']);
        } catch (Throwable $exception) {

            if ($exception->getCode() == 5000999) {

                OrderAssocData::insert(2, 2, $this->data['id'],
                    '', 'bbsp', '', $exception->getMessage());
            }

            throw $exception;
        }

        if (!empty($responseData)) {

            $orderData = [
                "other_id" => $this->data['order_id'],
                "trade_id" => $this->data["id"],
            ];
            $authInfo = [
                'accessKey' => $this->data['access_key'],
                'secret'    => $this->data['secret']
            ];

            try {
                FOSS_ORDERRequest::handle('/api/oil_adapter/oaPay', [
                    'order_id' => $this->data['id']
                ]);
                OrderAssocData::updateSelfOrderStatus($this->data['id'], 1, '');
            } catch (Throwable $exception) {

                dispatch(new RetryOrderToGas($orderData, $authInfo));
            }
        }
    }
}
