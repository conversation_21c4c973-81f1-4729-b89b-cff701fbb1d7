<?php


namespace App\Models\Logic\Data\Push\ToBePaid;

use App\Models\Data\AuthInfo as AuthInfoData;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Request\ZEY as ZEYRequest;
use Throwable;
use Tool\Alarm\FeiShu;

class ZEY extends ThirdParty
{
    /**
     * @return void
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 15:31
     */
    public function handle()
    {
        $requestData = [];
        $extends = json_decode($this->data["extends"], true);
        $realExtends = $extends['extends'] ?? [];
        $requestData["customerOrderId"] = $realExtends["customerOrderId"] ?? '';
        $requestData["oilsStationId"] = $this->data["gasStationId"];
        $requestData["count"] = (float)$this->data["realOilNum"];
        $requestData["price"] = (float)$this->data["realPrice"];
        $requestData["totalPrice"] = (float)$this->data["money"];
        $requestData["zhaoyouOrderId"] = $this->data["id"];
        $requestData["oilsId"] = $this->data["gas_oil_type"] . $this->data["gas_oil_name"] . $this->data["gas_oil_level"];
        $insertResult = $this->createOrder(
            2,
            2,
            $this->data['id'],
            ''
        );
        $mainAuthInfo = AuthInfoData::getAuthInfoFieldByNameAbbreviation($this->name_abbreviation);
        $this->accessKey = $mainAuthInfo['access_key'];
        $this->secret = $mainAuthInfo['secret'];

        try {
            ZEYRequest::handle("syncOrder/add", $requestData);
        } catch (Throwable $exception) {
            $insertResult->platform_reason = $exception->getMessage();
            $insertResult->reason = "";
            $insertResult->self_order_status = 4;
            $insertResult->platform_order_status = 4;
            $insertResult->save();
            throw $exception;
        }
        try {
            FOSS_ORDERRequest::handle('/api/oil_adapter/oaPay', [
                'order_id' => $this->data['id'],
            ]);
            $insertResult->self_order_status = 1;
            $insertResult->platform_order_status = 1;
            $insertResult->platform_order_id = $realExtends["customerOrderId"] ?? '';
            $insertResult->save();
        } catch (Throwable $throwable) {
            (new FeiShu())->deductionMainAccountFailed([
                'platform_name' => '则一',
                'station_name'  => $this->data['orderData']['stationInfo']['station_name'],
                'oil'           => config("oil.oil_no_simple.{$this->data['orderData']['oil_type']}", "") .
                                   array_flip(config("oil.oil_type"))[$this->data['orderData']['oil_name']] .
                                   array_flip(config("oil.oil_level"))[$this->data['orderData']['oil_level']],
                "price"         => $this->data["orderData"]["price"],
                "oil_num"       => $this->data["orderData"]["oil_num"],
                "money"         => $this->data["orderData"]["money"],
                "plate_number"  => $this->data["orderData"]["truck_no"] ?? '',
                "reason"        => $throwable->getMessage(),
                "org_code"      => $this->data["orderData"]["orgcode"],
                "card_no"       => $this->data["orderData"]["card_no"],
            ]);
            $insertResult->self_order_status = 2;
            $insertResult->platform_order_status = 1;
            $insertResult->platform_order_id = $realExtends["customerOrderId"] ?? '';
            $insertResult->reason = $throwable->getMessage();
            $insertResult->save();
        }
    }
}
