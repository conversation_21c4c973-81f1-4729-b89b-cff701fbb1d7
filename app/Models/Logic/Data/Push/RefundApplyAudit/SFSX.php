<?php


namespace App\Models\Logic\Data\Push\RefundApplyAudit;


use Request\SFSX as SFSXRequest;
use Symfony\Component\HttpFoundation\Response;
use Throwable;


class SFSX extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 15:31
     */
    public function handle(): Response
    {
        SFSXRequest::handle("dundun/openapi/outer/sx/g_seven/callback/receiveRefundApplyAuditAck", [
            'order_id'     => $this->data['data']['order_id'],
            'audit_result' => $this->data['data']['audit_status'],
            'audit_reason' => $this->data['data']['audit_reason'],
        ]);
        return responseFormat();
    }
}
