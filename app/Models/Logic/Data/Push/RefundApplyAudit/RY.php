<?php


namespace App\Models\Logic\Data\Push\RefundApplyAudit;


use Request\RY as RYRequest;
use Symfony\Component\HttpFoundation\Response;
use Throwable;


class RY extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 15:31
     */
    public function handle(): Response
    {
        RYRequest::handle("web/g7/order/refund/callback", [
            'order_id'     => $this->data['data']['order_id'],
            'audit_result' => $this->data['data']['audit_status'],
            'audit_reason' => $this->data['data']['audit_reason'],
        ]);
        return responseFormat();
    }
}
