<?php


namespace App\Models\Logic\Data\Push\RefundApplyAudit;


use Request\CFHY as CFHYRequest;
use Symfony\Component\HttpFoundation\Response;
use Throwable;


class CFHY extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019-07-12 15:31
     */
    public function handle(): Response
    {
        CFHYRequest::handle("refund/writeOff", [
            'order_id'     => $this->data['data']['order_id'],
            'audit_result' => $this->data['data']['audit_status'],
            'audit_reason' => $this->data['data']['audit_reason'],
        ]);
        return responseFormat();
    }
}
