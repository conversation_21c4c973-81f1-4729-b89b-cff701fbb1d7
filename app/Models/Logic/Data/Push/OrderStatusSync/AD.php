<?php


namespace App\Models\Logic\Data\Push\OrderStatusSync;


use App\Jobs\PushOrderResultForAD;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Trade\Pay\AD as ADPay;
use Illuminate\Support\Facades\Queue;
use Symfony\Component\HttpFoundation\Response;
use Throwable;


class AD extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhu<PERSON><PERSON>@zhongqixing.com>
     * @since 2019-07-12 15:31
     */
    public function handle(): Response
    {
        $orderInfo = OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'self_order_id',
                'operator' => '=',
                'value'    => $this->data['data']['order_id'],
            ],
            [
                'field'    => 'platform_name',
                'operator' => '=',
                'value'    => $this->name_abbreviation,
            ],
        ], ['*']);
        if (empty($orderInfo)) {
            return responseFormat(5000018);
        }
        Queue::push(
            new PushOrderResultForAD(
                $orderInfo,
                ADPay::getPushOrderType($this->data['data']['trade_type'], $this->data['data']['supplier_code'])
            ),
            "",
            "adapter_deal_trade"
        );
        return responseFormat();
    }
}
