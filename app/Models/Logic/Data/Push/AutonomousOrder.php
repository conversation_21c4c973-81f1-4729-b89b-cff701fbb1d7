<?php
/**
 * Created by PhpStorm.
 * User: zdl
 * Date: 2019-01-28
 * Time: 15:22
 */

namespace App\Models\Logic\Data\Push;


use App\Models\Data\AuthInfo;
use App\Models\Logic\Base;
use App\Models\Logic\Data\Push\AutonomousOrder\CNPC;
use App\Models\Logic\Data\Push\AutonomousOrder\CY;
use App\Models\Logic\Data\Push\AutonomousOrder\DH;
use App\Models\Logic\Data\Push\AutonomousOrder\DT;
use App\Models\Logic\Data\Push\AutonomousOrder\EZT;
use App\Models\Logic\Data\Push\AutonomousOrder\GAODENG;
use App\Models\Logic\Data\Push\AutonomousOrder\GB;
use App\Models\Logic\Data\Push\AutonomousOrder\GDQP;
use App\Models\Logic\Data\Push\AutonomousOrder\GS;
use App\Models\Logic\Data\Push\AutonomousOrder\HBKJ;
use App\Models\Logic\Data\Push\AutonomousOrder\HSY;
use App\Models\Logic\Data\Push\AutonomousOrder\JHCX;
use App\Models\Logic\Data\Push\AutonomousOrder\JT;
use App\Models\Logic\Data\Push\AutonomousOrder\JTX;
use App\Models\Logic\Data\Push\AutonomousOrder\MTLSY;
use App\Models\Logic\Data\Push\AutonomousOrder\SAIC;
use App\Models\Logic\Data\Push\AutonomousOrder\SC;
use App\Models\Logic\Data\Push\AutonomousOrder\SH;
use App\Models\Logic\Data\Push\AutonomousOrder\SHSX;
use App\Models\Logic\Data\Push\AutonomousOrder\SQZL;
use App\Models\Logic\Data\Push\AutonomousOrder\SQZSH;
use App\Models\Logic\Data\Push\AutonomousOrder\TBJX;
use App\Models\Logic\Data\Push\AutonomousOrder\XMSK;
use App\Models\Logic\Data\Push\AutonomousOrder\YC;
use App\Models\Logic\Data\Push\AutonomousOrder\YUNDATONG;
use App\Models\Logic\Data\Push\AutonomousOrder\ZDC;
use App\Models\Logic\Data\Push\AutonomousOrder\ZHYK;
use App\Models\Logic\Data\Push\AutonomousOrder\ZWL;
use App\Models\Logic\Data\Push\AutonomousOrder\ZY;
use App\Models\Logic\Data\Push\VerificationResult\BQ;
use Illuminate\Http\JsonResponse;
use Throwable;


class AutonomousOrder extends Base
{
    private $jobMapping = [
        'zy'        => ZY::class,
        'ezt'       => EZT::class,
        'yc'        => YC::class,
        'sc'        => SC::class,
        'sx'        => SC::class,
        'fj'        => SC::class,
        'gs'        => GS::class,
        'zyLng'     => ZY::class,
        'hb'        => SC::class,
        'saic_aj'   => SAIC::class,
        'gb'        => GB::class,
        'saic_fl'   => SAIC::class,
        'cnpc'      => CNPC::class,
        'dt_zj'     => DT::class,
        'jhcx'      => JHCX::class,
        'saic_ajsw' => SAIC::class,
        'cy'        => CY::class,
        'hbkj'      => HBKJ::class,
        'saic_yc'   => SAIC::class,
        'hsy'       => HSY::class,
        'sqzl'      => SQZL::class,
        'gdqp'      => GDQP::class,
        'gb_tj'     => GB::class,
        'zhyk'      => ZHYK::class,
        'bq'        => BQ::class,
        'zdc'       => ZDC::class,
        'zwl'       => ZWL::class,
        'sqZsh'     => SQZSH::class,
        'mtlSy'     => MTLSY::class,
        'sh'        => SH::class,
        'shsx'      => SHSX::class,
        'jt'        => JT::class,
        'jtx'       => JTX::class,
        'dh'        => DH::class,
        'xmsk'      => XMSK::class,
        'yundatong' => YUNDATONG::class,
        'gaodeng'   => GAODENG::class,
        'zjqp'      => SC::class,
        'tbjx'      => TBJX::class,
    ];

    /**
     * @var ZY|EZT|YC|SC|GS
     */
    private $worker = null;

    /**
     * RefuelingStatement constructor.
     * @param array $parameter
     * @param bool $initFailedResponse
     */
    public function __construct(array $parameter, bool $initFailedResponse = true)
    {
        parent::__construct($parameter);

        $authInfo = AuthInfo::getAuthInfoByRoleCode($this->data["data"]["pcode"]);
        if (!empty($authInfo)) {
            //把授权信息数据存入真实数据
            $this->data['data']['auth_info_data'] = $authInfo[0];
            if (isset($this->jobMapping[$authInfo[0]['name_abbreviation']])) {
                $this->worker = (new $this->jobMapping[$authInfo[0]['name_abbreviation']]($this->data));
            }
        }
        if (!$this->worker and $initFailedResponse) {
            responseFormat(0, [], true, "非法请求");
        }
    }

    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-08-22 18:02
     */
    public function handle(): JsonResponse
    {
        $this->worker->handle();
        return responseFormat();
    }

    /**
     * @return bool
     * <AUTHOR> <<EMAIL>>
     * @since 2021/9/2 4:51 下午
     */
    public function checkWorkerExists(): bool
    {
        if (!isset($this->data['data']['auth_info_data']['name_abbreviation'])) {
            return false;
        }
        return isset($this->jobMapping[$this->data['data']['auth_info_data']['name_abbreviation']]);
    }
}
