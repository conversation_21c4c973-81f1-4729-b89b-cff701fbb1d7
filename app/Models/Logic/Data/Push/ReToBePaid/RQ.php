<?php


namespace App\Models\Logic\Data\Push\ReToBePaid;


use App\Jobs\RunTaskByJob;
use App\Models\Dao\OrderAssoc as OrderAssocDao;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Trade\ReToBePaidCallback\RQ as RQReToBePaidCallback;
use Carbon\Carbon;
use Illuminate\Support\Facades\Queue;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Request\RQ as RQRequest;
use Throwable;


class RQ extends ThirdParty
{
    /**
     * @return OrderAssocDao
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 15:31
     */
    public function handle(): OrderAssocDao
    {
        $genOrderData = [
            'card_no'        => $this->data['real_auth_info']['card_no'],
            'station_id'     => $this->data['station_id'],
            'oil_num'        => $this->data['oil_num'],
            'oil_price'      => $this->data['oil_sale_price'],
            'supplier_price' => $this->data['supplier_price'],
            'priceGun'       => $this->data['mac_price'],
            'oil_money'      => $this->data['oil_money'],
            'pay_money'      => $this->data['oil_money'],
            'supplier_money' => $this->data['supplier_money'],
            'amountGun'      => $this->data['mac_money'],
            'oil_type'       => $this->data['oil_type'],
            'oil_name'       => $this->data['oil_name'],
            'oil_level'      => $this->data['oil_level'],
            'oil_time'       => $this->data['oil_time'],
            'third_order_id' => $this->getOrderNo("fo"),
            'driver_source'  => 2,
            'trade_mode'     => 65,
            'truck_no'       => $this->data['truck_no'],
            'driver_name'    => $this->data['driver_name'],
            'driver_phone'   => $this->data['driver_phone'],
            'oil_unit'       => $this->data['oil_unit'],
        ];
        $orderData = FOSS_ORDERRequest::handle('/api/oil_adapter/makeOrder', $genOrderData);
        $insertResult = OrderAssocData::insert(
            2,
            2,
            $orderData['data']['order_id'],
            $genOrderData['third_order_id'],
            explode('_', $this->data['real_auth_info']['name_abbreviation'])[0],
            '',
            '',
            '',
            '补单'
        );

        try {
            $responseData = RQRequest::handle("rokin.partner.oil.pay", [
                'driverCode'   => $this->data['third_card_no'],
                'phone'        => $this->data['driver_phone'],
                'licensePlate' => $this->data['truck_no'],
                'stationCode'  => $this->data['station_id'],
                'oilCode'      => $this->data['oil_type_val'] . ',' . $this->data['oil_name_val'] . ',' .
                                  $this->data['oil_level_val'],
                'litter'       => $this->data['oil_num'],
                'tradePrice'   => (int)bcmul($this->data['oil_sale_price'], 100),
                'price'        => (int)bcmul($this->data['mac_price'], 100),
                'money'        => (int)bcmul($this->data['oil_money'], 100),
                'payMoney'     => (int)bcmul($this->data['oil_money'], 100),
                'outTradeNo'   => $orderData['data']['order_id'],
                'orderType'    => 2,
            ]);
            $remarkData = [];
            foreach ($responseData['result']['legalList'] as $v) {
                $remarkData[] = $v['legalCode'] . ":" . bcdiv($v['money'], 100, 2);
            }
            $insertResult->platform_order_id = $responseData['result']['tradeNo'];
            $insertResult->platform_order_status = 1;
            $insertResult->platform_reason = implode(",", $remarkData);
            $insertResult->save();
            Queue::later(
                Carbon::now()->addSeconds(1),
                new RunTaskByJob(RQReToBePaidCallback::class, "handle", [
                    [
                        'order_id'             => $responseData['result']['tradeNo'],
                        'trade_id'             => $orderData['data']['order_id'],
                        'pay_status'           => $responseData['result']['payState'] == 2 ? 1 : 2,
                        'pay_reason'           => '',
                        "updatePlatformReason" => false,
                        'auth_data'            => $this->data['real_auth_info'],
                    ]
                ], true),
                '',
                'adapter_deal_trade'
            );
        } catch (Throwable $exception) {
            OrderAssocData::updateOrderInfoById($insertResult->id, [
                "platform_reason" => $exception->getMessage(),
            ]);

            throw $exception;
        }

        return $insertResult;
    }
}
