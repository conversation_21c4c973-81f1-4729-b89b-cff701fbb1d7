<?php


namespace App\Models\Logic\Data\Push\ReToBePaid;


use App\Models\Dao\OrderAssoc as OrderAssocDao;
use App\Models\Data\OrderAssoc as OrderAssocData;
use Request\AD as ADRequest;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Throwable;


class AD extends ThirdParty
{
    /**
     * @return OrderAssocDao
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 15:31
     */
    public function handle(): OrderAssocDao
    {
        $genOrderData = [
            'card_no'        => $this->data['real_auth_info']['card_no'],
            'station_id'     => $this->data['station_id'],
            'oil_num'        => $this->data['oil_num'],
            'oil_price'      => $this->data['oil_sale_price'],
            'pay_price'      => $this->data['oil_sale_price'],
            'supplier_price' => $this->data['supplier_price'],
            'priceGun'       => $this->data['mac_price'],
            'oil_money'      => $this->data['oil_money'],
            'pay_money'      => $this->data['oil_money'],
            'supplier_money' => $this->data['supplier_money'],
            'amountGun'      => $this->data['mac_money'],
            'oil_type'       => $this->data['oil_type'],
            'oil_name'       => $this->data['oil_name'],
            'oil_level'      => $this->data['oil_level'],
            'oil_time'       => $this->data['oil_time'],
            'third_order_id' => $this->getOrderNo("fo"),
            'driver_source'  => 2,
            'trade_mode'     => 65,
            'truck_no'       => $this->data['truck_no'],
            'driver_name'    => $this->data['driver_name'],
            'driver_phone'   => $this->data['driver_phone'],
            'oil_unit'       => $this->data['oil_unit'],
        ];
        $orderData = FOSS_ORDERRequest::handle('/api/oil_adapter/makeOrder', $genOrderData);
        $insertResult = OrderAssocData::insert(
            2,
            2,
            $orderData['data']['order_id'],
            '',
            explode('_', $this->data['real_auth_info']['name_abbreviation'])[0],
            '',
            '',
            json_encode([
                "type" => "makeUp",
            ]),
            '补单'
        );

        try {
            $result = ADRequest::handle("bop/T201904230000000014/nop/append", [
                'orderNo'              => $orderData['data']['order_id'],
                'oilStationName'       => $orderData['data']['station_name'],
                'oilStationNo'         => $orderData['data']['station_id'],
                'thirdGoodsNo'         => array_flip(
                                              config('oil.oil_mapping.ad.short_oil_mapping')
                                          )["{$orderData['data']['oil_name']}_{$orderData['data']['oil_type']}_{$orderData['data']['oil_level']}"],
                'goodsNumber'          => $orderData['data']['oil_num'],
                'goodsUnit'            => config(
                    "oil.oil_mapping.ad.unit_mapping.{$orderData['data']['oil_name']}",
                    ''
                ),
                'orderAmount'          => $this->data['mac_money'],
                'settleAmount'         => $this->data['oil_money'],
                'settleUnitPrice'      => $this->data['oil_sale_price'],
                'orderUnitPrice'       => $this->data['mac_price'],
                'plateNumber'          => $this->data['truck_no'],
                'driverName'           => $this->data['driver_name'],
                'driverPhone'          => $this->data['driver_phone'],
                'appendCreateTime'     => $orderData['data']['oil_time'],
                'appendRemark'         => $this->data['remark'],
                'isRelatedAppend'      => (int)empty($this->data['third_party_order_no']),
                'relationOrderNo'      => empty($this->data['third_party_order_no']) ? null : $this->data['third_party_order_no'],
                'accountingEntityCode' => self::getCustomerEntityCodeByOrgCodeAndConfigName(
                    $orderData['data']['orgcode'],
                    'AD_ORG_TO_ORG_MAPPING',
                    'AD_DEFAULT_ENTITY_CODE',
                    true
                ),
            ]);
            $insertResult->platform_order_id = $result['orderNo'];
            $insertResult->save();
        } catch (Throwable $exception) {
            $insertResult->platform_reason = $exception->getMessage();
            throw $exception;
        }
        return $insertResult;
    }
}
