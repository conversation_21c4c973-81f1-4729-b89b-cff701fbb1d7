<?php


namespace App\Models\Logic\Data\Push\ReToBePaid;


use App\Jobs\RunTaskByJob;
use App\Models\Dao\OrderAssoc as OrderAssocDao;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Data\RegionalInfo as RegionalInfoData;
use App\Models\Logic\Trade\ReToBePaidCallback\SQ as SQReToBePaidCallback;
use Carbon\Carbon;
use Illuminate\Support\Facades\Queue;
use Request\FOSS as FOSSRequest;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Request\SQ as SQRequest;
use Throwable;


class SQ extends ThirdParty
{
    /**
     * @return OrderAssocDao
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019-07-12 15:31
     */
    public function handle(): OrderAssocDao
    {
        $data = FOSSRequest::handle('gas.org_account.account_balance', [
            'vice_no'       => $this->data['real_auth_info']['card_no'],
            'isCardBalance' => 1,
        ]);
        $genOrderData = [
            'card_no'        => $this->data['real_auth_info']['card_no'],
            'station_id'     => $this->data['station_id'],
            'oil_num'        => $this->data['oil_num'],
            'oil_price'      => $this->data['oil_sale_price'],
            'supplier_price' => $this->data['supplier_price'],
            'priceGun'       => $this->data['mac_price'],
            'oil_money'      => $this->data['oil_money'],
            'pay_money'      => $this->data['oil_money'],
            'supplier_money' => $this->data['supplier_money'],
            'amountGun'      => $this->data['mac_money'],
            'oil_type'       => $this->data['oil_type'],
            'oil_name'       => $this->data['oil_name'],
            'oil_level'      => $this->data['oil_level'],
            'oil_time'       => $this->data['oil_time'],
            'third_order_id' => $this->getOrderNo("fo"),
            'driver_source'  => 2,
            'trade_mode'     => 65,
            'truck_no'       => $this->data['truck_no'],
            'driver_name'    => $this->data['driver_name'],
            'driver_phone'   => $this->data['driver_phone'],
            'oil_unit'       => $this->data['oil_unit'],
        ];
        $orderData = FOSS_ORDERRequest::handle('/api/oil_adapter/makeOrder', $genOrderData);
        $insertResult = OrderAssocData::insert(
            2,
            2,
            $orderData['data']['order_id'],
            $genOrderData['third_order_id'],
            explode('_', $this->data['real_auth_info']['name_abbreviation'])[0],
            '',
            '',
            '',
            '补单'
        );
        $cityName = RegionalInfoData::getNameByCode($this->data['city_code'])[$this->data['city_code']] ?? '';

        try {
            $result = SQRequest::handle("DelayedWriteOilChargingInfo", [
                'truckerMobile' => $this->data['driver_phone'],
                'cardNo'        => $this->data['third_card_no'],
                'skuCode'       => $this->data['oil_type_val'] . $this->data['oil_name_val'] .
                                   $this->data['oil_level_val'],
                'skuName'       => $this->data['oil_type_val'] . $this->data['oil_name_val'] .
                                   $this->data['oil_level_val'],
                'litre'         => $this->data['oil_num'],
                'price'         => $this->data['oil_sale_price'],
                'amount'        => $this->data['oil_money'],
                'paymentAmount' => $this->data['oil_money'],
                'orderSn'       => $orderData['data']['order_id'],
                'orderTime'     => $this->data['oil_time'],
                'oilBlance'     => $data['data']['use_balance'],
                'cityName'      => $cityName,
                'stationId'     => $this->data['station_id'],
                'discountId'    => 0,
            ]);
            $insertResult->platform_order_id = $result['data']['SerialNo'];
            $insertResult->save();
            Queue::later(
                Carbon::now()->addSeconds(1),
                new RunTaskByJob(SQReToBePaidCallback::class, "handle", [
                    [
                        'order_id'  => $result['data']['SerialNo'],
                        'trade_id'  => $orderData['data']['order_id'],
                        'auth_data' => $this->data['real_auth_info'],
                    ]
                ]),
                '',
                'adapter_deal_trade'
            );
        } catch (Throwable $exception) {
            OrderAssocData::updateOrderInfoById($insertResult->id, [
                "platform_reason" => $exception->getMessage(),
            ]);

            throw $exception;
        }

        return $insertResult;
    }
}
