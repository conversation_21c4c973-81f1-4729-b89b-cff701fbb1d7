<?php


namespace App\Models\Logic\Order;

use App\Models\Logic\Base;
use App\Models\Logic\Order\Query\FO;
use App\Models\Logic\Order\Query\GS;
use App\Models\Logic\Order\Query\GSP;
use App\Models\Logic\Order\Query\KY;
use App\Models\Logic\Order\Query\MB;
use App\Models\Logic\Order\Query\SC;
use App\Models\Logic\Order\Query\SP;
use App\Models\Logic\Order\Query\YC;
use App\Models\Logic\Order\Query\YGY;
use App\Models\Logic\Order\Query\ZY;
use Exception;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class Query extends Base
{
    private $workerMapping = [
        'zy'   => ZY::class,
        'yc'   => YC::class,
        'sc'   => SC::class,
        'sp'   => SP::class,
        'fo'   => FO::class,
        'sx'   => SC::class,
        'fj'   => SC::class,
        'gs'   => GS::class,
        'gsp'  => GSP::class,
        'hb'   => SC::class,
        'ygy'  => YGY::class,
        'mb'   => MB::class,
        'ky'   => KY::class,
        'zjqp' => SC::class,
    ];

    /**
     * @var ZY
     */
    private $worker;

    /**
     * Query constructor.
     * @param array $parameter
     * @throws Exception
     */
    public function __construct(array $parameter)
    {
        parent::__construct($parameter);

        if (!isset($this->workerMapping[$this->name_abbreviation])) {
            throw new Exception(config("error.4030004"), 4030004);
        }

        $this->worker = new $this->workerMapping[$this->name_abbreviation]($this->data);
    }

    /**
     * @return JsonResponse|Response
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/12/20 5:45 下午
     */
    public function handle()
    {
        return $this->worker->handle();
    }
}
