<?php


namespace App\Models\Logic\Order\Query;


use App\Models\Data\Log\ResponseLog;
use App\Models\Data\OrderAssoc as OrderAssocData;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Throwable;


class GS extends ThirdParty
{
    /**
     * @return Response|JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/12/20 5:20 下午
     */
    public function handle()
    {
        $responseCall = function (int $code, string $msg = '', Throwable $exception = null) {

            ResponseLog::handle([
                'responseCode' => $code,
                'responseMsg'  => config("error.$code", $msg),
                'exception'    => $exception,
            ]);
            return response()->json([
                'errorcode' => (string)$code,
                'success'   => $code == 0,
                'errormsg' => config("error.$code", $msg)
            ]);
        };

        try {

            $orderAssocData = OrderAssocData::getOrderInfoByWhere([
                [
                    'field'    => 'self_order_id',
                    'operator' => '=',
                    'value'    => $this->data['order_no'],
                ],
                [
                    'field'    => 'platform_name',
                    'operator' => '=',
                    'value'    => $this->name_abbreviation,
                ],
            ], ['self_order_id', 'extend', 'platform_order_status']);
            if (empty($orderAssocData)) {

                return $responseCall(5000024);
            }

            $data = FOSS_ORDERRequest::handle('/api/oil_adapter/getOrderItem', [
                'order_id' => $orderAssocData['self_order_id']
            ]);
            if (empty($data['data'])) {

                return $responseCall(5000001);
            }
            if ($data['data']['order_status'] != 2) {

                return $responseCall(5000103);
            }
        } catch (Throwable $exception) {

            return $responseCall(5000001, '', $exception);
        }

        return $responseCall(0);
    }
}
