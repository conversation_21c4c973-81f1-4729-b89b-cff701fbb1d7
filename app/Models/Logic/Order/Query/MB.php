<?php


namespace App\Models\Logic\Order\Query;


use Illuminate\Http\JsonResponse;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Throwable;


class MB extends ThirdParty
{
    public const ORDER_STATUS_MAPPING = [
        1 => 10,
        2 => 25,
        3 => 30,
        4 => 30,
        5 => 40,
        6 => 60
    ];

    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/12/20 5:20 下午
     */
    public function handle()
    {
        $this->filterEmptyValue($this->data);
        $queryParams = [
            'org_code' => $this->data['auth_data']['role_code']
        ];
        if (!empty($this->data['originalOrderNo'])) {
            $queryParams['order_id'] = $this->data['originalOrderNo'];
        }
        if (!empty($this->data['orderNo'])) {
            $queryParams['third_order_id'] = $this->data['orderNo'];
        }
        $data = FOSS_ORDERRequest::handle("/api/oil_adapter/getOrderItem", $queryParams);
        if (empty($data['data'])) {
            return responseFormat(5000060);
        }
        return responseFormat(0, [
            'orderNo'         => $data['data']['third_order_id'],
            'originalOrderNo' => $data['data']['order_id'],
            'originalAmount'  => (int)bcmul($data['data']['mac_amount'], 100),
            'settleUnitPrice' => (int)bcmul($data['data']['oil_price'], 100),
            'settleAmount'    => (int)bcmul($data['data']['oil_money'], 100),
            'tradeStatus'     => self::ORDER_STATUS_MAPPING[$data['data']['order_status']],
            'tradeMsg'        => $data['data']['remark'],
            'tradeDate'       => $data['data']['pay_time'] != '0000-00-00 00:00:00' ? $data['data']['pay_time'] : '',
        ]);
    }
}
