<?php


namespace App\Models\Logic\Order\Query;


use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Data\StationUniqueMapping as StationUniqueMappingData;
use Illuminate\Http\JsonResponse;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Throwable;


class ZY extends ThirdParty
{
    protected $orderStatusMapping = [
        1 => "doing",
        2 => "success",
        6 => "refund",
        4 => "cancel",
    ];

    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019/12/20 5:20 下午
     */
    public function handle()
    {
        $this->filterEmptyValue($this->data);
        $orderAssocData = OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'platform_order_id',
                'operator' => '=',
                'value'    => $this->data['order_id'],
            ],
            [
                'field'    => 'platform_name',
                'operator' => '=',
                'value'    => $this->name_abbreviation,
            ],
        ], ['self_order_id'], true, true);
        if (!$orderAssocData) {

            return responseFormat(5000018, []);
        }
        $data = FOSS_ORDERRequest::handle("/api/oil_adapter/getOrderItem", [
            'order_id' => $orderAssocData->self_order_id,
        ]);

        $data['data'] = [$data['data']];
        foreach ($data['data'] as &$v) {

            if ($v['order_status'] == 6) {

                $v['pay_status'] = 4;
            }

            $v = [
                "tradeId"     => $v['order_id'],
                "zyTradeId"   => $v['third_order_id'],
                "orderStatus" => $this->orderStatusMapping[$v['order_status']],
                "stationId"   => StationUniqueMappingData::getDataByStationId($v['station_id'],
                        ['external_station_id'])['external_station_id'] ?? '',
            ];
        }

        return responseFormat(0, $data['data']);
    }
}
