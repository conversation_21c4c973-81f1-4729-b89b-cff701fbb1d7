<?php


namespace App\Models\Logic\Order\ReToBePaid;


use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Logic\Data\Push\ReToBePaid as ReToBePaidLogic;
use Exception;
use Illuminate\Http\JsonResponse;
use Request\FOSS_STATION as FOSS_STATIONRequest;
use Throwable;


class FO extends ThirdParty
{
    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/10/31 10:14 上午
     */
    public function handle(): JsonResponse
    {
        $realAuthInfo = AuthInfoData::getAuthInfoByRoleCode($this->data['orgcode'], true);
        if (empty($realAuthInfo)) {
            throw new Exception("", 5000071);
        }

        $stationAllData = FOSS_STATIONRequest::handle("v1/station/getStationById", [
            'id' => $this->data['station_id'],
        ])['data']['stationInfo'] ?? [];
        if (empty($stationAllData)) {
            throw new Exception("", 5000027);
        }

        $authInfo = AuthInfoData::getAuthInfoByRoleCode($stationAllData['pcode'], true);
        $this->data['oil_name_val'] = array_flip(config('oil.oil_type'))[$this->data['oil_name']] ?? '';
        $this->data['oil_type_val'] = str_replace(
            $this->data['oil_name_val'],
            '',
            config("oil.oil_no_simple.{$this->data['oil_type']}", "")
        );
        $this->data['oil_level_val'] = array_flip(config('oil.oil_level'))[$this->data['oil_level']] ?? '';
        $this->data['role_code'] = [$realAuthInfo['role_code']];
        $this->data['auth_data'] = $authInfo;
        $this->data['real_auth_info'] = $realAuthInfo;
        $result = (new ReToBePaidLogic($this->data))->handle();
        return responseFormat(0, [
            'order_id' => $result->self_order_id,
            'third_order_id' => $result->platform_order_id,
        ]);
    }
}
