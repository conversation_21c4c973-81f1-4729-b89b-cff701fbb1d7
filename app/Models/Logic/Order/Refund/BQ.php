<?php


namespace App\Models\Logic\Order\Refund;


use Request\BQ as BQRequest;
use Throwable;


class BQ extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-18 17:35
     */
    public function handle()
    {
        BQRequest::handle("trade/refund", [
            'order_id' => $this->orderAssocData['platform_order_id'],
        ]);
    }
}
