<?php

namespace App\Models\Logic\Order\Refund\Check;

use App\Models\Data\DockingPlatformInfo;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Coupon\JHCX as JHCXCoupon;
use Exception;
use Request\JHCX as JHCXRequest;

class JHCX extends ThirdParty
{
    /**
     * @throws Exception
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2022/4/22 15:28
     */
    public function handle()
    {
        $orderAssocData = OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'self_order_id',
                'operator' => '=',
                'value'    => $this->data['self_order_id'],
            ],
            [
                'field'    => 'platform_name',
                'operator' => '=',
                'value'    => $this->data['platform_name'],
            ],
        ], ['*']);
        if (!$orderAssocData) {

            return;
        }
        $response = JHCXRequest::handle("/Api/DiyCard/code_get", [
            "code" => $orderAssocData['platform_order_id']
        ]);
        switch ($response['detail']['code_status']) {
            case 'NORMAL':
            case 'UNAVAILABLE':
                throw new Exception(str_replace([
                    'supplier_name',
                    'status_desc',
                ], [
                    DockingPlatformInfo::getPlatformNameByNameAbbreviation($this->data['platform_name']),
                    JHCXCoupon::COUPON_STATUS_DESC[$response['detail']['code_status']],
                ], config("error.5000123")), 5000123);
            case 'CONSUMED':
                throw new Exception(config("error.5000122"), 5000122);
        }
    }
}
