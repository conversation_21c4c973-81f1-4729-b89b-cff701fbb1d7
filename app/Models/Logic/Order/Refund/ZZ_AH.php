<?php


namespace App\Models\Logic\Order\Refund;


use Request\ZZ_AH as ZZ_AHRequest;
use Throwable;


class ZZ_AH extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019-07-18 17:35
     */
    public function handle()
    {
        ZZ_AHRequest::handle("delTradeLk", [
            'order_id' => $this->orderAssocData['platform_order_id'],
        ]);
    }
}
