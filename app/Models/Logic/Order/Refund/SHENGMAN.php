<?php


namespace App\Models\Logic\Order\Refund;


use Request\SHENGMAN as SHENGMANRequest;
use Throwable;


class SHENGMAN extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhu<PERSON><PERSON>@zhongqixing.com>
     * @since 2019-07-18 17:35
     */
    public function handle()
    {
        SHENGMANRequest::handle("trade", [
            'cmd'  => '3020.SMTKHT.6DC312',
            'data' => [
                'order_id' => $this->orderAssocData['platform_order_id'],
            ],
        ]);
    }
}
