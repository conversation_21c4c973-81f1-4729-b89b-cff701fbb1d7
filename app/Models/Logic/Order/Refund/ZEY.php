<?php


namespace App\Models\Logic\Order\Refund;


use Request\ZEY as ZEYRequest;
use Throwable;

class ZEY extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-18 17:35
     */
    public function handle()
    {
        ZEYRequest::handle("cancelOrder/add", [
            'customerOrderId' => $this->orderAssocData['platform_order_id'],
        ]);
    }
}
