<?php


namespace App\Models\Logic\Order\Refund;


use Request\DESP as DESPRequest;
use Throwable;

class DESP2H8BBD extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019-07-18 17:35
     */
    public function handle()
    {
        DESPRequest::handle("open_api/v1/order/oa/refund", [
            'order_id' => $this->orderAssocData['platform_order_id'],
        ], explode('|', $this->orderAssocData['platform_name'])[1]);
    }
}
