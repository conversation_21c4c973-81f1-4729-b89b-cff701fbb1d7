<?php


namespace App\Models\Logic\Order\Refund;


use Request\HLJH as HLJHRequest;
use Throwable;

class HLJH extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-18 17:35
     */
    public function handle()
    {
        HLJHRequest::handle("ht/refund", [
            'order_id' => $this->data['platform_order_id'],
        ]);
    }
}
