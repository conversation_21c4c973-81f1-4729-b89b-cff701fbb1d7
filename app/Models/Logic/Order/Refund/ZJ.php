<?php


namespace App\Models\Logic\Order\Refund;


use Request\ZJ as ZJRequest;
use Throwable;


class ZJ extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhu<PERSON><PERSON>@zhongqixing.com>
     * @since 2019-07-18 17:35
     */
    public function handle()
    {
        ZJRequest::handle("refundNotice", 'post', [
            'orderId'      => $this->orderAssocData['platform_order_id'],
            'reason'       => '系统自动',
            'refundStatus' => 1,
        ]);
    }
}
