<?php


namespace App\Models\Logic\Order\Refund;


use Request\MYB as MYBRequest;
use Throwable;

class MYB extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-18 17:35
     */
    public function handle()
    {
        MYBRequest::handle("refund", [
            'order_id' => $this->orderAssocData['platform_order_id'],
        ]);
    }
}
