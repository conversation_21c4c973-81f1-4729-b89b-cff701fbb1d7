<?php


namespace App\Models\Logic\Order\Refund;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\AuthInfo as AuthInfoData;
use Request\SP as SPRequest;
use Throwable;

class SP extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-18 17:35
     */
    public function handle()
    {
        $extend = json_decode($this->orderAssocData['extend'], true);
        $realNameAbbreviation = explode('_', $this->orderAssocData['platform_name'])[0];
        $authInfo = AuthInfoData::getAuthInfoFieldByNameAbbreviation($realNameAbbreviation);
        SPRequest::handle(
            "api/trade/refund",
            [
                'order_sn'          => $this->orderAssocData['platform_order_id'],
                'refund_order_type' => 1,
                'refund_money'      => $extend['money'],
                'refund_action'     => 1,
                'refund_reason'     => '其他',
            ],
            AuthConfigData::getAuthConfigValByName("SP_APP_KEY_" . $authInfo['role_code'])
            ,
            AuthConfigData::getAuthConfigValByName("SP_APP_SECRET_" . $authInfo['role_code'])
        );
    }
}
