<?php


namespace App\Models\Logic\Order\Refund;


use Request\XM as XMRequest;
use Throwable;


class XM extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-18 17:35
     */
    public function handle()
    {
        XMRequest::handle("oil/notice/g7/refund", [
            'order_id' => $this->orderAssocData['platform_order_id'],
        ]);
    }
}
