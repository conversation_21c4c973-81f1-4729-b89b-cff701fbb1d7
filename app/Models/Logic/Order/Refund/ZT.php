<?php
// 在途

namespace App\Models\Logic\Order\Refund;


use Request\ZT as ZTRequest;
use Throwable;


class ZT extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-18 17:35
     */
    public function handle()
    {
        ZTRequest::handle("g7/order_refund", [
            'order_id' => $this->orderAssocData['platform_order_id'],
        ]);
    }
}
