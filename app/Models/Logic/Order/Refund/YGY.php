<?php


namespace App\Models\Logic\Order\Refund;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use Exception;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Request\YGY as YGYRequest;
use Throwable;
use Tool\Alarm\FeiShu;


class YGY extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-18 17:35
     */
    public function handle()
    {
        $data = FOSS_ORDERRequest::handle('/api/oil_adapter/getOrderItem', [
            'order_id' => $this->orderAssocData['self_order_id'],
        ]);
        $responseData = YGYRequest::handle("other/callback/refundOrder", [
            'originalOrderNo' => $this->orderAssocData['platform_order_id'],
            'foreignUniqueId' => $this->orderAssocData['self_order_id'],
            'refundAmount'    => $data['data']['oil_money'],
            'sourceKey'       => AuthConfigData::getAuthConfigValByName("YGY_SOURCE_KEY"),
        ], 60, true);
        if (isset($responseData['result']['status']) and $responseData['result']['status'] == 1) {
            (new FeiShu())->customerRefundCallback(
                AuthConfigData::getAuthConfigValByName('YGY_REFUND_CALLBACK_ACCESS_TOKEN'),
                [
                    'platform_name' => DockingPlatformInfoData::getPlatformNameByNameAbbreviation(
                        $this->name_abbreviation
                    ),
                    'self_order_id' => $this->orderAssocData['self_order_id'],
                    'platform_order_id' => $this->orderAssocData['platform_order_id'],
                    'result' => '退款成功',
                    'reason' => '',
                ]
            );
        }
        if (isset($this->data['need_call_order']) and
            $this->data['need_call_order'] != 2 and
            isset($responseData['result']['status']) and
            $responseData['result']['status'] != 1) {
            throw new Exception("", 5000137);
        }
    }
}
