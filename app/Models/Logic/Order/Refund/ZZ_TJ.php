<?php


namespace App\Models\Logic\Order\Refund;


use Request\ZZ_TJ as ZZ_TJRequest;
use Throwable;


class ZZ_TJ extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019-07-18 17:35
     */
    public function handle()
    {
        ZZ_TJRequest::handle("delTradeTj", [
            'order_id' => $this->orderAssocData['platform_order_id'],
        ]);
    }
}
