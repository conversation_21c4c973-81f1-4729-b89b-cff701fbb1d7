<?php


namespace App\Models\Logic\Order\Refund;


use Request\HK as HKRequest;
use Throwable;


class HK extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019-07-18 17:35
     */
    public function handle()
    {
        HKRequest::handle("/g7/order/refund.action", [
            'order_id' => $this->orderAssocData['platform_order_id'],
        ]);
    }
}
