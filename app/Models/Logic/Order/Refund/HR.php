<?php


namespace App\Models\Logic\Order\Refund;


use Illuminate\Http\JsonResponse;
use Request\HR as HRRequest;
use Throwable;


class HR extends ThirdParty
{
    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-18 17:35
     */
    public function handle()
    {
        HRRequest::handle("app-service/callback/g7/cancel/callback", [
            'order_id' => $this->orderAssocData['self_order_id'],
        ]);
        return responseFormat(0, []);
    }
}
