<?php
// 中通

namespace App\Models\Logic\Order\Refund;


use Request\ZTO as ZTORequest;
use Throwable;


class ZTO extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-18 17:35
     */
    public function handle()
    {
        ZTORequest::handle("com.ztocc.transport.v1.g7CancelOrder", [
            'order_id' => $this->orderAssocData['platform_order_id'],
        ]);
    }
}
