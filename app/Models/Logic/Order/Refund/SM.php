<?php


namespace App\Models\Logic\Order\Refund;


use Request\SM as SMRequest;
use Throwable;

class SM extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019-07-18 17:35
     */
    public function handle()
    {
        SMRequest::handle("refund.do", [
            'order_id' => $this->data['platform_order_id'],
        ]);
    }
}
