<?php


namespace App\Models\Logic\Order\Refund;


use Request\CHTX as CHTXRequest;
use Throwable;

class CHTX extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-18 17:35
     */
    public function handle()
    {
        CHTXRequest::handle("refund", "post", [
            'order_id' => $this->orderAssocData['platform_order_id'],
        ]);
    }
}
