<?php


namespace App\Models\Logic\Order\ToBePaid\Downstream;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Common as CommonData;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Data\Push\AutonomousOrder;
use App\Models\Logic\Order\ToBePaid\ThirdParty;
use App\Models\Logic\Trade\Pay\Simple as TradePaySimpleLogic;
use Exception;
use Illuminate\Http\JsonResponse;
use Request\FOSS as FOSSRequest;
use Request\FOSS_ORDER as FOSS_ORDERequest;
use Request\FOSS_STATION as FOSS_STATIONRequest;
use Throwable;


class GBDW extends ThirdParty
{
    /**
     * @return JsonResponse
     * @throws Exception|Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/12/31 11:22 上午
     */
    public function handle(): JsonResponse
    {
        try {

            $cardNo = $this->data['auth_data']['card_no'];
            if (!empty($this->data['org_code'])) {

                $realAuthInfo = AuthInfoData::getAuthInfoByRoleCode($this->data['org_code'], true);
                if (empty($realAuthInfo)) {

                    return responseFormat(4120402);
                }
                $cardNo = $realAuthInfo['card_no'];
            }
            $stationInfo = FOSS_STATIONRequest::handle("v1/station/getStationById", [
                'id' => $this->data['station_id']
            ]);
            if (!in_array($stationInfo['data']['stationInfo']['trade_type'],
                CommonData::RECEIVE_TRADE_FOR_DOWNSTREAM_BY_TRADE_ENUM)) {

                throw new Exception("", 5000117);
            }
            $convertOilMapping = [];
            foreach (config("oil.oil_mapping.gbdw.mapping") as $k => $v) {

                if (!isset($convertOilMapping[$v])) {

                    $convertOilMapping[$v] = [];
                }
                $convertOilMapping[$v][] = $k;
            }
            if (!isset($convertOilMapping[$this->data['oil_type_id']])) {

                return responseFormat(4120140);
            }
            $oilAttributes = explode("_", $convertOilMapping[$this->data['oil_type_id']][0]);
            $oilName = $oilAttributes[0] ?? '';
            $oilType = $oilAttributes[1] ?? '';
            $oilLevel = $oilAttributes[2] ?? '';
            if (in_array($stationInfo['data']['stationInfo']['pcode'], json_decode(AuthConfigData::getAuthConfigValByName(
                'ORDER_NEED_GUN_INFO_FOR_SUPPLIER_CODE')))) {

                if (empty($this->data['gun_price'])) {

                    $this->data['gun_price'] = 0;
                }
                if (!is_numeric($this->data['gun_price'])) {

                    throw new Exception("", 4120485);
                }
            }
            $selfSuppliers = array_keys(FOSSRequest::handle('g7s.station.getCanPcode', [
                'is_self_operated' => 1,
            ])['data']);
            $autonomousOrderObj = (new AutonomousOrder([
                'data' => [
                    'pcode' => $stationInfo['data']['stationInfo']['pcode'],
                ],
            ], false));
            $response = FOSS_ORDERequest::handle('/api/oil_adapter/makeOrder', [
                'card_no'        => $cardNo,
                'driver_name'    => $this->data['driver_name'] ?? '',
                'driver_phone'   => $this->data['driver_phone'],
                'truck_no'       => $this->data['truck_no'] ?? '',
                'station_id'     => $this->data['station_id'],
                'oil_name'       => $oilName,
                'oil_type'       => $oilType,
                'oil_level'      => $oilLevel,
                'oil_unit'       => $this->data['deduction_mode'],
                'oil_num'        => $this->data['oil_num'],
                'oil_price'      => $this->data['price'],
                'oil_money'      => $this->data['money'],
                'pay_money'      => $this->data['money'],
                'third_order_id' => $this->data['order_id'],
                'trade_mode'     => in_array($stationInfo['data']['stationInfo']['pcode'], $selfSuppliers) ? 10 : (
                $autonomousOrderObj->checkWorkerExists() ? 32 : 30),
                'driver_source'  => 2,
                'priceGun'       => $this->data['gun_price'] ?? 0,
                'amountGun'      => $this->data['gun_money'] ?? 0,
                'gunNumber'      => (int)($this->data['gun_number'] ?? ''),
            ]);
            OrderAssocData::insert(2, 2, $response['data']['order_id'],
                $this->data['order_id'], $this->name_abbreviation, "", "",
                json_encode(array_merge($response['data'], [
                    'trade_type'    => $stationInfo['data']['stationInfo']['trade_type'],
                    'supplier_code' => $stationInfo['data']['stationInfo']['pcode'],
                    'orgcode'       => $this->data['auth_data']['role_code'],
                ])));
            (new TradePaySimpleLogic([
                'auth_data'            => $this->data['auth_data'],
                "trade_id"             => $response["data"]["order_id"],
                "order_id"             => $this->data["order_id"],
                "pay_status"           => 1,
                "pay_reason"           => '',
                "updatePlatformReason" => false,
            ]))->handle(true);
        } catch (Throwable $t) {

            return responseFormat($t->getCode(), null, false, $t->getMessage());
        }
        return responseFormat(0, [
            'trade_id' => $response['data']['order_id'],
        ]);
    }
}
