<?php


namespace App\Models\Logic\Order\ToBePaid\Downstream;


use App\Jobs\PushTradeToGas;
use App\Models\Data\AuthConfig;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Order\ToBePaid\ThirdParty;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Queue;
use Request\FOSS as FOSSRequest;
use Throwable;


class HTX extends ThirdParty
{
    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/10/31 10:14 上午
     */
    public function handle(): JsonResponse
    {
        $this->data['oilLevelId'] = '';
        $this->data['oilNameId'] = config("oil.oil_type.{$this->data['oil_name']}");
        $this->data['oilTypeId'] = '';
        $this->data['oil_time'] = $this->data['refueling_time'];
        if (isset($this->data['oil_type']) and !empty($this->data['oil_type'])) {

            $this->data['oilTypeId'] = array_flip(config("oil.oil_no_simple"))[$this->data['oil_type']];
        }
        if (isset($this->data['oil_level']) and !empty($this->data['oil_level'])) {

            $this->data['oilLevelId'] = config("oil.oil_level.{$this->data['oil_level']}");
        }
        //验证扣费账户是否存在及余额是否充足
        $costAccountMapping = json_decode(AuthConfig::getAuthConfigValByName(
                "HTX_COMPANY_TO_CARD_MAPPING"), true) ?? [];
        if (!isset($costAccountMapping[$this->data['company_subject_name']])) {

            return responseFormat(4120469);
        }
        $authInfo = AuthInfoData::getAuthInfoFieldByNameAbbreviation($this->name_abbreviation .
            '_' . $costAccountMapping[$this->data['company_subject_name']], '*', true);
        if (empty($authInfo)) {

            return responseFormat(4120469);
        }
        $mainNameAbbreviation = $this->name_abbreviation;
        $this->data['auth_data'] = $authInfo;
        $this->name_abbreviation = $authInfo['name_abbreviation'];
        $data = FOSSRequest::handle('gas.org_account.account_balance', [
            'vice_no'       => $this->data['auth_data']['card_no'],
            'isCardBalance' => 1,
        ]);
        if (bccomp($data['data']['use_balance'], $this->data['money'], 2) < 0) {

            return responseFormat(5000114);
        }
        //创建订单并下发异步任务队列推送订单到交易系统完成后续操作
        $result = OrderAssocData::insertBySearchForPlatform([
            'self_order_status'     => 1,
            'platform_order_id'     => $this->data['order_id'],
            'platform_name'         => $mainNameAbbreviation,
            'platform_order_status' => 1,
            'refueling_time'        => $this->data['refueling_time'],
            'extend'                => json_encode([
                'taskData'          => $this->data,
                'name_abbreviation' => $this->name_abbreviation
            ]),
        ]);
        if (isset($result['allowPushTask']) and $result['allowPushTask']) {

            $this->data['orderAssocData'] = $result;
            Queue::push(new PushTradeToGas($this->data, $result, $this->data['auth_data']), '',
                "adapter_deal_trade");
        }
        //返回虚拟单号，优先通知用户交易成功
        return responseFormat(0, ['trade_id' => "{$this->data['order_id']}-{$result['id']}"]);
    }
}
