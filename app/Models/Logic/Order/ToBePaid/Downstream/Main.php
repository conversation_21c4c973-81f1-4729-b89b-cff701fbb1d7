<?php


namespace App\Models\Logic\Order\ToBePaid\Downstream;


use App\Models\Logic\Order\ToBePaid\ThirdParty;
use Illuminate\Http\JsonResponse;
use Throwable;


class Main extends ThirdParty
{
    private $workerMapping = [
        ''            => Simple::class,
        'SPTXXTZ'     => Sp::class,
        'SPT5CWE'     => Sp::class,
        'SPT3SB7'     => Sp::class,
        'SPTLHLX'     => Sp::class,
        'SPTCDNF'     => Sp::class,
        'SPT76YH'     => Sp::class,
        'SPSL8KY'     => Sp::class,
        'SPT5GZG'     => Sp::class,
        'SPTMKCF'     => Sp::class,
        'SPTHZTD'     => Sp::class,
        'SPTG7PR'     => Sp::class,
        'SPTRR3N'     => Sp::class,
        'SPTNGHH'     => Sp::class,
        'SPTYW4A'     => Sp::class,
        'SPTHSQW'     => Sp::class,
        'SPT5RLT'     => Sp::class,
        'rq'          => Rq::class,
        'htx'         => HTX::class,
        'ygy'         => YGY::class,
        'fy'          => FY::class,
        'desp|2H8BBD' => DESP2H8BBD::class,
        'desp|2A6LA9' => DESP2A6LA9::class,
        'gbdw'        => GBDW::class,
        'desp|2C637M' => DESP2C637M::class,
        'zj'          => Zj::class,
        'hr'          => HR::class,
        'hll'         => HLL::class,
        'ad'          => AD::class,
        'zey'         => ZEY::class,
        'ky'          => KY::class,
        'sfsx'        => SFSX::class,
    ];

    /**
     * @var ThirdParty
     */
    private $worker;

    /**
     * Main constructor.
     * @param array $parameter
     * @throws Throwable
     */
    public function __construct(array $parameter)
    {
        parent::__construct($parameter);

        if (!isset($this->workerMapping[$this->data['auth_data']['real_name_abbreviation']])) {
            $this->worker = new $this->workerMapping['']($this->data);
            return;
        }

        $this->worker = new $this->workerMapping[$this->data['auth_data']['real_name_abbreviation']]($this->data);
    }

    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/8/17 4:20 下午
     */
    public function handle(): JsonResponse
    {
        return $this->worker->handle();
    }
}
