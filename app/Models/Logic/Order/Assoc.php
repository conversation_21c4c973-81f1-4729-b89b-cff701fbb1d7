<?php


namespace App\Models\Logic\Order;


use App\Jobs\PushTradeToGas;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Base;
use App\Models\Logic\Code\GetSecondaryPaymentQrCode as GetSecondaryPaymentQrCodeLogic;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Queue;
use Throwable;


class Assoc extends Base
{
    public function getData()
    {
        if (!empty($this->data['created_at'])) {

            $searchTime = explode(' - ', $this->data['created_at']);
            $this->data['createdStartTime'] = trim($searchTime[0]);
            $this->data['createdEndTime'] = trim($searchTime[1]);
        }

        if (!empty($this->data['updated_at'])) {

            $searchTime = explode(' - ', $this->data['updated_at']);
            $this->data['updatedStartTime'] = trim($searchTime[0]);
            $this->data['updatedEndTime'] = trim($searchTime[1]);
        }

        $this->filterEmptyValue($this->data);

        return responseFormat(0, OrderAssocData::getData($this->data));
    }

    public function getChart()
    {
        return responseFormat(0, OrderAssocData::getChart());
    }

    public function handle()
    {
    }

    /**
     * @throws Exception
     */
    public function retryFailedOrder(): JsonResponse
    {
        $taskData = OrderAssocData::getRetryOrderById($this->data['ids']);

        if (empty($taskData)) {

            return responseFormat(5000022);
        }

        foreach ($taskData as $v) {

            $realTaskData = json_decode($v['extend'], true);

            if (is_null($realTaskData)) {

                continue;
            }

            Queue::push(new PushTradeToGas($realTaskData['taskData'], $v,
                AuthInfoData::getAuthInfoFieldByNameAbbreviation($realTaskData['name_abbreviation'],
                    '*', true)), '', "adapter_deal_trade");
        }

        return responseFormat();
    }

    /**
     * @return JsonResponse
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/9/2 2:33 下午
     */
    public function getSecondaryPaymentQrCode(): JsonResponse
    {
        $orderData = OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'id',
                'operator' => '=',
                'value'    => $this->data['id'],
            ],
        ], ['extend', 'self_order_id']);

        if (empty($orderData)) {

            return responseFormat(5000018);
        }

        $orderData['extend'] = json_decode($orderData['extend'], true);

        if (!isset($orderData['extend']['owner'])) {

            return responseFormat(5000101);
        }

        return (new GetSecondaryPaymentQrCodeLogic([
            'supplier_code' => $orderData['extend']['owner']['pcode'],
            'order_id'      => $orderData['self_order_id'],
        ]))->handle();
    }
}
