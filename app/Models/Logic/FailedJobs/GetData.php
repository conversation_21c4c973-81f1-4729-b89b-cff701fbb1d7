<?php


namespace App\Models\Logic\FailedJobs;


use App\Models\Data\Log\FailedJobs as FailedJobsData;
use App\Models\Logic\Base;

class GetData extends Base
{
    public function handle()
    {
        if (!empty($this->data['failed_at'])) {

            $searchTime = explode(' - ', $this->data['failed_at']);
            $this->data['failedStartTime'] = trim($searchTime[0]);
            $this->data['failedEndTime'] = trim($searchTime[1]);
        }

        $this->filterEmptyValue($this->data);

        return responseFormat(0, FailedJobsData::getData($this->data));
    }
}
