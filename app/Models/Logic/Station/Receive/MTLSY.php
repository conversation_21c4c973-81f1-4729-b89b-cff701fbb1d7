<?php


namespace App\Models\Logic\Station\Receive;


use App\Console\Commands\PullOilForMtlSy;
use App\Jobs\RunTaskByJob;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Queue;
use Throwable;


class MTLSY extends ThirdParty
{
    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/11/28 3:58 下午
     */
    public function handle(): JsonResponse
    {
        Queue::push(
            new RunTaskByJob(
                PullOilForMtlSy::class, "handle",
                [
                    [
                        'station_id' => $this->data['stationId'],
                    ]
                ],
                false
            ),
            '',
            'supplier_station_queue'
        );
        return responseFormat(200, "success", false);
    }
}
