<?php


namespace App\Models\Logic\Station\Receive;


use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Log\ResponseLog as Log;
use App\Models\Data\StationPrice as StationPriceData;
use Illuminate\Http\JsonResponse;
use Throwable;

class ZY extends ThirdParty
{
    public static $specialOilNameMapping = [
        'LNG' => '液化天然气',
        'CNG' => '压缩天然气',
    ];
    private $isStopMapping = [
        'online'  => 0,
        'offline' => 1,
        'pause'   => 1,
    ];
    private $isHighwayMapping = [
        'A' => 0,
        'B' => 1,
        ''  => 0,
    ];

    /**
     * @return bool|JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/11/28 3:58 下午
     */
    public function handle()
    {
        //改写省市编码,并验证省市编码正确性
        $this->data['province'] = $this->data['province'] . '000000';
        $this->data['city'] = $this->data['city'] . '000000';
        $regionData = $this->validateRegionCode([
            [
                'lng'           => $this->data['lng'],
                'lat'           => $this->data['lat'],
                'province_code' => $this->data['province'],
                'city_code'     => $this->data['city'],
            ]
        ]);
        $stationData = [
            'id'             => $this->data['stationId'],
            'station_name'   => $this->data['stationName'],
            'province_code'  => $regionData["{$this->data["lng"]},{$this->data["lat"]}"]['province_code'] ?? '',
            'city_code'      => $regionData["{$this->data["lng"]},{$this->data["lat"]}"]['city_code'] ?? '',
            'lng'            => $this->data['lng'],
            'lat'            => $this->data['lat'],
            'address'        => $this->data['address'],
            'is_stop'        => $this->isStopMapping[$this->data['status']],
            'contact_phone'  => $this->data['tel'] ?? '',
            'business_hours' => $this->data['opentime'],
            'is_highway'     => $this->isHighwayMapping[$this->data['tagType'] ?? ''],
            'price_list'     => [],
            'trade_type'     => 1,
        ];
        //如果为直辖市,那么省市代码一致
        if (checkIsMunicipality($stationData['city_code'])) {

            $stationData['province_code'] = $stationData['city_code'];
        }
        $subAuthInfo = AuthInfoData::getAuthInfoFieldByNameAbbreviation("zyLng");
        $insertStationData = $stationData;
        $insertStationData['station_id'] = $stationData['id'];
        $insertStationData['oil_price_list'] = [];
        $insertStationData['extends'] = [];
        $skuCodes = array_unique(array_column($this->data['guns'], 'skuCode'));
        $onlyLngFlag = (in_array("T100L01", $skuCodes) and count($skuCodes) == 1);
        foreach ($this->data['guns'] as $cv) {

            if (!$onlyLngFlag and $cv['skuCode'] == 'T100L01') {

                continue;
            }
            $oilInfo = explode(" ", $cv['skuName']);
            $realOilInfo = explode("_", config("oil.oil_mapping.zy.{$cv['skuCode']}"));
            $oilPriceTemp = [];
            $insertOilPriceTemp = [];
            $oilPriceTemp['oil_type'] = $realOilInfo[1] ?? "";
            $oilPriceTemp['oil_level'] = $realOilInfo[2] ?? "";
            $oilPriceTemp['oil_name'] = $realOilInfo[0] ?? "";
            $insertOilPriceTemp['oil_no'] = count($oilInfo) > 1 ? $oilInfo[0] : '';
            $insertOilPriceTemp['oil_level'] = $oilInfo[2] ?? '';
            $insertOilPriceTemp['oil_type'] = count($oilInfo) > 1 ? $oilInfo[1] :
                self::$specialOilNameMapping[$oilInfo[0]];
            if (empty($oilPriceTemp['oil_name'])) {

                Log::handle([
                    'msg' => "Oil's attribute is invalid",
                ]);
                continue;
            }

            $insertOilPriceTemp['gun_no'] = $cv['gunId'];
            $insertStationData['enabled_state'] = self::STATION_ENABLE_STATE_ENUM[$insertStationData['is_stop']];
            $insertOilPriceTemp['sale_price'] = bcmul($cv['lvPrice'], 100);
            $insertOilPriceTemp['listing_price'] = bcmul($cv['listedPrice'], 100);
            $insertOilPriceTemp['issue_price'] = bcmul($cv['basePrice'], 100);
            $oilPriceTemp['price'] = $cv['lvPrice'];
            $oilPriceTemp['mac_price'] = $cv['listedPrice'];
            $stationData['price_list'][] = $oilPriceTemp;
            $insertStationData['oil_price_list'][] = $insertOilPriceTemp;
            $skuKey = "{$insertOilPriceTemp['oil_no']}{$insertOilPriceTemp['oil_type']}{$insertOilPriceTemp['oil_level']}_skuCode";
            $insertStationData['extends'][$skuKey] = $cv['skuCode'];
        }

        if (isset($this->data['sellType'])) {
            $this->data['sellType'] = json_decode($this->data['sellType'], true) ?? [];
            if ($onlyLngFlag) {
                if (in_array('userss', $this->data['sellType'])) {

                    $stationData['trade_type'] = 3;
                } elseif (in_array('oilerqr', $this->data['sellType'])) {

                    $stationData['trade_type'] = 1;
                } else {

                    $stationData['is_stop'] = 1;
                }
            } else {
                if (in_array('oilerqr', $this->data['sellType'])) {

                    $stationData['trade_type'] = 1;
                } elseif (in_array('userss', $this->data['sellType'])) {

                    $stationData['trade_type'] = 3;
                } else {

                    $stationData['is_stop'] = 1;
                }
            }
        }
        $this->data['auth_data'] = $onlyLngFlag ? $subAuthInfo : $this->data['auth_data'];
        $insertStationData['platform_code'] = $this->data['auth_data']['role_code'];
        $this->accessKey = $onlyLngFlag ? $subAuthInfo['access_key'] : $this->accessKey;
        $insertStationData['extends'] = json_encode($insertStationData['extends']);
        StationPriceData::create($insertStationData);
        return $this->pushStationToStationCenter($stationData, $this->data['stationId']);
    }
}
