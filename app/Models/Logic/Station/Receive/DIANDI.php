<?php


namespace App\Models\Logic\Station\Receive;


use App\Models\Data\StationPrice as StationPriceData;
use Illuminate\Http\JsonResponse;
use Throwable;


class DIANDI extends ThirdParty
{

    /**
     * @return bool|JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhu<PERSON><PERSON>@zhongqixing.com>
     * @since 2019/11/28 3:45 下午
     */
    public function handle()
    {
        //改写省市编码,并验证省市编码正确性
        $this->data['province_code'] = (string)$this->data['province_code'];
        $this->data['city_code'] = (string)$this->data['city_code'];
        $checkResult = $this->validateAreaCode($this->data['province_code']);

        if (!$checkResult) {

            responseFormat(4120126, [], true);
        }

        $checkResult = $this->validateAreaCode($this->data['city_code']);

        if (!$checkResult) {

            responseFormat(4120128, [], true);
        }

        $stationData = [
            'id'            => $this->data['id'],
            'station_name'  => $this->data['station_name'],
            'province_code' => $this->data['province_code'],
            'city_code'     => $this->data['city_code'],
            'lng'           => $this->data['lng'],
            'lat'           => $this->data['lat'],
            'address'       => $this->data['address'],
            'is_stop'       => $this->data['is_stop'],
            'rebate_grade'  => $this->data['rebate_grade'] ?? '',
            'contact_phone' => $this->data['contact_phone'] ?? '',
            'price_list'    => $this->data['price_list'],
        ];

        foreach ($stationData['price_list'] as &$v) {

            $v['oil_name'] = config("oil.oil_type.{$v['oil_name']}");

            if (!empty($v['oil_level'])) {

                $v['oil_level'] = config("oil.oil_level_en_letter")[$v['oil_level']];
            } else {

                $v['oil_level'] = "";
            }

            if (!empty($v['oil_type'])) {

                $v['oil_type'] = array_flip(config("oil.oil_no_simple"))[$v['oil_type']];
            } else {

                $v['oil_type'] = "";
            }
        }

        if (checkIsMunicipality($stationData['city_code'])) {

            $stationData['province_code'] = $stationData['city_code'];
        }

        $insertStationPriceData = $stationData;
        $insertStationPriceData['station_id'] = $this->data['id'];
        $insertStationPriceData['enabled_state'] = self::STATION_ENABLE_STATE_ENUM[$insertStationPriceData['is_stop']];
        $insertStationPriceData['platform_code'] = $this->data['auth_data']['role_code'];
        $insertStationPriceData['oil_price_list'] = $this->data['price_list'];
        unset($insertStationPriceData['price_list']);

        foreach ($insertStationPriceData['oil_price_list'] as &$iv) {

            $iv['sale_price'] = bcmul($iv['price'], 100);
            $iv['oil_no'] = $iv['oil_type'];
            $iv['oil_type'] = $iv['oil_name'];
        }

        StationPriceData::create($insertStationPriceData);
        return $this->pushStationToStationCenter($stationData, $this->data['id']);
    }
}
