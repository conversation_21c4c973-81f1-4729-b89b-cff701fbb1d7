<?php


namespace App\Models\Logic\Station\Receive;


use App\Models\Data\StationPrice as StationPriceData;
use Throwable;


class YUNDATONG extends ThirdParty
{

    /**
     * @return void
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhu<PERSON><PERSON>@zhongqixing.com>
     * @since 2019/11/28 3:45 下午
     */
    public function handle()
    {
        try {
            //改写省市编码,并验证省市编码正确性
            $this->data['province_code'] = $this->data['provinceCode'] . '000000';
            $this->data['city_code'] = $this->data['cityCode'] . '000000';
            $regionData = $this->validateRegionCode([
                [
                    'lng'           => $this->data['lng'],
                    'lat'           => $this->data['lat'],
                    'province_code' => $this->data['province_code'],
                    'city_code'     => $this->data['city_code'],
                ]
            ]);

            $stationData = [
                'id'                => $this->data['stationId'],
                'station_name'      => $this->data['stationName'],
                'province_code'     => $regionData["{$this->data["lng"]},{$this->data["lat"]}"]['province_code'] ?? '',
                'city_code'         => $regionData["{$this->data["lng"]},{$this->data["lat"]}"]['city_code'] ?? '',
                'lng'               => $this->data['lng'],
                'lat'               => $this->data['lat'],
                'address'           => $this->data['address'],
                'is_stop'           => $this->data['status'],
                'trade_type'        => 3,
                'contact'           => '运达通',
                'contact_phone'     => $this->data['contactPhone'] ?? '',
                'station_brand'     => 8,
                'price_list'        => [],
                'station_type'      => 2,
                'station_oil_unit'  => 1,
                'allow_switch_unit' => 0,
                'business_hours'    => "24小时",
            ];
            if ($this->data['verifyMode'] != 0) {
                $stationData['is_stop'] = 1;
            }
            if ($this->data['payMode'] != 0) {
                $stationData['is_stop'] = 1;
            }
            if (checkIsMunicipality($stationData['city_code'])) {
                $stationData['province_code'] = $stationData['city_code'];
            }

            $insertStationPriceData = $stationData;
            $insertStationPriceData['oil_price_list'] = [];
            //油站价格数据
            $stationOilPrice = [];
            $oilGunNos = [];
            foreach ($this->data['priceList'] as $cv) {
                if (!isset($stationOilPrice[$cv['oilNo']])) {
                    $stationOilPrice[$cv['oilNo']] = [];
                    $oilGunNos[$cv['oilNo']] = [];
                }
                if (!empty($cv['gunList'])) {
                    array_push($oilGunNos[$cv['oilNo']], ...$cv['gunList']);
                }
                $stationOilPrice[$cv['oilNo']][] = $cv['price'] . '_' . $cv['priceGun'];
                $stationOilPrice[$cv['oilNo']] = array_unique($stationOilPrice[$cv['oilNo']]);
            }
            foreach ($this->data['priceList'] as $cv) {
                if (isset($stationOilPrice[$cv['oilNo']]) and count($stationOilPrice[$cv['oilNo']]) > 1) {
                    continue;
                }
                if (empty($oilGunNos[$cv['oilNo']])) {
                    continue;
                }
                $realOilInfo = explode("_", config("oil.oil_mapping.yundatong.{$cv['oilNo']}"));
                $oilPriceTemp = [];
                $insertOilPriceTemp = [];
                $oilPriceTemp['oil_type'] = $realOilInfo[1] ?? "";
                $oilPriceTemp['oil_level'] = $realOilInfo[2] ?? "";
                $oilPriceTemp['oil_name'] = $realOilInfo[0] ?? "";
                if (empty($oilPriceTemp['oil_name'])) {
                    continue;
                }
                $insertOilPriceTemp['oil_type'] = array_flip(
                                                      config("oil.oil_type")
                                                  )[$oilPriceTemp['oil_name']] ?? '';
                $insertOilPriceTemp['oil_no'] = str_replace(
                    $insertOilPriceTemp['oil_type'],
                    '',
                    array_flip(
                        config("oil.oil_no")
                    )[$oilPriceTemp['oil_type']] ?? ''
                );
                $insertOilPriceTemp['oil_level'] = array_flip(
                                                       config("oil.oil_level")
                                                   )[$oilPriceTemp['oil_level']] ?? '';

                $oilPriceTemp['price'] = $cv['priceGun'];
                $oilPriceTemp['mac_price'] = $cv['priceGun'];
                $insertOilPriceTemp['gun_no'] = implode(',', $oilGunNos[$cv['oilNo']]);
                $insertOilPriceTemp['sale_price'] = bcmul($cv['price'], 100);
                $insertOilPriceTemp['listing_price'] = bcmul($cv['priceGun'], 100);
                $insertOilPriceTemp['issue_price'] = bcmul($cv['priceOfficial'], 100);
                $stationData['price_list'][$cv['oilNo']] = $oilPriceTemp;
                $insertStationPriceData['oil_price_list'][$cv['oilNo']] = $insertOilPriceTemp;
            }
            if (empty($stationData['price_list'])) {
                $stationData['is_stop'] = 1;
            }
            $insertStationPriceData['station_id'] = $this->data['stationId'];
            $insertStationPriceData['enabled_state'] = self::STATION_ENABLE_STATE_ENUM[$insertStationPriceData['is_stop']];
            $insertStationPriceData['platform_code'] = $this->data['auth_data']['role_code'];
            $stationData['price_list'] = array_values($stationData['price_list']);
            $insertStationPriceData['oil_price_list'] = array_values($insertStationPriceData['oil_price_list']);
            unset($insertStationPriceData['price_list']);
            $stationData['clearGunCache'] = true;
            $stationData['gunCacheKeyPrefix'] = "gun_number_";
            StationPriceData::create($insertStationPriceData);
            $this->pushStationToStationCenter($stationData, $this->data['stationId']);
            responseFormatForYUNDATONG();
        } catch (Throwable $throwable) {
            responseFormatForYUNDATONG(
                $throwable->getCode() ?? 5000001,
                null,
                $throwable->getMessage()
            );
        }
    }
}
