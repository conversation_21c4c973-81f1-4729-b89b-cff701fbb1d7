<?php


namespace App\Models\Logic\Station\Receive;


use App\Console\Commands\PullOilForZdc;
use App\Jobs\RunTaskByJob;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Queue;
use Throwable;


class ZDC extends ThirdParty
{
    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019/11/28 3:58 下午
     */
    public function handle(): JsonResponse
    {
        Queue::push(
            new RunTaskByJob(
                PullOilForZdc::class, "handle",
                $this->data['changeType'] == 2 ? [$this->data['stationIdList']] : [],
                false
            ),
            '',
            'supplier_station_queue'
        );
        return responseFormatForZdc(200, [], '成功');
    }
}
