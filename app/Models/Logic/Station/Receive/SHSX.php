<?php


namespace App\Models\Logic\Station\Receive;


use App\Models\Data\Log\ResponseLog as Log;
use App\Models\Data\StationPrice as StationPriceData;
use Illuminate\Http\JsonResponse;
use Throwable;


class SHSX extends ThirdParty
{
    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/11/28 3:58 下午
     */
    public function handle()
    {
        try {
            $regionData = getRegionForCoordinateByGdApi(["{$this->data["lng"]},{$this->data["lat"]}"]);
            $stationData = [
                'id'                => $this->data['id'],
                'station_name'      => $this->data['title'],
                'province_code' => $regionData["{$this->data["lng"]},{$this->data["lat"]}"]['provinceCode'] ?? '',
                'city_code'     => $regionData["{$this->data["lng"]},{$this->data["lat"]}"]['cityCode'] ?? '',
                'lng'               => $this->data['lng'],
                'lat'               => $this->data['lat'],
                'address'           => $this->data['address'],
                'is_stop' => $this->data['status'] == 1 ? 0 : 1,
                'price_list'        => [],
                'station_type'      => 2,
                'trade_type'        => 3,
                'station_oil_unit'  => 1,
                'allow_switch_unit' => 0,
                'station_brand'     => config(
                    'brand.' . $this->name_abbreviation . '.' . $this->data['oil_brand'] ?? '',
                    8
                ),
                'contact'           => '刘荣虎',
                'contact_phone'     => '***********',
            ];
            if (!empty($this->data['open_start']) or !empty($this->data['open_end'])) {
                $stationData['business_hours'] = $this->data['open_start'] . '~' . $this->data['open_end'];
            }
            //如果为直辖市,那么省市代码一致
            if (checkIsMunicipality($stationData['city_code'])) {
                $stationData['province_code'] = $stationData['city_code'];
            }

            $insertStationData = $stationData;
            $insertStationData['station_id'] = $stationData['id'];
            $insertStationData['enabled_state'] = $stationData['is_stop'] + 1;
            $insertStationData['oil_price_list'] = [];
            $insertStationData['platform_code'] = $this->data['auth_data']['role_code'];
            // 经善宏确认，站点目前不提供油枪数据，需自行模拟
            if ($stationData['province_code'] == '140000000000') {
                $this->data['gun'] = [
                    [
                        'oil' => [
                            'id' => getLaravelAndMachineEnv("RUN__ENVIRONMENT") != 'prod' ? 18 : 12,
                        ],
                    ]
                ];
            }
            if (getLaravelAndMachineEnv("RUN__ENVIRONMENT") == 'prod') {
                $this->data['gun'][] = [
                    'oil' => [
                        'id' => 13,
                    ],
                ];
                $this->data['gun'][] = [
                    'oil' => [
                        'id' => 14,
                    ],
                ];
                $this->data['gun'][] = [
                    'oil' => [
                        'id' => 15,
                    ],
                ];
            }
            $checkOilRepeat = [];
            foreach ($this->data['gun'] as $cv) {
                if (in_array($cv['oil']['id'], $checkOilRepeat)) {
                    continue;
                }
                $realOilInfo = explode(
                    "_",
                    config(
                        "oil.oil_mapping.shsx.{$cv['oil']['id']}",
                        ''
                    )
                );
                $oilPriceTemp = [];
                $insertOilPriceTemp = [];
                $oilPriceTemp['oil_type'] = $realOilInfo[1];
                $oilPriceTemp['oil_level'] = $realOilInfo[2];
                $oilPriceTemp['oil_name'] = $realOilInfo[0];
                $insertOilPriceTemp['oil_no'] = config("oil.oil_no_simple.$realOilInfo[1]", "");
                $insertOilPriceTemp['oil_level'] = array_flip(config("oil.oil_level"))[$realOilInfo[2]];
                $insertOilPriceTemp['oil_type'] = array_flip(config("oil.oil_type"))[$realOilInfo[0]];
                $oilPriceTemp['price'] = 0;
                $oilPriceTemp['mac_price'] = 0;
                $insertOilPriceTemp['sale_price'] = 0;
                $insertOilPriceTemp['listing_price'] = 0;
                $insertOilPriceTemp['issue_price'] = 0;
                $stationData['price_list'][] = $oilPriceTemp;
                $insertStationData['oil_price_list'][] = $insertOilPriceTemp;
                $checkOilRepeat[] = $cv['oil']['id'];
            }
            if (empty($stationData['price_list'])) {
                $stationData['is_stop'] = 1;
            }
            StationPriceData::create($insertStationData);
            $this->pushStationToStationCenter($stationData, $this->data['id']);
        } catch (Throwable $exception) {
            $errorCode = $exception->getCode() > 0 ? $exception->getCode() : 5000001;
            if (config("error.$errorCode", "") != "") {
                responseFormatForSh($errorCode);
            }
            Log::handle([
                'exception' => $exception,
            ]);
            responseFormatForSh(5000001);
        }
        responseFormatForSh();
    }
}
