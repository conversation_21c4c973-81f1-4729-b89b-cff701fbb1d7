<?php


namespace App\Models\Logic\Station\Receive;


use App\Jobs\BasicJob;
use App\Models\Data\Log\ResponseLog as Log;
use App\Models\Data\StationPrice as StationPriceData;
use Illuminate\Http\JsonResponse;
use Request\FOSS_STATION as FOSS_STATIONRequest;
use Throwable;


class SQZSH extends ThirdParty
{
    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhu<PERSON><PERSON>@zhongqixing.com>
     * @since 2019/11/28 3:58 下午
     */
    public function handle()
    {
        try {
            $regionData = getRegionForCoordinateByGdApi(["{$this->data["lng"]},{$this->data["lat"]}"]);
            $stationData = [
                'id'                => $this->data['stationId'],
                'station_name'      => $this->data['stationName'],
                'province_code'     => $regionData["{$this->data["lng"]},{$this->data["lat"]}"]['provinceCode'] ?? '',
                'city_code'         => $regionData["{$this->data["lng"]},{$this->data["lat"]}"]['cityCode'] ?? '',
                'lng'               => $this->data['lng'],
                'lat'               => $this->data['lat'],
                'address'           => $this->data['address'],
                'is_stop'           => $this->data['status'] == 'offline' ? 1 : 0,
                'business_hours'    => $this->data['openTime'],
                'station_type'      => 2,
                'trade_type'        => 3,
                'station_brand'     => config("brand.sqzl.{$this->data['type']}", 8),
                'station_oil_unit'  => 1,
                'allow_switch_unit' => 0,
                'is_highway'        => (int)($this->data['isHighspeed'] == 'B'),
                'price_list'        => [],
            ];
            //如果为直辖市,那么省市代码一致
            if (checkIsMunicipality($stationData['city_code'])) {
                $stationData['province_code'] = $stationData['city_code'];
            }
            $insertStationData = $stationData;
            $insertStationData['station_id'] = $stationData['id'];
            $insertStationData['enabled_state'] = $stationData['is_stop'] + 1;
            $insertStationData['oil_price_list'] = [];
            $insertStationData['platform_code'] = $this->data['auth_data']['role_code'];
            $insertStationData['extends'] = [];
            foreach ($this->data['oilGunBeans'] as $cv) {
                $realOilInfo = explode("_", config("oil.oil_mapping.$this->name_abbreviation.{$cv['fuelNo']}"));
                $oilPriceTemp = [];
                $insertOilPriceTemp = [];
                $oilPriceTemp['oil_type'] = $realOilInfo[1] ?? "";
                $oilPriceTemp['oil_level'] = $realOilInfo[2] ?? "";
                $oilPriceTemp['oil_name'] = $realOilInfo[0] ?? "";
                if (empty($oilPriceTemp['oil_name'])) {
                    Log::handle("Oil's attribute is invalid", [
                        "data" => $this->data
                    ], "上汽中石化", "oil_station_data", "error");
                    continue;
                }
                $insertOilPriceTemp['oil_type'] = array_flip(
                                                      config("oil.oil_type")
                                                  )[$oilPriceTemp['oil_name']] ?? '';
                $insertOilPriceTemp['oil_no'] = str_replace(
                    $insertOilPriceTemp['oil_type'],
                    '',
                    array_flip(
                        config("oil.oil_no")
                    )[$oilPriceTemp['oil_type']] ?? ''
                );
                $insertOilPriceTemp['oil_level'] = array_flip(
                                                       config("oil.oil_level")
                                                   )[$oilPriceTemp['oil_level']] ?? '';

                $oilPriceTemp['price'] = $cv['lvPrice'];
                $oilPriceTemp['mac_price'] = $cv['lvPrice'];
                $insertOilPriceTemp['sale_price'] = bcmul($cv['lvPrice'], 100);
                $stationData['price_list'][$cv['fuelNo']] = $oilPriceTemp;
                $insertStationData['oil_price_list'][$cv['fuelNo']] = $insertOilPriceTemp;
                $skuKey = "{$insertOilPriceTemp['oil_no']}{$insertOilPriceTemp['oil_type']}{$insertOilPriceTemp['oil_level']}";
                $insertStationData['extends'][$skuKey] = $cv;
            }
            if (empty($stationData['price_list'])) {
                $cacheStation = json_decode(
                                    app('redis')->hget(
                                        BasicJob::OIL_STATION_CACHE_CHECK . '_' . $this->name_abbreviation,
                                        $this->data['stationId']
                                    ),
                                    true
                                ) ?? [];
                if (($cacheStation and $cacheStation['is_stop'] != 1) or !$cacheStation) {
                    FOSS_STATIONRequest::handle("v1/station/batchStop", [
                        "id_list" => [
                            [
                                'app_station_id' => $this->data['stationId'],
                                'pcode'          => $this->data['auth_data']['role_code'],
                            ]
                        ],
                        "app_key" => $this->data['auth_data']['access_key'],
                    ]);
                    StationPriceData::updateByWhere([
                        [
                            'field'    => "station_id",
                            'operator' => "=",
                            'value'    => $this->data['stationId'],
                        ],
                        [
                            'field'    => "platform_code",
                            'operator' => "=",
                            'value'    => $this->data['auth_data']['role_code'],
                        ],
                    ], [
                        'enabled_state' => 2,
                    ]);
                    if ($cacheStation) {
                        $cacheStation['is_stop'] = 1;
                        app('redis')->hset(
                            BasicJob::OIL_STATION_CACHE_CHECK . '_' . $this->name_abbreviation,
                            $this->data['stationId'],
                            json_encode($cacheStation)
                        );
                    }
                }
                return responseFormatForSqzl(4120135);
            }
            $stationData['price_list'] = array_values($stationData['price_list']);
            $insertStationData['oil_price_list'] = array_values(
                $insertStationData['oil_price_list']
            );
            $insertStationData['extends'] = json_encode($insertStationData['extends']);
            StationPriceData::create($insertStationData);
            $this->pushStationToStationCenter($stationData, $this->data['stationId']);
        } catch (Throwable $exception) {
            if (config("error.{$exception->getCode()}", "") != "") {
                return responseFormatForSqzl(
                    $exception->getCode() ?? 5000001,
                    null,
                    false,
                    $exception->getMessage()
                );
            }
            Log::handle([
                'exception' => $exception,
            ]);
            return responseFormatForSqzl(5000001);
        }
        return responseFormatForSqzl();
    }
}
