<?php


namespace App\Models\Logic\Station\Receive;


use App\Jobs\BasicJob;
use App\Models\Data\StationPrice as StationPriceData;
use Illuminate\Http\JsonResponse;
use Throwable;


class ZHYK extends ThirdParty
{

    /**
     * @return JsonResponse|true
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/11/28 3:45 下午
     */
    public function handle()
    {
        //改写省市编码,并验证省市编码正确性
        $this->data['province_code'] = $this->data['provinceCode'] . '000000';
        $this->data['city_code'] = $this->data['cityCode'] . '000000';
        $checkResult = $this->validateAreaCode($this->data['provinceCode']);

        if (!$checkResult) {
            responseFormat(4120126, [], true);
        }

        $checkResult = $this->validateAreaCode($this->data['city_code']);

        if (!$checkResult) {
            responseFormat(4120128, [], true);
        }

        $stationData = [
            'id'                => $this->data['stationCode'],
            'station_name'      => $this->data['stationName'],
            'province_code'     => $this->data['province_code'],
            'city_code'         => $this->data['city_code'],
            'lng'               => $this->data['longitude'],
            'lat'               => $this->data['latitude'],
            'address'           => $this->data['address'],
            'is_stop'           => 0,
            'trade_type'        => 3,
            'contact'           => $this->data['leaderName'],
            'contact_phone'     => $this->data['leaderPhone'] ?? '',
            'station_brand'     => config('brand.zhyk')[$this->data['brand']] ?? 8,
            'price_list'        => [],
            'station_type'      => 2,
            'station_oil_unit'  => 1,
            'allow_switch_unit' => 0,
            'business_hours'    => "24小时",
        ];
        $oilStationCacheCheckKey = BasicJob::OIL_STATION_CACHE_CHECK . "_" . $this->name_abbreviation;
        $cacheOilTemp = json_decode(
            app('redis')->hget($oilStationCacheCheckKey, $this->data['stationCode']),
            true
        );
        if ($this->data['refuelMode'] == 3) {
            if (empty($cacheOilTemp)) {
                return true;
            }
            $stationData['trade_type'] = 1;
            $stationData['is_stop'] = 1;
        }
        if (checkIsMunicipality($stationData['city_code'])) {
            $stationData['province_code'] = $stationData['city_code'];
        }

        $insertStationPriceData = $stationData;
        $insertStationPriceData['oil_price_list'] = [];
        //油站价格数据
        $stationOilPrice = [];
        $oilGunNos = [];
        foreach ($this->data['gunList'] as $cv) {
            if (!isset($stationOilPrice[$cv['plateOilCode']])) {
                $stationOilPrice[$cv['plateOilCode']] = [];
                $oilGunNos[$cv['plateOilCode']] = [];
            }
            array_push($oilGunNos[$cv['plateOilCode']], ...explode(',', $cv['gunNo']));
            $stationOilPrice[$cv['plateOilCode']][] = $cv['oilPrice'] . '_' . $cv['discountOilPrice'];
            $stationOilPrice[$cv['plateOilCode']] = array_unique($stationOilPrice[$cv['plateOilCode']]);
        }
        foreach ($this->data['gunList'] as $cv) {
            if (isset($stationOilPrice[$cv['plateOilCode']]) and count(
                                                                     $stationOilPrice[$cv['plateOilCode']]
                                                                 ) > 1) {
                if (empty($cacheOilTemp)) {
                    continue;
                }
                $stationData['is_stop'] = 1;
            }
            $realOilInfo = explode("_", config("oil.oil_mapping.zhyk.{$cv['plateOilCode']}"));
            $oilPriceTemp = [];
            $insertOilPriceTemp = [];
            $oilPriceTemp['oil_type'] = $realOilInfo[1] ?? "";
            $oilPriceTemp['oil_level'] = $realOilInfo[2] ?? "";
            $oilPriceTemp['oil_name'] = $realOilInfo[0] ?? "";
            if (empty($oilPriceTemp['oil_name'])) {
                continue;
            }
            $insertOilPriceTemp['oil_type'] = array_flip(
                                                  config("oil.oil_type")
                                              )[$oilPriceTemp['oil_name']] ?? '';
            $insertOilPriceTemp['oil_no'] = str_replace(
                $insertOilPriceTemp['oil_type'],
                '',
                array_flip(
                    config("oil.oil_no")
                )[$oilPriceTemp['oil_type']] ?? ''
            );
            $insertOilPriceTemp['oil_level'] = array_flip(
                                                   config("oil.oil_level")
                                               )[$oilPriceTemp['oil_level']] ?? '';

            $oilPriceTemp['price'] = bcdiv($cv['discountOilPrice'], 100, 2);
            $oilPriceTemp['mac_price'] = bcdiv($cv['oilPrice'], 100, 2);
            $insertOilPriceTemp['gun_no'] = implode(',', $oilGunNos[$cv['plateOilCode']]);
            $insertOilPriceTemp['sale_price'] = $cv['discountOilPrice'];
            $insertOilPriceTemp['listing_price'] = $cv['oilPrice'];
            $insertOilPriceTemp['issue_price'] = $cv['NDRCOilPrice'];
            $stationData['price_list'][$cv['plateOilCode']] = $oilPriceTemp;
            $insertStationPriceData['oil_price_list'][$cv['plateOilCode']] = $insertOilPriceTemp;
        }
        if (empty($stationData['price_list'])) {
            $stationData['is_stop'] = 1;
        }
        $insertStationPriceData['station_id'] = $this->data['stationCode'];
        $insertStationPriceData['enabled_state'] = self::STATION_ENABLE_STATE_ENUM[$insertStationPriceData['is_stop']];
        $insertStationPriceData['platform_code'] = $this->data['auth_data']['role_code'];
        $stationData['price_list'] = array_values($stationData['price_list']);
        $insertStationPriceData['oil_price_list'] = array_values($insertStationPriceData['oil_price_list']);
        unset($insertStationPriceData['price_list']);
        $stationData['clearGunCache'] = true;
        $stationData['gunCacheKeyPrefix'] = "gun_number_";
        StationPriceData::create($insertStationPriceData);
        $this->pushStationToStationCenter($stationData, $this->data['stationCode']);
        return responseFormatForZhyk();
    }
}
