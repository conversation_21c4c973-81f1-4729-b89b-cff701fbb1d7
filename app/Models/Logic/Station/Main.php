<?php


namespace App\Models\Logic\Station;


use App\Jobs\PushOilDataBasicJob;
use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Common;
use App\Models\Data\Log\ResponseLog;
use App\Models\Data\OilMapping\Common as CommonData;
use App\Models\Data\RegionalInfo as RegionalInfoData;
use App\Models\Data\StationPushCondition as StationPushConditionData;
use App\Models\Data\SupplierInfo as SupplierInfoData;
use App\Models\Logic\Base;
use App\Models\Logic\Data\Push\OilStationData as PushOilOilStationDataLogic;
use Illuminate\Http\JsonResponse;
use Request\FOSS_STATION as FOSS_STATIONRequest;
use Symfony\Component\HttpFoundation\Response;
use Throwable;


class Main extends Base
{
    private $responseHandleMapping = [];

    private $stationQueryOneIdField = [
        'ad' => 'oilStationNo',
    ];

    public function __construct(array $parameter)
    {
        parent::__construct($parameter);
        $this->responseHandleMapping[''] = function () {
            return responseFormat(...func_get_args());
        };
        $this->responseHandleMapping['ygy'] = function () {
            return responseFormatForYgy(...func_get_args());
        };
        $this->responseHandleMapping['ad'] = function () {
            $funcArgs = func_get_args();
            if (isset($funcArgs[1])) {
                $funcArgs[1] = [
                    'stationList' => checkIsAssocArray($funcArgs[1]) ? [$funcArgs[1]] : $funcArgs[1]
                ];
            }
            return responseFormatForAd(...$funcArgs);
        };
    }

    public function handle()
    {
    }

    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/6/5 4:41 下午
     */
    public function queryOne(): JsonResponse
    {
        $idField = 'station_id';
        if (isset($this->stationQueryOneIdField[$this->name_abbreviation])) {
            $idField = 'oilStationNo';
        }
        $responseData = FOSS_STATIONRequest::handle("v1/station/getStationById", [
            'id' => $this->data[$idField],
        ]);
        if (!isset($responseData['data']['stationInfo'])) {
            return $this->getResponseHandle()(5000001);
        }

        $oilData = $responseData['data']['stationInfo'];
        $oilData['push_target_code'] = AuthInfoData::getAuthInfoFieldByNameAbbreviation(
            $this->name_abbreviation
        )['role_code'];
        $oilData['push_stop'] = $oilData['isstop'];
        CommonData::getOilStationData($oilData);
        CommonData::getSpecialPrice($oilData);
        if (isset($oilData["card_classify"])) {
            if ((int)$oilData["card_classify"] != 2) {
                $oilData["push_stop"] = 1;
            }
        }
        $nameAbbreviation = $this->name_abbreviation;
        $forceDataPrepareBySimple = json_decode(
                                        AuthConfigData::getAuthConfigValByName(
                                            "FORCE_DATA_PREPARE_FOR_STATION_QUERY"
                                        ),
                                        true
                                    ) ?? [];
        $dataPrepareClassConfig = isset($forceDataPrepareBySimple[$this->name_abbreviation]) ?
            [PushOilDataBasicJob::$dataPrepareClassMapping[$forceDataPrepareBySimple[$this->name_abbreviation]]] : [];
        if (empty($dataPrepareClassConfig)) {
            $dataPrepareClassConfig = PushOilDataBasicJob::checkWorkerExists($this->name_abbreviation);
            if (empty($dataPrepareClassConfig)) {
                $dataPrepareClassConfig = PushOilDataBasicJob::checkWorkerExists($this->data['auth_data']['role_code']);
                if (!empty($dataPrepareClassConfig)) {
                    $nameAbbreviation = array_keys($dataPrepareClassConfig)[0];
                }
            }
        }
        if (empty($dataPrepareClassConfig)) {
            return $this->getResponseHandle()(4030004);
        }
        $targetedPushConfig = json_decode(
                                  AuthConfigData::getAuthConfigValByName(
                                      "TARGETED_PUSH_CONFIG"
                                  ),
                                  true
                              ) ?? [];
        $forcePushDisableBySupplierCodeConfig = json_decode(
                                                    AuthConfigData::getAuthConfigValByName(
                                                        "STATION_FORCE_PUSH_STOP_BY_SUPPLIER_CODE"
                                                    ),
                                                    true
                                                ) ?? [];
        $forcePushDisableByStationIdConfig = json_decode(
                                                 AuthConfigData::getAuthConfigValByName(
                                                     "STATION_FORCE_PUSH_STOP_BY_STATION_ID"
                                                 ),
                                                 true
                                             ) ?? [];
        $stationWhiteListConfig = json_decode(
                                      AuthConfigData::getAuthConfigValByName(
                                          "STATION_WHITE_LIST_FOR_DOWNSTREAM"
                                      ),
                                      true
                                  ) ?? [];
        $forcePushStop = false;
        if (isset($stationWhiteListConfig[$nameAbbreviation]) and !in_array(
                $oilData['id'],
                $stationWhiteListConfig[$nameAbbreviation]
            )) {
            $forcePushStop = true;
            $oilData["push_stop"] = 1;
        }
        if (isset($oilData['pcode']) and in_array($oilData['pcode'], $forcePushDisableBySupplierCodeConfig)) {
            $forcePushStop = true;
            $oilData["push_stop"] = 1;
        }
        if (isset($forcePushDisableByStationIdConfig[$oilData['id']]) and in_array(
                $nameAbbreviation,
                $forcePushDisableByStationIdConfig[$oilData['id']]
            )) {
            $forcePushStop = true;
            $oilData["push_stop"] = 1;
        }
        if (!$forcePushStop and isset($targetedPushConfig[$oilData["id"]]) and is_array(
                $targetedPushConfig[$oilData["id"]]
            )) {
            if (!in_array($nameAbbreviation, $targetedPushConfig[$oilData["id"]])) {
                $oilData["push_stop"] = 1;
            }
        }
        $pushRule = StationPushConditionData::getPushRuleByNameAbbreviation($nameAbbreviation);
        if (!empty($pushRule)) {
            $pushRule = json_decode($pushRule[0], true);
            $pushRule = checkIsAssocArray($pushRule) ? [$pushRule] : $pushRule;
            foreach ($pushRule as $value) {
                switch ($value['type']) {
                    case 1:

                        if (!empty($value['val']) and !in_array($oilData['pcode'], $value['val'])) {
                            $oilData['push_stop'] = 1;
                        }
                        break;
                    case 2:

                        if ($oilData['trade_type'] == 5) {
                            $repeatTradeType = array_intersect(
                                $value['val'],
                                array_keys(
                                    Common::TRADE_TYPE_ENUM
                                )
                            );
                            if (empty($value['val'])) {
                                $oilData['trade_type'] = 1;
                                break;
                            }
                            $oilData['trade_type'] = $repeatTradeType[0];
                            if (count($repeatTradeType) > 1) {
                                $oilData['trade_type'] = 1;
                            }
                        }
                        if (!empty($value['val']) and !in_array($oilData['trade_type'], $value['val'])) {
                            $oilData['push_stop'] = 1;
                        }
                        break;
                    case 3:

                        if (!empty($value['val']) and !in_array($oilData['oil_unit'], $value['val'])) {
                            $oilData['push_stop'] = 1;
                        }
                        break;
                }
            }
        }
        $dataPrepareClass = array_values($dataPrepareClassConfig)[0];
        if (method_exists($dataPrepareClass, 'getOilStationData')) {
            $dataPrepareClass::getOilStationData($oilData);
        }
        CommonData::filterCommonField($oilData);
        return $this->getResponseHandle()(0, $oilData);
    }

    /**
     * @return Response
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/6/5 3:28 下午
     */
    public function queryAll(): Response
    {
        $queryLimit = json_decode(
            AuthConfigData::getAuthConfigValByName("QUERY_ALL_STATION_NO_LIMIT"),
            true
        );
        $redis = app('redis');
        if (!isset($queryLimit[$this->name_abbreviation])) {
            $queryNum = $redis->incr($this->name_abbreviation . '_' . date("Y-m-d"));
            if ($queryNum >= AuthConfigData::getAuthConfigValByName("QUERY_ALL_STATION_LIMIT_FREQUENCY")) {
                return $this->getResponseHandle()(4030005);
            }
            $queryAllStationLimitSecond = AuthConfigData::getAuthConfigValByName(
                "QUERY_ALL_STATION_LIMIT_SECOND"
            );
            if (time() - strtotime($redis->get("{$this->name_abbreviation}_last_query_all_station")) <
                $queryAllStationLimitSecond) {
                return $this->getResponseHandle()(4030005);
            }
            $redis->setex(
                "{$this->name_abbreviation}_last_query_all_station",
                $queryAllStationLimitSecond,
                time()
            );
            $redis->expire($this->name_abbreviation . '_' . date("Y-m-d"), 86400);
        }
        $nameAbbreviation = $this->name_abbreviation;
        $dataPrepareClassConfig = PushOilDataBasicJob::checkWorkerExists($this->name_abbreviation);
        if (empty($dataPrepareClassConfig)) {
            $dataPrepareClassConfig = PushOilDataBasicJob::checkWorkerExists($this->data['auth_data']['role_code']);
            if (!empty($dataPrepareClassConfig)) {
                $nameAbbreviation = array_keys($dataPrepareClassConfig)[0];
            }
        }
        if (empty($dataPrepareClassConfig)) {
            return $this->getResponseHandle()(4030004);
        }
        $dataPrepareClass = array_values($dataPrepareClassConfig)[0];
        if (property_exists($dataPrepareClass, 'dbActionInner')) {
            $dataPrepareClass::$dbActionInner = false;
        }
        $startTime = microtime(true);
        $hashFields = $redis->hkeys(PushOilOilStationDataLogic::$oilStationCacheForHubKey);
        if (count($hashFields) > 0) {
            $stationData = $redis->hmget(PushOilOilStationDataLogic::$oilStationCacheForHubKey, $hashFields);
            $cost = intval(1000 * (microtime(true) - $startTime));
            $startTime1 = microtime(true);
            $pushRuleData = StationPushConditionData::getPushRuleByNameAbbreviation($nameAbbreviation);
        } else {
            return $this->getResponseHandle()(0, []);
        }
        set_time_limit(300);
        $targetedPushConfig = json_decode(
                                  AuthConfigData::getAuthConfigValByName(
                                      "TARGETED_PUSH_CONFIG"
                                  ),
                                  true
                              ) ?? [];
        $forcePushDisableBySupplierCodeConfig = json_decode(
                                                    AuthConfigData::getAuthConfigValByName(
                                                        "STATION_FORCE_PUSH_STOP_BY_SUPPLIER_CODE"
                                                    ),
                                                    true
                                                ) ?? [];
        $forcePushDisableByStationIdConfig = json_decode(
                                                 AuthConfigData::getAuthConfigValByName(
                                                     "STATION_FORCE_PUSH_STOP_BY_STATION_ID"
                                                 ),
                                                 true
                                             ) ?? [];
        $stationWhiteListConfig = json_decode(
                                      AuthConfigData::getAuthConfigValByName(
                                          "STATION_WHITE_LIST_FOR_DOWNSTREAM"
                                      ),
                                      true
                                  ) ?? [];
        foreach ($stationData as $k => &$v) {
            $v = json_decode($v, true);
            if (!$v) {
                unset($stationData[$k]);
                continue;
            }
            if (!isset($v['id'], $v['pcode'], $v['trade_type'], $v['oil_unit'])) {
                unset($stationData[$k]);
                continue;
            }
            if (!empty($pushRuleData)) {
                $pushRule = json_decode($pushRuleData[0], true);
                $pushRule = checkIsAssocArray($pushRule) ? [$pushRule] : $pushRule;
                foreach ($pushRule as $value) {
                    switch ($value['type']) {
                        case 1:

                            if (!empty($value['val']) and !in_array($v['pcode'], $value['val'])) {
                                unset($stationData[$k]);
                                continue 3;
                            }
                            break;
                        case 2:

                            if ($v['trade_type'] == 5) {
                                $repeatTradeType = array_intersect(
                                    $value['val'],
                                    array_keys(
                                        Common::TRADE_TYPE_ENUM
                                    )
                                );
                                if (empty($value['val'])) {
                                    $v['trade_type'] = 1;
                                    break;
                                }
                                $v['trade_type'] = $repeatTradeType[0];
                                if (count($repeatTradeType) > 1) {
                                    $v['trade_type'] = 1;
                                }
                            }
                            if (!empty($value['val']) and !in_array($v['trade_type'], $value['val'])) {
                                unset($stationData[$k]);
                                continue 3;
                            }
                            break;
                        case 3:

                            if (!empty($value['val']) and !in_array($v['oil_unit'], $value['val'])) {
                                unset($stationData[$k]);
                                continue 3;
                            }
                            break;
                    }
                }
            }
            if (isset($stationWhiteListConfig[$nameAbbreviation]) and !in_array(
                    $v['id'],
                    $stationWhiteListConfig[$nameAbbreviation]
                )) {
                unset($stationData[$k]);
                continue;
            }
            if (in_array($v['pcode'], $forcePushDisableBySupplierCodeConfig)) {
                unset($stationData[$k]);
                continue;
            }
            if (isset($forcePushDisableByStationIdConfig[$v['id']]) and in_array(
                    $nameAbbreviation,
                    $forcePushDisableByStationIdConfig[$v['id']]
                )) {
                unset($stationData[$k]);
                continue;
            }
            if (isset($targetedPushConfig[$v["id"]]) and is_array($targetedPushConfig[$v["id"]]) and
                                                         !in_array($nameAbbreviation, $targetedPushConfig[$v["id"]])) {
                unset($stationData[$k]);
            }
        }
        $supplierData = convertListToDict(
            SupplierInfoData::getSupplierNameBySupplierCode(
                array_column(
                    $stationData,
                    'pcode'
                )
            ),
            'supplier_code'
        );
        CommonData::$dbActionInner = false;
        foreach ($stationData as &$sv) {
            $sv['real_province_code'] = "{$sv['provice_code']}000000";
            $sv['real_city_code'] = "{$sv['city_code']}000000";
        }
        $regionalMapping = RegionalInfoData::getNameByCode(
            array_merge(
                array_column(
                    $stationData,
                    'real_province_code'
                ),
                array_column($stationData, 'real_city_code')
            )
        );
        foreach ($stationData as &$v) {
            $v['supplier_name'] = $supplierData[$v['pcode']]['supplier_name'] ?? "";
            CommonData::getOilStationData($v);
            $v['push_target_code'] = $this->data['auth_data']['role_code'];
            CommonData::getSpecialPrice($v);
            $v['province_name'] = $regionalMapping[$v['real_province_code']] ?? '';
            $v['city_name'] = $regionalMapping[$v['real_city_code']] ?? '';
            unset($v['real_province_code'], $v['real_city_code']);
            if (!isset($v['push_stop'])) {
                $v['push_stop'] = 1;
                continue;
            }
            if (method_exists($dataPrepareClass, 'getOilStationData')) {
                $dataPrepareClass::getOilStationData($v);
            }
            CommonData::filterCommonField($v);
        }

        $cost1 = intval(1000 * (microtime(true) - $startTime1));
        ResponseLog::handle([
            'getStationCostTime'     => $cost,
            'prepareStationCostTime' => $cost1
        ]);
        return $this->getResponseHandle()(0, array_values($stationData));
    }

    public function getSelect()
    {
        $redisConn = app("redis");
        $cacheStationIds = $redisConn->hkeys(PushOilOilStationDataLogic::$oilStationCacheForHubKey);
        $stationData = $redisConn->hmget(
            PushOilOilStationDataLogic::$oilStationCacheForHubKey,
            $cacheStationIds
        );
        foreach ($stationData as $k => &$v) {
            $v = json_decode($v, true);
            if (empty($v)) {
                unset($stationData[$k]);
                continue;
            }
            $v = [
                'station_id'   => $v['id'],
                'station_name' => $v['station_name'],
            ];
        }
        return $stationData;
    }

    private function getResponseHandle()
    {
        if (!isset($this->responseHandleMapping[$this->name_abbreviation])) {
            return $this->responseHandleMapping[''];
        }
        return $this->responseHandleMapping[$this->name_abbreviation];
    }

    public static function getAvailableStationListForCustomer(string $orgCode): JsonResponse
    {
        $authInfoData = AuthInfoData::getAuthInfoByRoleCode($orgCode, true);
        $hashFields = app('redis')->hkeys(PushOilOilStationDataLogic::$oilStationCacheForHubKey);
        if (count($hashFields) > 0) {
            $stationData = app('redis')->hmget(PushOilOilStationDataLogic::$oilStationCacheForHubKey, $hashFields);
            $pushRuleData = StationPushConditionData::getPushRuleByNameAbbreviation($authInfoData['name_abbreviation']);
        } else {
            return responseFormat(0, []);
        }
        $targetedPushConfig = json_decode(
                                  AuthConfigData::getAuthConfigValByName(
                                      "TARGETED_PUSH_CONFIG"
                                  ),
                                  true
                              ) ?? [];
        $forcePushDisableBySupplierCodeConfig = json_decode(
                                                    AuthConfigData::getAuthConfigValByName(
                                                        "STATION_FORCE_PUSH_STOP_BY_SUPPLIER_CODE"
                                                    ),
                                                    true
                                                ) ?? [];
        $forcePushDisableByStationIdConfig = json_decode(
                                                 AuthConfigData::getAuthConfigValByName(
                                                     "STATION_FORCE_PUSH_STOP_BY_STATION_ID"
                                                 ),
                                                 true
                                             ) ?? [];
        $stationWhiteListConfig = json_decode(
                                      AuthConfigData::getAuthConfigValByName(
                                          "STATION_WHITE_LIST_FOR_DOWNSTREAM"
                                      ),
                                      true
                                  ) ?? [];
        foreach ($stationData as $k => &$v) {
            $v = json_decode($v, true);
            if (!$v) {
                unset($stationData[$k]);
            }
            if (!isset($v['id'], $v['pcode'], $v['trade_type'], $v['oil_unit'])) {
                unset($stationData[$k]);
                continue;
            }
            if (!empty($pushRuleData)) {
                $pushRule = json_decode($pushRuleData[0], true);
                $pushRule = checkIsAssocArray($pushRule) ? [$pushRule] : $pushRule;
                foreach ($pushRule as $value) {
                    if (empty($value['val'])) {
                        continue;
                    }
                    switch ($value['type']) {
                        case 1:

                            if (!in_array($v['pcode'], $value['val'])) {
                                unset($stationData[$k]);
                                continue 2;
                            }
                            break;
                        case 2:

                            if ($v['trade_type'] == 5) {
                                $repeatTradeType = array_intersect(
                                    $value['val'],
                                    array_keys(
                                        Common::TRADE_TYPE_ENUM
                                    )
                                );
                                $v['trade_type'] = $repeatTradeType[0];
                                if (count($repeatTradeType) > 1) {
                                    $v['trade_type'] = 1;
                                }
                            }
                            if (!in_array($v['trade_type'], $value['val'])) {
                                unset($stationData[$k]);
                                continue 2;
                            }
                            break;
                        case 3:

                            if (!in_array($v['oil_unit'], $value['val'])) {
                                unset($stationData[$k]);
                                continue 2;
                            }
                            break;
                    }
                }
            }
            if (isset($stationWhiteListConfig[$authInfoData['name_abbreviation']]) and !in_array(
                    $v['id'],
                    $stationWhiteListConfig[$authInfoData['name_abbreviation']]
                )) {
                unset($stationData[$k]);
                continue;
            }
            if (in_array($v['pcode'], $forcePushDisableBySupplierCodeConfig)) {
                unset($stationData[$k]);
                continue;
            }
            if (isset($forcePushDisableByStationIdConfig[$v['id']]) and in_array(
                    $authInfoData['name_abbreviation'],
                    $forcePushDisableByStationIdConfig[$v['id']]
                )) {
                unset($stationData[$k]);
                continue;
            }
            if (isset($targetedPushConfig[$v["id"]]) and is_array($targetedPushConfig[$v["id"]]) and
                                                         !in_array(
                                                             $authInfoData['name_abbreviation'],
                                                             $targetedPushConfig[$v["id"]]
                                                         )) {
                unset($stationData[$k]);
            }
        }
        return responseFormat(0, array_column($stationData, 'id'));
    }
}
