<?php


namespace App\Models\Logic\Callback;


use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Logic\Base;
use App\Models\Logic\Trade\Refund;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Throwable;


class Hyt extends Base
{
    /**
     * @return JsonResponse|void
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 16:26
     */
    public function handle()
    {
        // validate parameters and processing for each event
        if ($this->data['type'] == "GAS_OIL_REFUND") {
            $validator = Validator::make($this->data['content'], [
                'orderNo' => 'required'
            ], [
                'orderNo.required' => 4120451,
            ]);
            if ($validator->fails()) {

                return responseFormat($validator->errors()->first());
            }
            $this->data['content']['auth_data'] = AuthInfoData::getAuthInfoFieldByNameAbbreviation(
                "hyt");
            (new Refund($this->data['content']))->handle();
        }
    }
}
