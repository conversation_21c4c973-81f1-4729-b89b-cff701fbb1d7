<?php


namespace App\Models\Logic\Callback;


use App\Models\Logic\Base;
use App\Models\Logic\Trade\Pay\Main as Pay;
use Symfony\Component\HttpFoundation\Response;
use Throwable;


class JHCX extends Base
{
    /**
     * @return Response|void
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019-07-12 16:26
     */
    public function handle()
    {
        switch ($this->data['type']) {
            case "CONSUMED":

                return (new Pay($this->data))->handle();
            case "RECHARGE":
                return responseFormatForJHCX();
        }
    }
}
