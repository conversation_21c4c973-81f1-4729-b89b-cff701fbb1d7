<?php


namespace App\Models\Logic\Callback;


use App\Jobs\QueryOrderToThirdParty;
use App\Models\Data\Log\ResponseLog;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Base;
use App\Models\Logic\Code\Parse;
use App\Models\Logic\Data\Push\ToBePaid;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Throwable;

class Bdt extends Base
{
    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 16:26
     */
    public function handle()
    {
        if ($this->data['billStatus'] < 0) {

            responseFormat(4120056, [], true);
        }

        //转换二维码字段以及站点ID
        $this->data['qr_code'] = $this->data['qrCode'];
        $this->data['station_id'] = $this->data['shopID'];

        try {

            $this->data['gasStationId'] = $this->getGasStationIdByAppStationIdAndPCode($this->data['station_id']);
        } catch (Throwable $exception) {

            ResponseLog::handle([
                'data'      => $this->data,
                'exception' => $exception,
            ]);
        }

        $cardData = (new Parse($this->data))->handle(true);
        if ((isset($cardData['availableAmount']) and bccomp($cardData['availableAmount'], 0.00, 2) === 1)
            and bccomp($cardData['availableAmount'], $this->data["billPrice"], 2) === -1) {

            responseFormat(5000009, [], true);
        }
        if (!$cardData['is_check_pwd']) {

            $orderAssocModel = OrderAssocData::insert(1, 1, '',
                $this->data['dealNum'], $this->name_abbreviation, '', '');
        } else {

            $orderAssocModel = OrderAssocData::insert(2, 2, '',
                $this->data['dealNum'], $this->name_abbreviation, '', '');
        }

        $orderData = FOSS_ORDERRequest::handle('/api/oil_adapter/makeOrder', [
            'card_no'        => $cardData['card_no'],
            'app_station_id' => $this->data['shopID'],
            'oil_num'        => $this->data['goodNum'],
            'oil_price'      => $this->data['goodPrice'],
            'oil_money'      => $this->data['billPrice'],
            'pay_money'      => $this->data['billPrice'],
            'oil_type'       => '',
            'oil_name'       => config('oil.oil_type.液化天然气'),
            'oil_level'      => '',
            'third_order_id' => $this->data['dealNum'],
            'order_no'       => $cardData['order_no'],
            'pcode'          => $this->data['auth_data']['role_code'],
            'driver_source'  => $cardData['is_self'] ? 1 : 2,
            'client_type'    => $cardData['qr_code_source'],
            'driver_phone'   => $cardData['driver_phone'],
            'driver_name'    => $cardData['driver_name'],
            'truck_no'       => $cardData['truck_no'],
            'trade_mode'     => $cardData['is_self'] ? 25 : 40,
        ]);
        if (!array_has($orderData, ["code", "msg", "data"])) {

            responseFormat(5000001, [], true);
        }

        if ($orderData['code'] != 0 or empty($orderData['data'])) {

            responseFormat(5009999, [], true, $orderData['msg'] ?? '');
        }

        $orderAssocModel->self_order_status = 2;
        $orderAssocModel->platform_order_status = 2;
        $orderAssocModel->self_order_id = $orderData['data']['order_id'];
        $orderAssocModel->reason = $orderData['msg'] ?? '';
        $orderAssocModel->save();
        $this->data['gas_oil_type'] = "";
        $this->data['gas_oil_name'] = '液化天然气';
        $this->data['gas_oil_level'] = '';
        $this->data['id'] = $orderData['data']['order_id'];
        $this->data['extends'] = $cardData['extends'];
        $this->data['orderData'] = $orderData['data'];
        $this->data['money'] = $this->data['billPrice'];
        $this->data['order_id'] = $this->data['dealNum'];
        $this->data['access_key'] = $cardData['access_key'] ?? '';
        $this->data['secret'] = $cardData['secret'] ?? '';
        $this->data['name_abbreviation'] = $cardData['name_abbreviation'] ?? '';
        $orderAssocCacheKey = "BDT_ORDER_{$orderData['data']['order_id']}";
        app('redis')->setex($orderAssocCacheKey, 3600, json_encode([
            'billTime'  => $this->data['billTime'],
            'dealNum'   => $this->data['dealNum'],
            'billPrice' => $this->data['billPrice'],
        ]));
        if (isset($cardData['is_self']) and !$cardData['is_self']) {

            (new ToBePaid($this->data))->handle();
        }

        dispatch((new QueryOrderToThirdParty([
            'order_id'          => $this->data['dealNum'],
            'billCode'          => $orderData['data']['order_id'],
            'billPrice'         => $this->data['billPrice'],
            'access_key'        => $this->data['auth_data']['access_key'],
            'secret'            => $this->data['auth_data']['secret'],
            'name_abbreviation' => 'bdt'
        ]))->delay(Carbon::now()->addSecond(175)));
        return responseFormat(0, [], true);
    }
}
