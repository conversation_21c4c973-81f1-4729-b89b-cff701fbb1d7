<?php


namespace App\Models\Logic\Trade\Refund;

use App\Models\Data\OrderAssoc as OrderAssocData;
use Illuminate\Http\JsonResponse;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Throwable;

class ZHYK extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/10/11 2:41 下午
     */
    public function handle(): JsonResponse
    {
        $orderInfo = OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'self_order_id',
                'operator' => '=',
                'value'    => $this->data['outTradeNo'],
            ],
            [
                'field'    => 'platform_name',
                'operator' => '=',
                'value'    => $this->name_abbreviation,
            ],
        ], ["*"], true, true);
        if ($orderInfo) {
            try {
                FOSS_ORDERRequest::handle('/api/oil_adapter/oaRefund', [
                    'order_id' => $orderInfo->self_order_id,
                ]);
                $orderInfo->platform_order_status = 3;
                $orderInfo->self_order_status = 3;
                $orderInfo->save();
                $this->refundAlarm($orderInfo);
            } catch (Throwable $throwable) {
                return responseFormatForZhyk($throwable->getCode(), null, false, $throwable->getMessage());
            }
            return responseFormatForZhyk();
        }
        return responseFormatForZhyk(5000018);
    }
}
