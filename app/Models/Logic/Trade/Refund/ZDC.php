<?php


namespace App\Models\Logic\Trade\Refund;

use App\Models\Data\Log\ResponseLog;
use App\Models\Data\OrderAssoc as OrderAssocData;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Throwable;

class ZDC extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/10/11 2:41 下午
     */
    public function handle()
    {
        $responseFormatForQp = !empty($this->data['qp_callback']);
        try {
            $orderInfo = OrderAssocData::getOrderInfoByWhere([
                [
                    'field'    => 'platform_order_id',
                    'operator' => '=',
                    'value'    => $this->data['orderId'],
                ],
                [
                    'field' => 'self_order_status',
                    'operator' => '=',
                    'value'    => 1,
                ],
                [
                    'field'    => 'platform_name',
                    'operator' => '=',
                    'value'    => $this->name_abbreviation,
                ],
            ], ['*'], true, true);
            if (!isset($orderInfo->self_order_id)) {
                return responseFormatForZdc(
                    '40501',
                    $responseFormatForQp ? [
                        'status' => 0
                    ] : [],
                    '查询订单失败',
                    false,
                    (int)$responseFormatForQp
                );
            }
            if (isset($this->data['force_refund']) and $this->data['force_refund']) {
                FOSS_ORDERRequest::handle('/api/oil_adapter/oaRefund', [
                    'order_id' => $orderInfo->self_order_id,
                ]);
                $orderInfo->platform_order_status = 3;
                $orderInfo->self_order_status = 3;
                $orderInfo->save();
                return responseFormatForZdc(
                    $responseFormatForQp ? '00000' : 200,
                    $responseFormatForQp ? [
                        'status' => 1
                    ] : [],
                    '成功',
                    false,
                    (int)$responseFormatForQp
                );
            }
            if (isset($this->data['not_refund']) and $this->data['not_refund']) {
                $orderInfo->platform_order_status = 2;
                $orderInfo->save();
                return responseFormatForZdc(
                    $responseFormatForQp ? '00000' : 200,
                    $responseFormatForQp ? [
                        'status' => 1
                    ] : [],
                    '成功',
                    false,
                    (int)$responseFormatForQp
                );
            }
            $extend = json_decode($orderInfo->extend, true);
            FOSS_ORDERRequest::handle('/api/oil_adapter/orderApproveResultCallback', [
                'id'              => $extend['order_approve']['id'],
                'approve_status'  => $this->data['state'] == 2 ? 2 : 1,
                'approval_reason' => $this->data['state'] == 2 ? $this->data['msg'] : '',
            ]);
            if ($this->data['state'] == 1) {
                $orderInfo->platform_order_status = 3;
                $orderInfo->self_order_status = 3;
                $orderInfo->save();
            }
            return responseFormatForZdc(
                $responseFormatForQp ? '00000' : 200,
                $responseFormatForQp ? [
                    'status' => 1
                ] : [],
                '成功',
                false,
                (int)$responseFormatForQp
            );
        } catch (Throwable $throwable) {
            ResponseLog::handle([
                'exception' => $throwable
            ]);
            return responseFormatForZdc(
                '50000',
                $responseFormatForQp ? [
                    'status' => 0
                ] : [],
                config("error.5000001"),
                false,
                (int)$responseFormatForQp
            );
        }
    }
}
