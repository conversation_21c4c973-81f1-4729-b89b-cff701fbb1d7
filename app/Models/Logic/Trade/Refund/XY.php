<?php


namespace App\Models\Logic\Trade\Refund;

use App\Models\Data\OrderAssoc as OrderAssocData;
use Exception;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Throwable;


class XY extends ThirdParty
{
    /**
     * @throws Exception|Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/11/5 4:20 下午
     */
    public function handle()
    {
        $orderInfo = OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'platform_order_id',
                'operator' => '=',
                'value'    => $this->data['order_id'],
            ],
            [
                'field'    => 'platform_order_status',
                'operator' => '=',
                'value'    => 1,
            ],
            [
                'field'    => 'platform_name',
                'operator' => '=',
                'value'    => $this->name_abbreviation,
            ],
        ], ['*'], true, true);
        if ($orderInfo) {
            FOSS_ORDERRequest::handle('/api/oil_adapter/oaRefund', [
                'order_id' => $orderInfo->self_order_id,
            ]);
            OrderAssocData::updateOrderInfoByOrderId($this->data['order_id'], [
                'platform_order_status' => 3,
                'self_order_status'     => 3,
            ], false);
            $this->refundAlarm($orderInfo);
            return responseFormat();
        }

        return responseFormat(5000018);
    }
}
