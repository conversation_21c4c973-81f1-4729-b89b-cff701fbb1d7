<?php


namespace App\Models\Logic\Trade\Refund;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Order\Refund as OrderRefund;
use Illuminate\Http\JsonResponse;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Throwable;

class SG extends ThirdParty
{
    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/1/2 3:23 下午
     */
    public function handle()
    {
        $upStreamOrderInfo = OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'platform_order_id',
                'operator' => '=',
                'value'    => $this->data['order_id'],
            ],
            [
                'field'    => 'platform_order_status',
                'operator' => '=',
                'value'    => 1,
            ],
            [
                'field'    => 'platform_name',
                'operator' => '=',
                'value'    => $this->name_abbreviation,
            ],
        ], [
            'self_order_id',
            'platform_order_id',
            'platform_name',
        ]);
        if ($upStreamOrderInfo) {

            responseFormat(5000005, [], true);
        }

        FOSS_ORDERRequest::handle('/api/oil_adapter/oaRefund', [
            'order_id' => $upStreamOrderInfo['self_order_id'],
        ]);
        //查询是否有下游订单需要退款
        $downStreamOrderInfo = OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'self_order_id',
                'operator' => '=',
                'value'    => $upStreamOrderInfo['self_order_id'],
            ],
            [
                'field'    => 'platform_name',
                'operator' => '!=',
                'value'    => $this->name_abbreviation,
            ],
        ], [
            'self_order_id',
            'platform_order_id',
            'platform_name',
        ]);
        if (!empty($downStreamOrderInfo)) {

            (new OrderRefund([
                'platform_order_id' => $downStreamOrderInfo['platform_order_id'],
                'platform_name'     => $downStreamOrderInfo['platform_name'],
                'skipSelf'          => true,
            ]))->doRefund();
        }
        return responseFormat(0, [[
                                      "out_order_no"   => $upStreamOrderInfo['self_order_id'],
                                      "out_partner_id" => AuthConfigData::getAuthConfigValByName("SG_PARTNER_ID")
                                  ]]);
    }
}
