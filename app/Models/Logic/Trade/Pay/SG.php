<?php


namespace App\Models\Logic\Trade\Pay;


use App\Models\Dao\DriverMapping as DriverMappingDao;
use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\ResponseLog;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Data\Trade\Order as OrderData;
use App\Models\Logic\Code\Parse;
use App\Models\Logic\Data\Push\ToBePaid;
use Illuminate\Http\JsonResponse;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Request\SG as SGRequest;
use Throwable;


class SG extends ThirdParty
{
    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-17 16:27
     */
    public function handle()
    {
        try {

            $this->data['gasStationId'] = $this->getGasStationIdByAppStationIdAndPCode($this->data['station_id']);
        } catch (Throwable $exception) {

            ResponseLog::handle([
                'data'      => $this->data,
                'exception' => $exception,
            ]);
        }

        $cardData = (new Parse($this->data))->handle(true);

        if ((isset($cardData['availableAmount']) and bccomp($cardData['availableAmount'], 0.00, 2) === 1) and
            bccomp($cardData['availableAmount'], $this->data["actual_amount"], 2) === -1) {

            responseFormat(5000009, [], true);
        }

        if (empty($cardData['driver_phone'])) {

            $cardData['driver_phone'] = '13000000000';
        }

        $phoneMappingData = (new DriverMappingDao())
            ->where("phone", "=", $cardData['driver_phone'])
            ->where("card_no", "=", $cardData['card_no'])
            ->get();
        $phoneMappingDataArray = $phoneMappingData->toArray();

        if (empty($phoneMappingDataArray)) {

            $data = SGRequest::handle("company.open.card", [
                'partner_id'  => AuthConfigData::getAuthConfigValByName("SG_PARTNER_ID"),
                'org_id'      => AuthConfigData::getAuthConfigValByName("SG_ORG_ID"),
                'out_user_id' => $cardData['card_no'],
                'mobile'      => $cardData['driver_phone'],
                'cust_name'   => '司机',
                'plate_no'    => $cardData['truck_no'] ?? '未知',
            ]);

            if (array_has($data, ['code', 'biz_content']) and $realData = json_decode($data['biz_content'], true) and
                $data['code'] == "000000") {

                $insertData = [
                    'phone'            => $cardData['driver_phone'],
                    'card_no'          => $cardData['card_no'],
                    'third_party_uniq' => $realData['xl_user_id'],
                ];
                (new DriverMappingDao())->updateOrCreate([
                    'phone'            => $cardData['driver_phone'],
                    'third_party_uniq' => $realData['xl_user_id'],
                    'platform_name' => $this->name_abbreviation,
                ], $insertData);
            } else {

                ResponseLog::handle([
                    'msg'  => 'Call sg open card failed',
                    'data' => $data,
                ]);
                responseFormat(5000004, [], true);
            }
        }

        $oilConverge = explode("_", config("oil.oil_mapping")[$this->name_abbreviation][$this->data['oil_converge']]);
        $orderData = FOSS_ORDERRequest::handle('/api/oil_adapter/makeOrder', [
            'card_no'        => $cardData['card_no'],
            'station_id'     => $this->data['station_id'],
            'oil_num'        => $this->data['oil_num'],
            'oil_price'      => $this->data['price'],
            'oil_money'      => $this->data['money'],
            'pay_money'      => $this->data['actual_amount'],
            'oil_type'       => config("oil.oil_no")[config("oil.oil_no_int")[$oilConverge[0]]],
            'oil_name'       => config("oil.oil_type")[config("oil.oil_type_int")[$oilConverge[1]]],
            'oil_level'      => config("oil.oil_level")[config("oil.oil_level_int")[0]],
            'third_order_id' => $this->data['order_id'],
            'order_no'       => $cardData['order_no'],
            'pcode'          => $this->data['auth_data']['role_code'],
            'driver_source'  => $cardData['is_self'] ? 1 : 2,
            'client_type'    => $cardData['qr_code_source'],
            'driver_phone'   => $cardData['driver_phone'],
            'driver_name'    => $cardData['driver_name'],
            'truck_no'       => $cardData['truck_no'],
            'trade_mode'     => $cardData['is_self'] ? 25 : 40,
        ]);
        OrderAssocData::insert(2, 2, ($orderData['data']['order_id'] ?? ''), $this->data['order_id'],
            $this->name_abbreviation, $orderData['msg'], '');
        $responseData = OrderData::handle($this->name_abbreviation, $orderData['data']);
        $this->data['gas_oil_type'] = config('oil.oil_no_simple')[config("oil.oil_no")[config("oil.oil_no_int")[$oilConverge[0]]]];
        $this->data['gas_oil_name'] = config("oil.oil_type_int")[$oilConverge[1]];
        $this->data['gas_oil_level'] = '';
        $this->data['id'] = $orderData['data']['order_id'];
        $this->data['extends'] = $cardData['extends'];
        $this->data['orderData'] = $orderData['data'];
        $this->data['money'] = $this->data['actual_amount'];
        $this->data['access_key'] = $cardData['access_key'] ?? '';
        $this->data['secret'] = $cardData['secret'] ?? '';
        $this->data['name_abbreviation'] = $cardData['secret'] ?? '';
        if (isset($cardData['is_self']) and !$cardData['is_self']) {

            (new ToBePaid($this->data))->handle();
        }
        if ($cardData['is_check_pwd']) {

            responseFormat(5000003, [$responseData], true);
        }

        return responseFormat(0, [$responseData]);
    }
}
