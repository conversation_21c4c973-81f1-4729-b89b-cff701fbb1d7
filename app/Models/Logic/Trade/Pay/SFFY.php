<?php


namespace App\Models\Logic\Trade\Pay;


use App\Jobs\PushOrderCancelForSFFY;
use App\Models\Dao\OrderAssoc as OrderAssocDao;
use App\Models\Data\OrderAssoc as OrderAssocData;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Queue;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Throwable;


class SFFY extends ThirdParty
{
    /**
     * @param bool $throwException
     * @return ?JsonResponse
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/9/8 3:01 下午
     */
    public function handle(bool $throwException = false): ?JsonResponse
    {
        $orderInfo = OrderAssocData::getOrderInfoBySelfOrderId($this->data['trade_id'], [
            "platform_name" => $this->name_abbreviation,
        ], ['*'], true);

        if (empty($orderInfo) or !$orderInfo instanceof OrderAssocDao or !$orderInfo->exists()) {
            if ($throwException) {
                throw new Exception(config("error.5000018"), 5000018);
            }
            return responseFormat(5000018);
        }

        $extend = json_decode($orderInfo->extend ?? '', true);
        switch ($this->data['pay_status']) {
            case 1:
                $this->payByNew($orderInfo);
                break;
            case 2:
                $this->cancelByNew($orderInfo);
                return responseFormat();
        }
        return responseFormat(0, Simple::getPayResult($this->data['trade_id'], $extend));
    }

    /**
     * @param OrderAssocDao $orderAssocModel
     * @return void
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/9/8 3:18 下午
     */
    public function payByNew(OrderAssocDao $orderAssocModel)
    {
        try {
            FOSS_ORDERRequest::handle("/api/oil_adapter/oaPay", [
                'order_id'       => $this->data['trade_id'],
                'third_order_id' => $this->data['order_id'],
                'code'           => $this->data['pay_status'] - 1,
                'msg'            => mb_substr($this->data['pay_reason'] ?? '', 0, 200),
                'remark'         => mb_substr($this->data['pay_reason'] ?? '', 0, 200),
            ]);
            $orderAssocModel->self_order_status = $this->data['pay_status'];
            $orderAssocModel->platform_order_status = $this->data['pay_status'] ?? '';
            $orderAssocModel->platform_order_id = $this->data['order_id'];
            if (!isset($this->data['updatePlatformReason']) or (isset($this->data['updatePlatformReason'])
                                                                and $this->data['updatePlatformReason'])) {
                $orderAssocModel->platform_reason = $this->data['pay_reason'] ?? '';
            }
            $orderAssocModel->save();
        } catch (Throwable $exception) {
            $orderAssocModel->platform_order_id = $this->data['order_id'];
            $orderAssocModel->reason = $exception->getMessage();
            if (!isset($this->data['updatePlatformReason']) or (isset($this->data['updatePlatformReason'])
                                                                and $this->data['updatePlatformReason'])) {
                $orderAssocModel->platform_reason = $this->data['pay_reason'] ?? '';
            }
            $orderAssocModel->save();
            Queue::push(new PushOrderCancelForSFFY($orderAssocModel->toArray()), "", "adapter_deal_trade");
            throw $exception;
        }
    }

    /**
     * @param OrderAssocDao $orderAssocModel
     * @return void
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/9/8 3:15 下午
     */
    public function cancelByNew(OrderAssocDao $orderAssocModel)
    {
        try {
            FOSS_ORDERRequest::handle("/api/oil_adapter/cancelOrder", [
                'order_id'       => $this->data['trade_id'],
                'pushMsgToDoper' => true,
                'pay_reason'     => '三方提示：' . mb_substr($this->data['pay_reason'] ?? '', 0, 32),
            ]);
            $orderAssocModel->self_order_status = 4;
            $orderAssocModel->platform_order_status = 4;
            $orderAssocModel->platform_order_id = $this->data['order_id'];
            if (!isset($this->data['updatePlatformReason']) or (isset($this->data['updatePlatformReason'])
                                                                and $this->data['updatePlatformReason'])) {
                $orderAssocModel->platform_reason = $this->data['pay_reason'] ?? '';
            }
            $orderAssocModel->save();
        } catch (Throwable $exception) {
            $orderAssocModel->platform_order_id = $this->data['order_id'];
            $orderAssocModel->reason = $exception->getMessage();
            if (!isset($this->data['updatePlatformReason']) or (isset($this->data['updatePlatformReason'])
                                                                and $this->data['updatePlatformReason'])) {
                $orderAssocModel->platform_reason = $this->data['pay_reason'] ?? '';
            }
            $orderAssocModel->save();
            throw $exception;
        }
    }
}
