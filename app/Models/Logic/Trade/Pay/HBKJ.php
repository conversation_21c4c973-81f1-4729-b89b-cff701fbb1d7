<?php


namespace App\Models\Logic\Trade\Pay;


use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Data\Push\VerificationResult;
use Exception;
use Illuminate\Http\JsonResponse;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Throwable;

class HBKJ extends ThirdParty
{
    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019-07-31 19:46
     */
    public function handle()
    {
        $orderInfo = OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'platform_order_id',
                'operator' => '=',
                'value'    => $this->data['realData']['couponNo'],
            ],
            [
                'field'    => 'platform_name',
                'operator' => '=',
                'value'    => $this->name_abbreviation,
            ],
            [
                'field'    => 'self_order_status',
                'operator' => '=',
                'value'    => 1,
            ],
        ], ['*'], true, true);
        if (empty($orderInfo)) {
            return responseFormatForHbkj(5000018, [], false, '', 'couponNotice');
        }
        try {
            $this->checkOrderStatusByCenterForVerification($orderInfo->self_order_id, function ($code, $message) {
                if ($code != 0) {
                    throw new Exception($message, $code);
                }
            });
        } catch (Throwable $throwable) {
            return responseFormatForHbkj(
                $throwable->getCode(),
                [],
                false,
                $throwable->getMessage(),
                'couponNotice'
            );
        }
        if (OrderAssocData::getOrderInfoByWhere([
                [
                    'field'    => 'self_order_id',
                    'operator' => '=',
                    'value'    => $orderInfo->self_order_id,
                ],
            ], ['id'], false, true)->count() > 1) {
            (new VerificationResult([
                'order_id'            => $orderInfo->self_order_id,
                'verification_status' => 1,
                'verification_time'   => $this->data['realData']['useTime'],
            ]))->handle();
        }
        $data = FOSS_ORDERRequest::handle('/api/oil_adapter/payment/receiveResult', [
            'voucher'  => $orderInfo->platform_order_id,
            'usedtime' => $this->data['realData']['useTime'],
        ]);
        $orderInfo->platform_order_status = 1;
        $orderInfo->self_order_status = 1;
        $orderInfo->reason = $data['msg'] ?? '';
        $orderInfo->save();
        return responseFormatForHbkj(0, [], false, '', 'couponNotice');
    }
}
