<?php


namespace App\Models\Logic\Trade\Pay;


use App\Models\Data\OrderAssoc as OrderAssocData;
use Exception;
use Illuminate\Support\Facades\DB;
use Throwable;


class DH extends ThirdParty
{
    /**
     * @return void
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-31 19:46
     */
    public function handle()
    {
        if ($this->data['tradeStatus'] == 'wait') {
            responseFormatForDh(1, [
                'outOrderId' => $this->data['outOrderId'],
            ], true);
        }
        DB::beginTransaction();
        try {
            $orderAssocDao = OrderAssocData::getOneOrderByWhereAndLock([
                [
                    'field'    => 'self_order_id',
                    'operator' => '=',
                    'value'    => substr($this->data['outOrderId'], 2),
                ],
                [
                    'field'    => 'platform_name',
                    'operator' => '=',
                    'value'    => $this->name_abbreviation,
                ],
            ], ['*'], true);
            if (!isset($orderAssocDao->id)) {
                throw new Exception("", 5000024);
            }
            if ($orderAssocDao->platform_order_status != 2) {
                throw new Exception("", 5000115);
            }

            if ($this->data["tradeStatus"] == 'success') {
                $this->checkOrderStatusByCenterForVerification(
                    $orderAssocDao->self_order_id,
                    function ($code, $message) {
                        if ($code != 0) {
                            throw new Exception($message, $code);
                        }
                    }
                );
                $orderAssocDao->platform_order_status = 1;
                $orderAssocDao->save();
            }
            if ($this->data["tradeStatus"] == 'fail') {
                self::refundToTradeCenterForPayCallbackFailed(
                    $orderAssocDao,
                    $this->data['msg'] ?? '',
                    false
                );
            }
            DB::commit();
        } catch (Throwable $exception) {
            DB::rollBack();
            responseFormatForDh(
                $exception->getCode() == 0 ? 5000001 : $exception->getCode(),
                [
                    'outOrderId' => $this->data['outOrderId'],
                ],
                true,
                $exception->getMessage()
            );
        }
        responseFormatForDh(1, [
            'outOrderId' => $this->data['outOrderId'],
        ], true);
    }
}
