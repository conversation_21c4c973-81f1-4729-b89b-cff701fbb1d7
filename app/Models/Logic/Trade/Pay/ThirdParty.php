<?php


namespace App\Models\Logic\Trade\Pay;


use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Base;
use Closure;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Throwable;

abstract class ThirdParty extends Base
{
    protected $unionOrder = false;

    public function __construct(array $parameter)
    {
        parent::__construct($parameter);
        $this->checkOrder();
    }

    public function checkOrder()
    {
        if (!isset($this->data['trade_id'])) {

            return;
        }

        $orderInfo = OrderAssocData::getOrderInfoBySelfOrderId($this->data['trade_id']);

        if (count($orderInfo) > 1) {

            $this->unionOrder = true;
        }
    }

    public function checkOrderStatusByCenterForVerification($selfOrderId, Closure $callback)
    {
        $errorCode = 0;
        $errorMessage = '';
        try {

            $data = FOSS_ORDERRequest::handle('/api/oil_adapter/getOrderItem', [
                'order_id' => $selfOrderId,
            ]);
            if (empty($data['data'])) {
                $errorCode = 5000024;
                $errorMessage = config("error.5000024");
            }
            if ($data['data']['order_status'] != 2) {

                $errorCode = 5000103;
                $errorMessage = config("error.5000103");
            }
        } catch (Throwable $throwable) {
            $errorCode = $throwable->getCode();
            $errorMessage = empty($throwable->getMessage()) ? config("error.$errorCode", "") :
                $throwable->getMessage();
        }
        return $callback($errorCode, $errorMessage);
    }
}
