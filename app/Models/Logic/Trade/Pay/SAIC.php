<?php


namespace App\Models\Logic\Trade\Pay;


use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\Log\ResponseLog;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Data\Push\VerificationResult;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Request\GAS as GASRequest;
use stdClass;
use Throwable;
use Tool\Alarm\FeiShu;


class SAIC extends ThirdParty
{
    public const ACQUIRE_ORDER_LOCK_DURATION_PREFIX = "pay_order_";
    public const ACQUIRE_ORDER_LOCK_DURATION        = 10;

    /**
     * @return void
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019-07-31 19:46
     */
    public function handle()
    {
        $acquireCardLockKey = self::ACQUIRE_ORDER_LOCK_DURATION_PREFIX . 'saic_' . $this->data['outOrderId'];
        $connection = app('redis');
        $responseCall = function (int $code, $msg = '', $sendMessage = true, Throwable $exception = null) use (
            &$extend, &$orderAssocDao, $acquireCardLockKey, $connection
        ) {

            releaseRedisLock($connection, $acquireCardLockKey, $this->data['outOrderId']);
            ResponseLog::handle([
                'responseCode' => $code,
                'responseMsg'  => config("error.$code", $msg),
                'exception'    => $exception,
            ]);
            if ($sendMessage) {

                $source = $extend['owner']['pushExtends']['driver_source'] == 1 ? 'zeus' : 'mini_program';
                $sendMessageChannel = $source == 'zeus' ? GASRequest::class : FOSS_ORDERRequest::class;
                $messageData = $source == 'zeus' ? [
                    'gas.adapter.sendWsMessage',
                    [
                        'user_id'  => $orderAssocDao->self_order_id,
                        'msg_type' => 'order_pay_result',
                        'content'  => json_encode([
                            'status'                     => $code === 200 ? 'success' : 'failed',
                            'reason'                     => $msg,
                            'platform_name_abbreviation' => $this->name_abbreviation,
                            'pay_card_no'                => $extend['either']['asn'],
                            'station_name'               => $extend['owner']['trade_place'],
                            'amount'                     => $extend['owner']['pushExtends']['amount'],
                        ]),
                    ],
                    $this->accessKey,
                    $this->secret,
                ] : [
                    '/api/services/v1/thirdOrder/pushMsg',
                    [
                        "msg_type"                   => $code === 200 ? 2 : 3,
                        "pre_history_id"             => $orderAssocDao->self_order_id,
                        "card_no"                    => (string)$extend['owner']['vice_no'],
                        "message"                    => $msg,
                        "platform_name_abbreviation" => $this->name_abbreviation,
                        'pay_card_no'                => $extend['either']['asn'],
                        'station_name'               => $extend['owner']['trade_place'],
                        'amount'                     => $extend['owner']['pushExtends']['amountGun'],
                    ],
                ];
                if (OrderAssocData::getOrderInfoByWhere([
                        [
                            'field'    => 'self_order_id',
                            'operator' => '=',
                            'value'    => $orderAssocDao->self_order_id,
                        ],
                    ], ['id'], false, true)->count() == 1) {

                    (new $sendMessageChannel())->handle(...$messageData);
                } else {

                    (new VerificationResult([
                        'order_id'            => $orderAssocDao->self_order_id,
                        'verification_status' => $code === 200 ? 1 : 2,
                    ]))->handle();
                }
            }
            responseFormatForSaic((string)$code, new stdClass(), true, config("error.$code", $msg));
        };

        try {

            if ($connection->set($acquireCardLockKey, $this->data['outOrderId'], 'ex',
                self::ACQUIRE_ORDER_LOCK_DURATION, 'nx')) {

                $orderAssocDao = OrderAssocData::getOrderInfoByWhere([
                    [
                        'field'    => 'platform_order_id',
                        'operator' => '=',
                        'value'    => $this->data['outOrderId'],
                    ],
                    [
                        'field'    => 'platform_name',
                        'operator' => '=',
                        'value'    => $this->name_abbreviation,
                    ],
                ], ['*'], true, true);
                if (!isset($orderAssocDao->id)) {

                    $responseCall(5000024, '', false);
                }
                if ($orderAssocDao->platform_order_status != 2) {

                    $responseCall(200, '', false);
                }

                $extend = json_decode($orderAssocDao->extend, true);
                if ($this->data["tradeStatus"] != '0000') {

                    (new FeiShu())->writtenOffFailed([
                        "title"        => DockingPlatformInfoData::getFieldsByNameAbbreviation(
                                $this->name_abbreviation, 'platform_name',
                                '')['platform_name'] . "站点支付失败",
                        "reason"       => $this->data['tradeDesc'] ?? '',
                        "station_name" => $extend["owner"]["trade_place"],
                        "oil"          => $extend["owner"]["oil_name"],
                        "price"        => $extend["owner"]["supplier_price"],
                        "oil_num"      => $extend["owner"]["trade_num"],
                        "money"        => $extend["owner"]["supplier_money"],
                        "plate_number" => $extend["owner"]["truck_no"],
                        "driver_phone" => $extend["owner"]["drivertel"],
                    ]);
                    $responseCall(200, '', false);
                }
                $this->checkOrderStatusByCenterForVerification($orderAssocDao->self_order_id, function ($code, $message) use ($responseCall) {

                    if ($code != 0) {

                        $responseCall($code, $message, false);
                    }
                });
                $orderAssocDao->platform_order_status = 1;
                $orderAssocDao->save();
                $responseCall(200);
            } else {

                $responseCall(5000100);
            }
        } catch (Throwable $exception) {

            $responseCall(5000001, '', false, $exception);
        }
    }
}
