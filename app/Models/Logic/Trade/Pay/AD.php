<?php


namespace App\Models\Logic\Trade\Pay;


use App\Jobs\PushOrderResultForAD;
use App\Models\Dao\OrderAssoc as OrderAssocDao;
use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Common as CommonData;
use App\Models\Data\Log\ResponseLog;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Code\GetSecondaryPaymentQrCode;
use App\Models\Logic\Data\Push\AutonomousOrder;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Queue;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use stdClass;
use Throwable;


class AD extends ThirdParty
{
    /**
     * @param bool $throwException
     * @return ?JsonResponse|void
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/9/8 3:01 下午
     */
    public function handle(bool $throwException = false): ?JsonResponse
    {
        if (!isset($this->data['pay_status'])) {
            $this->data['pay_status'] = 1;
        }
        $orderInfo = OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'self_order_id',
                'operator' => '=',
                'value'    => $this->data['supplierOrderNo'],
            ],
            [
                'field'    => 'platform_name',
                'operator' => '=',
                'value'    => $this->name_abbreviation,
            ]
        ], ['*'], true, true);
        if (empty($orderInfo) or !$orderInfo instanceof OrderAssocDao or !$orderInfo->exists()) {
            if ($throwException) {
                throw new Exception(config("error.5000018"), 5000018);
            }
            return responseFormatForAd(5000018);
        }
        $extend = json_decode($orderInfo->extend ?? '', true);
        $pushOrderType = self::getPushOrderType($extend['trade_type'], $extend['supplier_code'] ?? '');
        try {
            if ($this->data['pay_status'] == 1) {
                $this->payByNew($orderInfo);
                //如果订单非扫码付扣款后需要回调安得确认支付,扫码付款在我方扣费前已经通知安得扣费，所以这里不回调
                //主动付款电子券供应商这里也不回调，安得要求核销成功时才做回调
                if ($pushOrderType != 2) {
                    $couponSupplier = [];
                    try {
                        $couponSupplier = json_decode(
                                              AuthConfigData::getAuthConfigValByName(
                                                  'COUPON_SUPPLIER'
                                              ),
                                              true
                                          ) ?? [];
                    } catch (Throwable $throwable) {
                        ResponseLog::handle([
                            'exception' => $throwable,
                        ]);
                    }
                    if (!in_array($extend['supplier_code'], $couponSupplier)) {
                        $extend = json_decode($orderInfo->extend, true) ?? [];
                        if (empty($extend['pushed_trade_success'])) {
                            Queue::later(
                                1,
                                new PushOrderResultForAD($orderInfo->toArray(), $pushOrderType),
                                '',
                                'adapter_deal_trade'
                            );
                        }
                    }
                }
                $responseData = self::getPayResult($this->data['supplierOrderNo'], $extend);
                $responseCode = $responseData->get_verification_certificate_async == 2 ? 100 : 0;
                if ($responseData->certificate_type == 'image') {
                    return responseFormatForAd($responseCode, [
                        'qrCode'     => $responseData->certificate_resource,
                        'qrCodeType' => 2,
                        'reason'     => '',
                    ]);
                }
                if ($responseData->certificate_type == 'url') {
                    return responseFormatForAd($responseCode, [
                        'data'   => [
                            'paymentUrl' => $responseData->certificate_resource,
                        ],
                        'reason' => '',
                    ]);
                }
                return responseFormatForAd($responseCode, [
                    'paymentUrl' => '',
                    'reason'     => '',
                ]);
            }
            $this->cancelByNew($orderInfo);
            return responseFormatForAd(0, [
                'reason' => '',
            ]);
        } catch (Throwable $throwable) {
            return responseFormatForAd($throwable->getCode(), [
                'reason' => $throwable->getMessage()
            ], false, $throwable->getMessage());
        }
    }

    /**
     * @param OrderAssocDao $orderAssocModel
     * @return void
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/9/8 3:18 下午
     */
    public function payByNew(OrderAssocDao $orderAssocModel)
    {
        try {
            FOSS_ORDERRequest::handle("/api/oil_adapter/oaPay", [
                'order_id'       => $this->data['supplierOrderNo'],
                'third_order_id' => (string)$this->data['orderNo'],
                'code'           => $this->data['pay_status'] - 1,
                'msg'            => mb_substr($this->data['remark'] ?? '', 0, 200),
                'remark'         => mb_substr($this->data['remark'] ?? '', 0, 200),
            ]);
            $orderAssocModel->self_order_status = $this->data['pay_status'];
            $orderAssocModel->platform_order_status = $this->data['pay_status'] ?? '';
            $orderAssocModel->platform_order_id = $this->data['orderNo'];
            if (!isset($this->data['updatePlatformReason']) or (isset($this->data['updatePlatformReason'])
                                                                and $this->data['updatePlatformReason'])) {
                $orderAssocModel->platform_reason = $this->data['remark'] ?? '';
            }
            $orderAssocModel->save();
        } catch (Throwable $exception) {
            $orderAssocModel->platform_order_id = $this->data['orderNo'];
            $orderAssocModel->reason = $exception->getMessage();
            if (!isset($this->data['updatePlatformReason']) or (isset($this->data['updatePlatformReason'])
                                                                and $this->data['updatePlatformReason'])) {
                $orderAssocModel->platform_reason = $this->data['remark'] ?? '';
            }
            $orderAssocModel->save();
            throw $exception;
        }
    }

    /**
     * @param OrderAssocDao $orderAssocModel
     * @return void
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/9/8 3:15 下午
     */
    public function cancelByNew(OrderAssocDao $orderAssocModel)
    {
        try {
            FOSS_ORDERRequest::handle("/api/oil_adapter/cancelOrder", [
                'order_id'       => $this->data['supplierOrderNo'],
                'pushMsgToDoper' => true,
                'pay_reason'     => '三方提示：' . mb_substr($this->data['remark'] ?? '', 0, 32),
            ]);
            $orderAssocModel->self_order_status = 4;
            $orderAssocModel->platform_order_status = 4;
            $orderAssocModel->platform_order_id = $this->data['orderNo'];
            if (!isset($this->data['updatePlatformReason']) or (isset($this->data['updatePlatformReason'])
                                                                and $this->data['updatePlatformReason'])) {
                $orderAssocModel->platform_reason = $this->data['remark'] ?? '';
            }
            $orderAssocModel->save();
        } catch (Throwable $exception) {
            $orderAssocModel->platform_order_id = $this->data['orderNo'];
            $orderAssocModel->reason = $exception->getMessage();
            if (!isset($this->data['updatePlatformReason']) or (isset($this->data['updatePlatformReason'])
                                                                and $this->data['updatePlatformReason'])) {
                $orderAssocModel->platform_reason = $this->data['remark'] ?? '';
            }
            $orderAssocModel->save();
            throw $exception;
        }
    }

    /**
     * @throws Throwable
     */
    public static function getPayResult($orderId, array $orderExtendInfo): stdClass
    {
        $responseData = new stdClass();
        $responseData->show_verification_certificate = 1;
        $responseData->certificate_type = "";
        $responseData->expiration = 0;
        $responseData->certificate_resource = "";
        $responseData->get_verification_certificate_async = 1;
        $autonomousOrderObj = new AutonomousOrder([
            'data' => [
                'pcode' => $orderExtendInfo['supplier_code'],
            ]
        ], false);
        if ($autonomousOrderObj->checkWorkerExists()) {
            $getSecondaryPaymentQrCodeObj = (new GetSecondaryPaymentQrCode([
                'supplier_code' => $orderExtendInfo['supplier_code'],
                'order_id'      => $orderId,
            ]));
            if ($getSecondaryPaymentQrCodeObj->checkWorkerExists()) {
                $responseData->show_verification_certificate = 2;
                try {
                    $response = $getSecondaryPaymentQrCodeObj->handle();
                    $secondaryPaymentQrCode = $response->getData(true);
                    if ($secondaryPaymentQrCode['code'] != 0) {
                        throw new Exception($secondaryPaymentQrCode['msg'], $secondaryPaymentQrCode['code']);
                    }
                    $responseData->certificate_type = $secondaryPaymentQrCode['data']['certificate_type'];
                    $responseData->certificate_resource = $secondaryPaymentQrCode['data']['certificate_resource'];
                    $responseData->expiration = $secondaryPaymentQrCode['data']['expiration'];
                } catch (Throwable $throwable) {
                    if (in_array(
                        $orderExtendInfo['supplier_code'],
                        json_decode(
                            AuthConfigData::getAuthConfigValByName('GET_VERIFICATION_CERTIFICATE_ASYNC'),
                            true
                        )
                    )) {
                        $responseData->get_verification_certificate_async = 2;
                        $responseData->show_verification_certificate = 2;
                        return $responseData;
                    }
                    throw $throwable;
                }
            }
        }
        return $responseData;
    }

    /**
     * 根据交易类型和供应商代码获取推送订单类型
     *
     * 主要用于内部逻辑处理，根据不同的业务场景确定订单的处理方式
     *
     * @param mixed $tradeType 交易类型，用于判断推送订单类型的基础之一
     * @param string $supplierCode 供应商代码
     * @return int 返回推送订单类型代码，1、3和5
     */
    public static function getPushOrderType($tradeType, string $supplierCode): int
    {
        // 默认推送订单类型设为2
        $pushOrderType = 2;
        // 如果交易类型属于主动付款
        if (in_array(
            $tradeType,
            CommonData::RECEIVE_TRADE_FOR_DOWNSTREAM_BY_TRADE_ENUM
        ) and $tradeType != 5) {
            // 推送订单类型更改为1
            $pushOrderType = 1;
            // 如果供应商代码同时在通过二维码核销的供应商列表中
            if (in_array(
                $supplierCode,
                json_decode(
                    AuthConfigData::getAuthConfigValByName(
                        'WRITTEN_OFF_BY_QR_CODE_SUPPLIER'
                    ),
                    true
                )
            )) {
                // 推送订单类型更改为3
                $pushOrderType = 3;
            }
            // 获取优惠券供应商列表
            $couponSupplier = json_decode(
                                  AuthConfigData::getAuthConfigValByName(
                                      'COUPON_SUPPLIER'
                                  ),
                                  true
                              ) ?? [];
            // 如果供应商代码在优惠券供应商列表中
            if (in_array(
                $supplierCode,
                $couponSupplier
            )) {
                // 推送订单类型更改为5
                $pushOrderType = 5;
            }
        }
        // 返回最终确定的推送订单类型
        return $pushOrderType;
    }
}
