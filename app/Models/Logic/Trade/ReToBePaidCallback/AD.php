<?php


namespace App\Models\Logic\Trade\ReToBePaidCallback;


use App\Jobs\PushOrderResultForAD;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Trade\Pay\AD as ADPay;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Queue;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Throwable;
use Tool\Alarm\FeiShu;


class AD extends ThirdParty
{
    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-31 19:46
     */
    public function handle()
    {
        $orderData = FOSS_ORDERRequest::handle('/api/oil_adapter/getOrderItem', [
            'order_id' => $this->data['thirdOrderNo'],
        ]);
        $data = [];
        try {
            if ($this->data['appendStatus'] == 10) {
                $data = FOSS_ORDERRequest::handle('/api/oil_adapter/oaPay', [
                    'order_id'       => $this->data['thirdOrderNo'],
                    'third_order_id' => $this->data['nopOrderNo'],
                ]);
            }
            if ($this->data['appendStatus'] == 20) {
                $data = FOSS_ORDERRequest::handle("/api/oil_adapter/cancelOrder", [
                    'order_id'       => $this->data['thirdOrderNo'],
                    'pushMsgToDoper' => false,
                    'pay_reason'     => $this->data['appendAuditRemark'],
                ]);
            }
        } catch (Throwable $exception) {
            (new FeiShu())->reToBePaidApproveResult([
                'platform_name'     => DockingPlatformInfoData::getPlatformNameByNameAbbreviation(
                    $this->name_abbreviation
                ),
                'status'            => '失败',
                'self_order_id'     => $this->data['thirdOrderNo'],
                'platform_order_id' => $this->data['nopOrderNo'],
                'station_name'      => $orderData['data']['station_name'],
                'oil'               => $orderData['data']['goods'],
                'price'             => $orderData['data']['oil_price'],
                'oil_num'           => $orderData['data']['oil_num'],
                'money'             => $orderData['data']['oil_money'],
                'driver_phone'      => $orderData['data']['driver_phone'],
                'reason'            => $this->data['appendAuditRemark'],
                'approve_status'    => $this->data['appendStatus'] == 10 ? '通过' : ($this->data['appendStatus'] == 20 ?
                    '驳回' : ''),
                'failed_reason'     => $exception->getMessage(),
            ]);
            if ($this->data['appendStatus'] == 10) {
                try {
                    $pushOrderType = ADPay::getPushOrderType(
                        $orderData['data']['trade_type'],
                        $orderData['data']['supplier_code']
                    );
                    Queue::push(
                        new PushOrderResultForAD(
                            OrderAssocData::getOrderInfoByWhere([
                                [
                                    'field'    => 'self_order_id',
                                    'operator' => '=',
                                    'value'    => $this->data['thirdOrderNo'],
                                ],
                                [
                                    'field'    => 'platform_name',
                                    'operator' => '=',
                                    'value'    => $this->name_abbreviation,
                                ],
                            ], ['*']), $pushOrderType
                        ),
                        "",
                        "adapter_deal_trade"
                    );
                } catch (Throwable $t) {
                }
            }
            try {
                $this->pushOrderTradeResultToOrderHub([
                    'self_order_status'     => 2,
                    'platform_order_status' => $this->data['appendStatus'] == 10 ? 1 : 3,
                    'self_order_id'         => $this->data['thirdOrderNo'],
                    'platform_order_id'     => $this->data['nopOrderNo'],
                    'reason'                => $exception->getMessage(),
                    'platform_reason'       => $this->data['appendAuditRemark'],
                ]);
            } catch (Throwable $throwable) {
                return responseFormatForAd($throwable->getCode(), [], false, $throwable->getMessage());
            }
            return responseFormatForAd($exception->getCode(), [], false, $exception->getMessage());
        }
        try {
            $this->pushOrderTradeResultToOrderHub([
                'self_order_status'     => $this->data['appendStatus'] == 10 ? 1 : 3,
                'platform_order_status' => $this->data['appendStatus'] == 10 ? 1 : 3,
                'self_order_id'         => $this->data['thirdOrderNo'],
                'platform_order_id'     => $this->data['nopOrderNo'],
                'platform_reason'       => $this->data['appendAuditRemark'],
                'reason'                => '',
            ]);
            OrderAssocData::updateOrderInfoByOrderId($this->data['thirdOrderNo'], [
                'self_order_status'     => $this->data['appendStatus'] == 10 ? 1 : 3,
                'platform_order_status' => $this->data['appendStatus'] == 10 ? 1 : 3,
                'self_order_id'         => $this->data['thirdOrderNo'],
                'platform_order_id'     => $this->data['nopOrderNo'],
                'reason'                => $data['msg'] ?? '',
                'platform_reason'       => $this->data['appendAuditRemark'],
            ]);
        } catch (Throwable $throwable) {
            return responseFormatForAd($throwable->getCode(), [], false, $throwable->getMessage());
        }
        (new FeiShu())->reToBePaidApproveResult([
            'platform_name'     => DockingPlatformInfoData::getPlatformNameByNameAbbreviation($this->name_abbreviation),
            'status'            => '成功',
            'self_order_id'     => $this->data['thirdOrderNo'],
            'platform_order_id' => $this->data['nopOrderNo'],
            'station_name'      => $orderData['data']['station_name'],
            'oil'               => $orderData['data']['goods'],
            'price'             => $orderData['data']['oil_price'],
            'oil_num'           => $orderData['data']['oil_num'],
            'money'             => $orderData['data']['oil_money'],
            'driver_phone'      => $orderData['data']['driver_phone'],
            'reason'            => $this->data['appendAuditRemark'],
            'approve_status'    => $this->data['appendStatus'] == 10 ? '通过' : ($this->data['appendStatus'] == 20 ?
                '驳回' : ''),
            'failed_reason'     => '',
        ]);
        return responseFormatForAd(0, []);
    }
}