<?php
/**
 * Created by PhpStorm.
 * User: zdl
 * Date: 2019-01-18
 * Time: 14:13
 */

namespace App\Models\Logic\Zsh;


use App\Models\Data\AuthConfig;
use App\Models\Logic\Base;
use Illuminate\Http\JsonResponse;
use Request\ZSH as ZSHRequest;
use Throwable;

class GetPrefAmount extends Base
{
    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019-07-02 14:07
     */
    public function handle()
    {
        $responseData = ZSHRequest::handle('GetPrefAmount', [
            'CustomerCode' => AuthConfig::getAuthConfigValByName("ZSH_PARTNER_ID"),
            'Year'         => $this->data['year'],
            'Month'        => $this->data['month'],
        ]);
        return responseFormat(0, [
            'prefAmount' => $responseData['PrefAmount']
        ]);
    }
}
