<?php

namespace App\Models\Logic\Bill\Push;

use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Logic\Base;

class Main extends Base
{
    /**
     * @var ThirdParty[]
     */
    private $workerMapping = [
        'ky' => KY::class,
    ];

    public function handle()
    {
        $nameAbbreviation = AuthInfoData::getAuthInfoByRoleCode(
            $this->data['org_code'],
            true
        )['name_abbreviation'] ?? '';
        if (!isset($this->workerMapping[$nameAbbreviation])) {
            responseFormat(5000145, null, true);
        }
        return (new $this->workerMapping[$nameAbbreviation]($this->data))->handle();
    }
}