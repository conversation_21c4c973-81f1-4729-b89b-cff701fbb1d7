<?php


namespace App\Models\Logic\Bill\Query;


use App\Models\Data\Bill;
use App\Models\Data\OrderAssoc as OrderAssocData;
use GosSDK\Defines\Methods\CardViceTrades;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Request\GOS as GOSRequest;
use Symfony\Component\HttpFoundation\Response;
use Throwable;


class Simple extends ThirdParty
{

    /**
     * @return Response
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/5/19 11:11 上午
     */
    public function handle(): Response
    {
        $requestParams = [
            'vice_no'   => $this->data['cardNo'],
            'orgcode'   => $this->data['orgCode'],
            'noPage'    => 1,
            'isOpenApi' => 1
        ];
        if (isset($this->data['trade_start_time']) and isset($this->data['trade_end_time'])) {
            $requestParams['trade_timeGe'] = $this->data['trade_start_time'];
            $requestParams['trade_timeLe'] = $this->data['trade_end_time'];
        }
        if (isset($this->data['create_start_time']) and isset($this->data['create_end_time'])) {
            $requestParams['createtimeGe'] = $this->data['create_start_time'];
            $requestParams['createtimeLe'] = $this->data['create_end_time'];
        }
        $billData = GOSRequest::handle(CardViceTrades::GET_LIST, $requestParams);
        if (empty($billData) or !is_array($billData)) {
            return responseFormat(0, []);
        }

        $billIds = [];
        //获取需要查询账单对应订单的账单ID
        foreach ($billData as $v) {
            if (bccomp($v->trade_money, 0, 2) === 1) {
                $billIds[] = $v->api_id;
            }
        }

        $requestParamsForGetRefundData = [
            'cancel_sn_in' => array_column($billData, 'cancel_sn'),
            'vice_no'      => $this->data['cardNo'],
            'orgcode'      => $this->data['orgCode'],
            'noPage'       => 1,
            'isOpenApi'    => 1,
        ];
        $this->filterEmptyValue($requestParamsForGetRefundData['cancel_sn_in']);
        $refundSnToOrderIdMapping = [];

        if (!empty($requestParamsForGetRefundData['cancel_sn_in'])) {
            //拼接退款账单和原始消费账单的关系
            $refundBillData = GOSRequest::handle(CardViceTrades::GET_LIST, $requestParamsForGetRefundData);

            foreach ($refundBillData as $v) {
                if (isset($v->cancel_sn) and !empty($v->cancel_sn) and
                                             strtolower($v->cancel_sn) != 'null') {
                    $refundSnToOrderIdMapping[$v->cancel_sn][] = $v->api_id ?? '';
                }

                if (bccomp($v->trade_money, 0, 2) === 1) {
                    $billIds[] = $v->api_id;
                }
            }
        }

        $orderInfos = convertListToDict(
            FOSS_ORDERRequest::handle(
                "/api/order/getBatchOrderItem",
                [
                    "order_ids" => $billIds,
                ]
            )["data"] ?? [],
            "order_id"
        );
        $realOrderInfos = convertListToDict(
            OrderAssocData::getOrderInfoByWhere([
                [
                    'field'    => 'self_order_id',
                    'value'    => array_merge(
                        array_column($orderInfos, 'id'),
                        array_column(
                            $orderInfos,
                            'order_id'
                        )
                    ),
                    'operator' => 'in'
                ],
                [
                    'field'    => 'platform_name',
                    'value'    => $this->name_abbreviation,
                    'operator' => '='
                ],
            ], [
                'self_order_id',
                'platform_order_id',
            ], false),
            'self_order_id'
        );

        foreach ($billData as &$v) {
            $v = get_object_vars($v);
            $v['refund_sn'] = "";

            if (isset($v['cancel_sn']) and !empty($v['cancel_sn']) and
                                           strtolower($v['cancel_sn']) != 'null') {
                $v['refund_sn'] = $v['cancel_sn'];
            }

            if (isset($orderInfos[$v['api_id']])) {
                $v = array_merge($v, $orderInfos[$v['api_id']]);
                $v['order_id'] = $realOrderInfos[$v['api_id']]['platform_order_id'] ?? '';
            } elseif (isset($refundSnToOrderIdMapping[$v['refund_sn']])) {
                $orderIdForRefundData = array_diff($refundSnToOrderIdMapping[$v['refund_sn']], [$v['api_id']]);

                if ($orderIdForRefundData) {
                    $v = array_merge($v, $orderInfos[array_values($orderIdForRefundData)[0]]);
                    $v["order_id"] = $realOrderInfos[$orderInfos[array_values(
                                                                     $orderIdForRefundData
                                                                 )[0]]['order_id']]['platform_order_id'] ?? '';
                }
            }
            if (bccomp($v['trade_money'], 0, 2) === 1) {
                $v['refund_sn'] = '';
            }
            Bill::formatBillDataNew($v);
        }

        return responseFormat(0, array_values($billData));
    }
}
