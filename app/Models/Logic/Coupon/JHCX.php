<?php

namespace App\Models\Logic\Coupon;

use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Base;
use Exception;
use Illuminate\Http\JsonResponse;
use Request\JHCX as JHCXRequest;

class JHCX extends Base
{
    public const COUPON_STATUS_DESC = [
        'NORMAL'      => '未核销',
        'CONSUMED'    => '已核销',
        'EXPIRE'      => '已过期',
        'UNAVAILABLE' => '已作废',
    ];

    protected $routeMapping = [
        // 派发接口
        'distribute'                 => [
            'path' => '/Api/DiyCard/code_up',
        ],
        // 获取核销码
        'getVerificationCertificate' => [
            'path' => '/Api/DiyCard/dycode_up',
        ],
        // 注销电子券
        'cancel'                     => [
            'path' => '/Api/DiyCard/code_unavailable',
        ],
        // 获取电子券状态
        'getState'                   => [
            'path' => '/Api/DiyCard/code_get',
        ],
        // 获取电子券详情
        'getCouponDetail'            => [
            'path' => '/Api/DiyCard/card_get',
        ],
        // 获取电子券列表
        'getCoupons'                 => [
            'path' => '/Api/DiyCard/card_batchget',
        ],
        // 渠道账户信息及充值明细查询
        'getAccountInfo'             => [
            'path' => '/Api/DiyCard/agent_info',
        ],
    ];

    /**
     * @throws Exception
     */
    public function handle(string $route = ''): JsonResponse
    {
        if (!isset($this->routeMapping[$route])) {

            throw new Exception("", 5000118);
        }
        if ($route == 'getState' or $route = 'cancel') {

            $this->data['code'] = OrderAssocData::getOrderInfoByWhere([
                [
                    'field'    => 'self_order_id',
                    'operator' => '=',
                    'value'    => $this->data['order_id'],
                ],
                [
                    'field'    => 'platform_name',
                    'operator' => '=',
                    'value'    => $this->name_abbreviation,
                ],
            ], ['platform_order_id'], true, true)->platform_order_id;
            unset($this->data['order_id']);
        }
        unset($this->data['supplier_code'], $this->data['auth_data']);
        $response = JHCXRequest::handle($this->routeMapping[$route]['path'], $this->data);
        return responseFormat(0, $response ?? []);
    }
}
