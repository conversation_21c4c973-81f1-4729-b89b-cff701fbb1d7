<?php

namespace App\Models\Logic\Coupon;

use App\Models\Logic\Base;
use Exception;
use Illuminate\Http\JsonResponse;
use Request\CNPC as CNPCRequest;

class CNPC extends Base
{
    protected $routeMapping = [
        // 获取优惠券类别
        'getTypes'                   => [
            'path' => '/open/json/open_coupon/getcoutypes',
        ],
        // 派发接口
        'distribute'                 => [
            'path' => '/open/json/open_coupon/distcoupons',
        ],
        // 获取核销码
        'getVerificationCertificate' => [
            'path' => '/open/json/open_coupon/getcheckcode',
        ],
        // 注销电子券
        'cancel'                     => [
            'path' => '/open/json/open_coupon/cancelcoupons',
        ],
        // 获取电子券状态
        'getState'                   => [
            'path' => '/open/json/open_coupon/getstate',
        ],
        // 申请电子券派发账单
        'applyDistBill'              => [
            'path' => '/open/json/open_coupon/applydistbill',
        ],
        // 获取电子券派发账单
        'getDistBill'                => [
            'path' => '/open/json/open_coupon/getdistbill',
        ],
        // 申请电子券使用账单
        'applyUsedBill'              => [
            'path' => '/open/json/open_coupon/applyusedbill',
        ],
        // 获取电子券使用账单
        'getUsedBill'                => [
            'path' => '/open/json/open_coupon/getusedbill',
        ],
        // 获取可用电子券列表
        'getUsableCoupons'           => [
            'path' => '/open/json/open_coupon/getUsableCoupons',
        ],
    ];

    /**
     * @throws Exception
     */
    public function handle(string $route = ''): JsonResponse
    {
        if (!isset($this->routeMapping[$route])) {

            throw new Exception("", 5000118);
        }
        unset($this->data['supplier_code'], $this->data['auth_data']);
        $response = CNPCRequest::handle($this->routeMapping[$route]['path'], $this->data);
        return responseFormat(0, $response);
    }
}
