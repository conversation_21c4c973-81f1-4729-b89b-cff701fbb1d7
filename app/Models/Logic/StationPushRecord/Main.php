<?php


namespace App\Models\Logic\StationPushRecord;


use App\Models\Data\StationPushRecord as StationPushRecordData;
use App\Models\Logic\Base;

class Main extends Base
{
    public function handle()
    {
        if (!empty($this->data['pushed_at'])) {

            $searchTime = explode(' - ', $this->data['pushed_at']);
            $this->data['startTime'] = trim($searchTime[0]);
            $this->data['endTime'] = trim($searchTime[1]);
        }

        $this->filterEmptyValue($this->data);

        return responseFormat(0, StationPushRecordData::getData($this->data));
    }
}
