<?php


namespace App\Models\Logic\Tool;


use App\Console\Commands\PushBillToSFFY;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Logic\Base;
use App\Models\Logic\Tool\PushBillPreCheck\SFFY;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Artisan;
use Throwable;

class PushBill extends Base
{
    private $jobMapping = [
        'sffy' => [
            PushBillToSFFY::class,
        ],
    ];
    private $jobParameters = [
        'sffy' => [
            '--billDate'              => '',
            '--failedStopImmediately' => '',
        ],
    ];

    private $jobPreCheckMapping = [
        'sffy' => SFFY::class,
    ];

    public function __construct(array $parameter, $nameAbbreviation = "")
    {
        $parameter['auth_data'] = AuthInfoData::getAuthInfoFieldByNameAbbreviation($nameAbbreviation);
        parent::__construct($parameter);
        if ($this->jobPreCheckMapping[$this->name_abbreviation]) {

            $this->data = (new $this->jobPreCheckMapping[$this->name_abbreviation]($this->data))->handle();
        }
    }

    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/2/4 3:31 下午
     */
    public function handle(): JsonResponse
    {
        if (isset($this->jobMapping[$this->name_abbreviation])) {

            foreach ($this->jobMapping[$this->name_abbreviation] as $jobClass) {
                $worker = (new $jobClass());
                if (!$worker instanceof Command) {

                    throw new Exception(config("error.5000001"), 5000001);
                }
                if (isset($this->jobParameters[$this->name_abbreviation])) {

                    foreach ($this->jobParameters[$this->name_abbreviation] as $k => $v) {

                        $replaceK = str_replace('-', '', $k);
                        if (isset($this->data[$replaceK])) {

                            $this->jobParameters[$this->name_abbreviation][$k] = $this->data[$replaceK];
                        }
                    }
                    Artisan::call($jobClass, $this->jobParameters[$this->name_abbreviation]);
                    continue;
                }
                Artisan::call($jobClass);
            }
        }

        return responseFormat();
    }
}
