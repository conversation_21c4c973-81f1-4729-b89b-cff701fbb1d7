<?php


namespace App\Models\Logic\Tool;


use App\Models\Data\StationPushSwitch as StationPushSwitchData;
use App\Models\Logic\Base;
use Illuminate\Http\JsonResponse;
use Request\FOSS_STATION as FOSS_STATIONRequest;
use Throwable;

class PushOilStation extends Base
{
    private $args;

    public function __construct(array $args)
    {
        parent::__construct($args);
        $this->args = $args;
    }

    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhu<PERSON><PERSON>@zhongqixing.com>
     * @since 2019/12/25 6:23 下午
     */
    public function handle()
    {
        //判断是否允许给该平台推送站点
        $stationPushTaskData = array_column(StationPushSwitchData::getOpenAll(), 'name_abbreviation');

        if (!in_array($this->args['nameAbbreviation'], $stationPushTaskData)) {

            return responseFormat(4120060);
        }

        $requestData = [];
        if (isset($this->args['p_code']) and !empty($this->args['p_code'])) {

            $requestData['pcode'] = $this->args['p_code'];
        } else {

            $requestData['pcode'] = "";
        }

        FOSS_STATIONRequest::handle('v1/station/getStationBatch', $requestData, 1200);
        return responseFormat();
    }
}
