<?php

namespace App\Models\Logic\Tool\PushBillPreCheck;

use Exception;

class SFFY extends ThirdParty
{

    /**
     * @throws Exception
     */
    public function handle()
    {
        if (strtotime($this->data['created_at_end'] ?? '') - strtotime(
                $this->data['created_at_start'] ?? '') > 86400) {

            throw new Exception(config("error.4120507"), 4120507);
        }
        $this->data['billDate'] = date("Y-m-d", strtotime($this->data['created_at_start']));
        $this->data['failedStopImmediately'] = 1;
        return $this->data;
    }
}
