<?php


namespace App\Models\Logic\Tool;


use App\Models\Logic\Base;
use App\Models\Logic\Tool\GetBillCheckResult\SFFY;
use Exception;
use Illuminate\Http\JsonResponse;
use Throwable;


class GetBillCheckResult extends Base
{
    private $jobMapping = [
        'sffy' => SFFY::class,
    ];

    public function __construct(array $parameter)
    {
        parent::__construct($parameter);
    }

    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/2/4 3:31 下午
     */
    public function handle()
    {
        if (!empty($this->data['created_at'])) {

            $searchTime = explode(' - ', $this->data['created_at']);
            $this->data['createdStartTime'] = trim($searchTime[0]);
            $this->data['createdEndTime'] = trim($searchTime[1]);
        }
        if (!isset($this->jobMapping[$this->data['name_abbreviation']])) {

            throw new Exception("", 5000007);
        }
        $result = (new $this->jobMapping[$this->data['name_abbreviation']]($this->data))->handle();
        if (checkIsAssocArray($result) and !empty($result)) {

            $result = [$result];
        }

        return responseFormat(0, [
            'list'  => $result,
            'count' => count($result)
        ]);
    }
}
