<?php


namespace App\Models\Logic\Tool;


use App\Models\Logic\Base;
use App\Models\Logic\Tool\GetDriverInfo\ZEY;
use Exception;
use Illuminate\Http\JsonResponse;
use Throwable;


class GetDriverInfo extends Base
{
    private $jobMapping = [
        'zey' => ZEY::class,
    ];

    public function __construct(array $parameter)
    {
        parent::__construct($parameter);
    }

    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2020/2/4 3:31 下午
     */
    public function handle()
    {
        if (!isset($this->jobMapping[$this->data['name_abbreviation']])) {

            throw new Exception("", 5000007);
        }

        $result = (new $this->jobMapping[$this->data['name_abbreviation']]($this->data))->handle();

        if (checkIsAssocArray($result) and !empty($result)) {

            $result = [$result];
        }

        return responseFormat(0, [
            'list'  => $result,
            'count' => count($result)
        ]);
    }
}
