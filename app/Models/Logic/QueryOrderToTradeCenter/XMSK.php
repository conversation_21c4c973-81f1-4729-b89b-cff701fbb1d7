<?php


namespace App\Models\Logic\QueryOrderToTradeCenter;

use App\Jobs\QueryOrderToTradeCenter;
use App\Models\Data\OrderAssoc as OrderAssocData;
use Illuminate\Support\Facades\DB;
use Request\XMSK as XMSKRequest;
use Throwable;

class XMSK extends Base
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2020/4/3 11:16 上午
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $orderAssocModel = OrderAssocData::getOneOrderByWhereAndLock([
                [
                    'field'    => 'platform_order_id',
                    'operator' => '=',
                    'value'    => $this->args['platform_order_id'],
                ],
                [
                    'field'    => 'platform_name',
                    'operator' => '=',
                    'value'    => $this->args['name_abbreviation'],
                ],
            ], ['*'], true);
            if (!$orderAssocModel or $orderAssocModel->platform_order_status != 2) {
                DB::commit();
                return;
            }
            $extend = json_decode($orderAssocModel->extend, true) ?? [];
            if (isset($extend['either']['orderState']) and $extend['either']['orderState'] == 4) {
                DB::commit();
                return;
            }
            $data = XMSKRequest::handle('ATH00003', [
                'orderNo' => $this->args['platform_order_id'],
            ]);
            if (!isset($extend['either'])) {
                $extend['either'] = [];
            }
            $extend['either']['orderState'] = $data['message']['data']['orderState'];
            $orderAssocModel->extend = json_encode($extend);
            $orderAssocModel->save();
            if (!array_has($data['message']['data'], [
                'orderState',
            ])) {
                DB::commit();
                $this->retryLaterQueue(
                    new QueryOrderToTradeCenter($this->args),
                    30,
                    "order_query_cycle_xmsk_{$this->args['platform_order_id']}",
                    3
                );
                return;
            }
            if (in_array($data['message']['data']['orderState'], [5, 6, 8])) {
                \App\Models\Logic\Base::refundToTradeCenterForPayCallbackFailed(
                    $orderAssocModel,
                    '',
                    false
                );
                DB::commit();
                return;
            }
            $this->retryLaterQueue(
                new QueryOrderToTradeCenter($this->args),
                30,
                "order_query_cycle_xmsk_{$this->args['platform_order_id']}",
                3
            );
            DB::commit();
            return;
        } catch (Throwable $throwable) {
            DB::commit();
            throw $throwable;
        }
    }
}
