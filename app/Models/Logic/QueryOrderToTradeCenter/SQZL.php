<?php


namespace App\Models\Logic\QueryOrderToTradeCenter;

use App\Jobs\QueryOrderToTradeCenter;
use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Base as CommonBaseLogic;
use App\Models\Logic\Data\Push\VerificationResult;
use Illuminate\Support\Facades\DB;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Request\GAS as GASRequest;
use Request\SQZL as SQZLRequest;
use Throwable;

class SQZL extends Base
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/4/3 11:16 上午
     */
    public function handle()
    {
        $orderAssocModel = OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'platform_order_id',
                'operator' => '=',
                'value'    => $this->args['platform_order_id'],
            ],
            [
                'field'    => 'platform_name',
                'operator' => '=',
                'value'    => $this->args['name_abbreviation'],
            ],
        ], ['*'], true, true);
        if (!$orderAssocModel) {
            return;
        }

        $extend = json_decode($orderAssocModel->extend, true);
        try {
            $data = SQZLRequest::handle('orderDetail/queryOrderDetail', [
                'uniqueStr'   => $this->args['platform_order_id'],
                'companyCode' => AuthConfigData::getAuthConfigValByName('SQZL_CHANNEL_CODE'),
            ]);
        } catch (Throwable $exception) {
            $this->retryLaterQueue(
                new QueryOrderToTradeCenter($this->args),
                2,
                "order_query_cycle_sqzl_{$this->args['platform_order_id']}",
                3
            );
            return;
        }
        if ($data['data']['orderType'] != 2) {
            if ($data['data']['orderType'] == 0) {
                $this->retryLaterQueue(
                    new QueryOrderToTradeCenter($this->args),
                    2,
                    "order_query_cycle_sqzl_{$this->args['platform_order_id']}",
                    3
                );
                return;
            }
            CommonBaseLogic::refundToTradeCenterForPayCallbackFailed(
                $orderAssocModel,
                $data['Message'] ?? '',
                false
            );
            return;
        }

        DB::beginTransaction();

        try {
            $orderAssocModel->platform_order_status = 1;
            $orderAssocModel->save();
            DB::commit();
        } catch (Throwable $e) {
            DB::rollBack();
            Log::handle(
                "Pay order failed for sqzl",
                [
                    "exception" => $e,
                ],
                DockingPlatformInfoData::getFieldsByNameAbbreviation(
                    "sqzl",
                    "platform_name",
                    ""
                )["platform_name"],
                "pay_order",
                "error"
            );
            $this->retryLaterQueue(
                new QueryOrderToTradeCenter($this->args),
                2,
                "order_query_cycle_sqzl_{$this->args['platform_order_id']}",
                3
            );
            return;
        }

        $authInfoData = AuthInfoData::getAuthInfoFieldByNameAbbreviation('sqzl');
        $source = $extend['owner']['pushExtends']['source'] ?? 'zeus';
        $sendMessageChannel = $source == 'zeus' ? GASRequest::class : FOSS_ORDERRequest::class;
        $messageData = $source == 'zeus' ? [
            'gas.adapter.sendWsMessage',
            [
                'user_id'  => $orderAssocModel->self_order_id,
                'msg_type' => 'order_pay_result',
                'content'  => json_encode([
                    'status'                     => 'success',
                    'reason'                     => '',
                    'platform_name_abbreviation' => 'sqzl',
                    'pay_card_no'                => $extend['either']['cardAsn'],
                    'station_name'               => $extend['owner']['trade_place'],
                    'amount'                     => $extend['owner']['pushExtends']['amountGun'],
                ]),
            ],
            $authInfoData['access_key'],
            $authInfoData['secret'],
        ] : [
            '/api/services/v1/thirdOrder/pushMsg',
            [
                "msg_type"                   => 2,
                "pre_history_id"             => $orderAssocModel->self_order_id,
                "card_no"                    => $extend['owner']['vice_no'],
                "message"                    => '',
                'platform_name_abbreviation' => 'sqzl',
                'pay_card_no'                => $extend['either']['cardAsn'],
                'station_name'               => $extend['owner']['trade_place'],
                'amount'                     => $extend['owner']['pushExtends']['amountGun'],
            ],
        ];
        if (OrderAssocData::getOrderInfoByWhere([
                [
                    'field'    => 'self_order_id',
                    'operator' => '=',
                    'value'    => $orderAssocModel->self_order_id,
                ],
            ], ['id'], false, true)->count() == 1) {
            (new $sendMessageChannel())->handle(...$messageData);
        } else {
            (new VerificationResult([
                'order_id'            => $orderAssocModel->self_order_id,
                'verification_status' => 1,
            ]))->handle();
        }
    }
}
