<?php


namespace App\Models\Logic\QueryOrderToTradeCenter;

use App\Jobs\QueryOrderToTradeCenter;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Data\Push\VerificationResult;
use Illuminate\Support\Facades\DB;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Request\GAS as GASRequest;
use Request\YC as YCRequest;
use Throwable;
use Tool\Alarm\FeiShu;

class YC extends Base
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/4/3 11:16 上午
     */
    public function handle()
    {
        $orderAssocData = OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'platform_order_id',
                'operator' => '=',
                'value'    => $this->args['platform_order_id'],
            ],
            [
                'field'    => 'platform_name',
                'operator' => '=',
                'value'    => $this->args['name_abbreviation'],
            ],
        ], ['*'], true, true);
        if (!$orderAssocData) {
            return;
        }

        $extend = json_decode($orderAssocData['extend'], true);
        $failedCall = function () use ($extend) {
            $platformOilCode = $extend['either']['productList'][0]['productCode'] ?? '';
            $oilMapping = explode(
                '_',
                config(
                    "oil.oil_mapping.yc.$platformOilCode"
                )
            );
            (new FeiShu())->queryOrderFailed([
                "platform_name"      => "广东壳牌",
                "reason"             => "壳牌超过120s未返回扣款结果",
                "station_name"       => $extend["owner"]["trade_place"],
                "oil"                => $extend["owner"]["oil_name"],
                "mac_price"          => $extend['owner']['pushExtends']['priceGun'],
                "platform_mac_price" => '--',
                "oil_num"            => $extend["owner"]["trade_num"],
                "mac_money"          => $extend['owner']['pushExtends']['amountGun'],
                "plate_number"       => $extend["owner"]["truck_no"],
                "platform_mac_money" => '--',
                "platform_oil"       => (array_flip(config('oil.oil_type'))[$oilMapping[0]] ?? '') . config(
                        "oil.oil_no_simple.$oilMapping[1]",
                        ""
                    ) . (array_flip(config('oil.oil_level'))[$oilMapping[2]] ?? ''),
                "driver_phone"       => $extend["owner"]["drivertel"],
            ]);
        };

        try {
            $data = YCRequest::handle('order', [
                'orderid' => $this->args['platform_order_id'],
            ]);
        } catch (Throwable $exception) {
            $this->retryLaterQueue(
                new QueryOrderToTradeCenter($this->args),
                30,
                "order_query_cycle_yc_{$this->args['platform_order_id']}",
                3,
                $failedCall
            );
            return;
        }


        if (!isset($data['data']) or !array_has($data['data'], [
                'fleetOrderID',
                'orderID',
                'stationID',
                'transactionTime',
                'oilNo',
                'price',
                'litre',
                'amount',
            ])) {
            $this->retryLaterQueue(
                new QueryOrderToTradeCenter($this->args),
                30,
                "order_query_cycle_yc_{$this->args['platform_order_id']}",
                3,
                $failedCall
            );
            return;
        }

        DB::beginTransaction();

        try {
            $extend['either'] = $data['data'];
            OrderAssocData::updateOrderInfoByOrderId($this->args['platform_order_id'], [
                'extend'                => json_encode($extend),
                'platform_order_status' => 1
            ], false);
            DB::commit();
        } catch (Throwable $e) {
            DB::rollBack();
            Log::handle(
                "Pay order failed for yc",
                [
                    "exception" => $e,
                ],
                DockingPlatformInfoData::getFieldsByNameAbbreviation(
                    "yc",
                    "platform_name",
                    ""
                )["platform_name"],
                "pay_order",
                "error"
            );
            $this->retryLaterQueue(
                new QueryOrderToTradeCenter($this->args),
                30,
                "order_query_cycle_yc_{$this->args['platform_order_id']}",
                3,
                $failedCall
            );
            return;
        }

        $authInfoData = AuthInfoData::getAuthInfoFieldByNameAbbreviation('yc');
        $source = $extend['owner']['pushExtends']['source'] ?? 'zeus';
        $sendMessageChannel = $source == 'zeus' ? GASRequest::class : FOSS_ORDERRequest::class;
        $messageData = $source == 'zeus' ? [
            'gas.adapter.sendWsMessage',
            [
                'user_id'  => $orderAssocData['self_order_id'],
                'msg_type' => 'order_pay_result',
                'content'  => json_encode([
                    'status'                     => 'success',
                    'reason'                     => '',
                    'platform_name_abbreviation' => 'yc',
                ]),
            ],
            $authInfoData['access_key'],
            $authInfoData['secret'],
        ] : [
            '/api/services/v1/thirdOrder/pushMsg',
            [
                "msg_type"                   => 2,
                "pre_history_id"             => $orderAssocData['self_order_id'],
                "card_no"                    => $extend['owner']['vice_no'],
                "message"                    => '',
                'platform_name_abbreviation' => 'yc',
            ],
        ];
        if (OrderAssocData::getOrderInfoByWhere([
                [
                    'field'    => 'self_order_id',
                    'operator' => '=',
                    'value'    => $orderAssocData['self_order_id'],
                ],
            ], ['id'], false, true)->count() == 1) {
            (new $sendMessageChannel())->handle(...$messageData);
        } else {
            (new VerificationResult([
                'order_id'            => $orderAssocData['self_order_id'],
                'verification_status' => 1,
            ]))->handle();
        }
    }
}
