<?php
/**
 * Created by <PERSON>pStorm.
 * User: zdl
 * Date: 2019-01-16
 * Time: 15:35
 */

namespace App\Models\Logic;


use App\Models\Dao\OrderAssoc as OrderAssocDao;
use App\Models\Dao\QueueLog as QueueLogDao;
use App\Models\Dao\ReceiveLog as ReceiveLogDao;
use App\Models\Dao\RequestLog as RequestLogDao;
use App\Models\Dao\ResponseLog as ResponseLogDao;
use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\Log\QueueLog;
use App\Models\Data\Log\ResponseLog as ResponseLogData;
use App\Models\Data\OrderAssoc as OrderAssocData;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Queue;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Request\FOSS_STATION as FOSS_STATIONRequest;
use Throwable;
use Tool\Alarm\FeiShu;

abstract class Base
{
    protected $data;
    protected $accessKey         = '';
    protected $secret            = '';
    protected $name_abbreviation = '';

    public function __construct(array $parameter)
    {
        $this->accessKey = $parameter['auth_data']['access_key'] ?? '';
        $this->secret = $parameter['auth_data']['secret'] ?? '';
        $this->name_abbreviation = $parameter['auth_data']['name_abbreviation'] ?? '';
        $this->data = $parameter;
    }

    /**
     * @param OrderAssocDao $orderInfoModel
     * @param string $failedReason
     * @param bool $interruptProcess
     * @return void
     * @throws Exception
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2021/3/1 8:26 下午
     */
    public static function refundToTradeCenterForPayCallbackFailed(
        OrderAssocDao $orderInfoModel,
        string $failedReason,
        bool $interruptProcess = true
    ) {
        try {
            FOSS_ORDERRequest::handle("/api/oil_adapter/oaRefund", [
                "order_id" => $orderInfoModel->self_order_id,
            ]);
            $orderInfoModel->self_order_status = 3;
            $orderInfoModel->platform_order_status = 3;
            $orderInfoModel->reason = "";
            $orderInfoModel->platform_reason = $failedReason;
            $orderInfoModel->save();
        } catch (Throwable $throwable) {
            $orderInfoModel->self_order_status = 1;
            $orderInfoModel->platform_order_status = 2;
            $orderInfoModel->reason = $throwable->getMessage();
            $orderInfoModel->platform_reason = $failedReason;
            $orderInfoModel->save();
            throw $throwable;
        }
        if ($interruptProcess) {
            $errorMsg = config("error.$failedReason", '');
            throw new Exception(
                $errorMsg != '' ? $errorMsg : $failedReason,
                $errorMsg != '' ? (int)$failedReason : 5000999
            );
        }
    }

    /**
     * 更新订单状态及抛出异常让外层拦截并返回
     * @param string $selfOrderId
     * @param string $reason
     * @param int $code
     * ---------------------------------------------------
     * @throws Throwable
     * @since 2019/11/20 2:35 下午
     * <AUTHOR> <<EMAIL>>
     */
    public static function cancelToGasAndUpdateOrder(string $selfOrderId, string $reason, int $code = 0)
    {
        OrderAssocData::updateOrderInfoByOrderId($selfOrderId, [
            'self_order_status' => 4,
            'platform_reason'   => $reason == '' ? config("error.$code") : $reason,
        ]);
        throw new Exception(
            !empty(config("error.$code")) ? config("error.$code") :
                $reason, $code
        );
    }

    /**
     * @param array $parameters
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/3/29 8:58 下午
     */
    public static function getCurrentDayLogCountByType(array $parameters): array
    {
        switch ($parameters['log_type']) {
            case 'request_log':
                $model = new RequestLogDao();
                break;
            case 'queue_log':
                $model = new QueueLogDao();
                break;
            case 'receive_log':
                $model = new ReceiveLogDao();
                break;
            case 'response_log':
                $model = new ResponseLogDao();
                break;
            default:
                return [];
        }

        $countQuery = DB::raw('count(id) as Count');

        if ($parameters['log_type'] != 'queue_log') {
            $countQuery = DB::raw('count(distinct route_id) as Count');
        }

        $data = $model
            ->setTable(
                $model->getTable() . '_' . Carbon::createFromTimestamp(time())->yearIso .
                '_' . date("W")
            )
            ->where('created_at', '>=', date("Y-m-d 00:00:00"))
            ->where('created_at', '<=', date("Y-m-d 23:59:59"))
            ->groupBy([DB::raw('hour(created_at)')])
            ->orderBy(DB::raw('hour(created_at)'))
            ->get([
                DB::raw('hour(created_at) as Hour'),
                $countQuery,
            ]);
        $data = $data ? $data->toArray() : [];
        $xStart = 0;
        $xAxis = [];

        if (!empty($data)) {
            $xEnd = max(array_column($data, 'Hour'));
            $xData = convertListToDict($data, 'Hour');

            for ($i = $xStart; $i <= $xEnd; $i++) {
                $xAxis[$i < 10 ? "0$i" : $i] = $xData[$i]['Count'] ?? 0;
            }
        }

        return [
            'xAxisData' => array_keys($xAxis),
            'xData'     => array_values($xAxis)
        ];
    }

    abstract public function handle();

    public function filterEmptyValue(array &$params)
    {
        foreach ($params as $k => $v) {
            if (empty($v)) {
                unset($params[$k]);
            }
        }
    }

    public function pushQueueAndScreenExp($job, array $initData = [], $data = null, $queueName = '')
    {
        try {
            if (!empty($initData)) {
                $jobObj = new $job(...$initData);
            } else {
                $jobObj = new $job();
            }
            Queue::push($jobObj, $data, $queueName);
        } catch (Throwable $exception) {
            if ($exception->getCode() != 5000129) {
                QueueLog::handle('Push task to queue failed', [
                    'data'      => func_get_args(),
                    'exception' => $exception,
                ], '系统', 'oil_station_data', 'error');
            }
        }
    }

    /**
     * 根据外部油站ID以及
     * @param string $appStationId
     * @return mixed
     * @throws Throwable|Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-29 16:17
     */
    public function getGasStationIdByAppStationIdAndPCode(string $appStationId)
    {
        $stationIdData = FOSS_STATIONRequest::handle('v1/station/getStationByAppId', [
            'pcode'          => $this->data['auth_data']['role_code'],
            'app_station_id' => $appStationId,
        ]);
        if ($stationIdData['code'] != 0) {
            throw new Exception("站点ID转换失败", 5000999);
        }
        if (empty($stationIdData) or !array_has($stationIdData['data'], ['id'])) {
            throw new Exception("查询站点信息异常", 5000999);
        }

        return $stationIdData['data']['id'];
    }

    /**
     * 通过给定油品映射配置名,check传入油品是否合法,如合法将匹配后的值写入传入变量
     * @param array $oilData ['oilLevel' => '', 'oilName' => '柴油', 'oilType' => '0#']
     * @param string $oilLevelKey
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/12/2 11:45 上午
     */
    public function checkOil(array &$oilData, string $oilLevelKey)
    {
        $oilNoMapping = array_flip(config("oil.oil_no_simple"));
        $oilTypeMapping = config("oil.oil_type");
        $oilLevelMapping = config($oilLevelKey);

        if (!empty($oilData['oilType']) and !isset($oilNoMapping[$oilData['oilType']])) {
            throw new Exception("", 4120141);
        }

        if (!isset($oilTypeMapping[$oilData['oilName']])) {
            throw new Exception("", 4120142);
        }

        if (!empty($oilData['oilLevel']) and !isset($oilLevelMapping[$oilData['oilLevel']])) {
            throw new Exception("", 4120016);
        }

        $oilData['oil_name'] = $oilTypeMapping[$this->data['oilName']];
        $oilData['oil_type'] = $oilNoMapping[$this->data['oilType']] ?? '';
        $oilData['oil_level'] = $oilLevelMapping[$oilData['oilLevel']] ?? '';
    }

    /**
     * @param array $orderTradeResult
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/6/17 11:26 上午
     */
    public function pushOrderTradeResultToOrderHub(array $orderTradeResult)
    {
        $orderStatusMapping = [
            1 => 2,
            2 => 3,
            4 => 3,
            3 => 3,
        ];
        if (!array_has($orderTradeResult, [
            'self_order_id',
            'self_order_status',
            'platform_order_id',
            'platform_order_status',
            'reason',
            'platform_reason'
        ])) {
            throw new Exception("Order data is invalid");
        }

        if (!isset($orderStatusMapping[$orderTradeResult['self_order_status']]) or
            !isset($orderStatusMapping[$orderTradeResult['platform_order_status']])) {
            throw new Exception("Order status is invalid");
        }

        FOSS_ORDERRequest::handle("/api/additional/v1/additional/updateApproveItem", [
            'order_id'           => $orderTradeResult['self_order_id'],
            'order_status'       => $orderStatusMapping[$orderTradeResult['self_order_status']],
            'order_msg'          => $orderTradeResult['reason'],
            'third_order_id'     => $orderTradeResult['platform_order_id'],
            'third_order_status' => $orderStatusMapping[$orderTradeResult['platform_order_status']],
            'third_order_msg'    => $orderTradeResult['platform_reason'],
        ]);
    }

    /**
     * @param int $selfOrderStatus
     * @param int $platformOrderStatus
     * @param string $selfOrderId
     * @param string $platformOrderId
     * @param string $reason
     * @param string $platformReason
     * @param array $extend
     * @return OrderAssocDao
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/9/8 10:51 上午
     */
    public function createOrder(
        int $selfOrderStatus,
        int $platformOrderStatus,
        string $selfOrderId,
        string $platformOrderId,
        string $reason = '',
        string $platformReason = '',
        array $extend = []
    ): OrderAssocDao {
        $extend = array_merge($extend, $this->data['data'] ?? []);
        return OrderAssocData::insert(
            $selfOrderStatus,
            $platformOrderStatus,
            $selfOrderId,
            $platformOrderId,
            $this->name_abbreviation,
            $reason,
            $platformReason,
            json_encode($extend)
        );
    }

    /**
     * 根据机构代码和配置名称获取客户实体代码
     *
     * 此函数用于从配置数据中检索与特定机构代码和配置名称对应的客户实体代码
     * 如果未找到匹配项且参数=default为true，则返回默认配置名称对应的配置项
     *
     * @param string $orgCode 机构代码，用于在配置中查找对应的客户实体代码
     * @param string $configName 配置名称，指定要查找的配置项
     * @param string $defaultConfigName 默认配置名称，当找不到匹配项且=default为true时使用
     * @param bool $default 是否使用默认配置项的标志，默认为false
     *
     * @return string 客户实体代码，如果未找到则返回空字符串
     */
    public static function getCustomerEntityCodeByOrgCodeAndConfigName(
        string $orgCode,
        string $configName,
        string $defaultConfigName,
        bool $default = false
    ): string {
        // 通过配置名称获取配置数组，并尝试根据机构代码获取对应的客户实体代码
        $accountingEntityCode = array_flip(
                                    json_decode(AuthConfigData::getAuthConfigValByName(
                                        $configName
                                    ), true)
                                )[$orgCode] ?? '';
        // 如果未找到客户实体代码且=default为true，则尝试获取默认配置名称对应的配置项
        if (empty($accountingEntityCode) and $default) {
            $accountingEntityCode = AuthConfigData::getAuthConfigValByName(
                $defaultConfigName
            );
        }
        // 返回找到的客户实体代码，如果没有找到则返回空字符串
        return $accountingEntityCode;
    }

    public function refundAlarm(OrderAssocDao $orderInfo)
    {
        try {
            if (empty($orderInfo->self_order_id) or empty($orderInfo->platform_order_id) or
                empty($orderInfo->extend)) {
                return;
            }
            $orgOrderInfo = OrderAssocData::getOrderInfoByWhere([
                [
                    'field'    => 'self_order_id',
                    'operator' => '=',
                    'value'    => $orderInfo->self_order_id,
                ],
                [
                    'field'    => 'platform_name',
                    'operator' => '!=',
                    'value'    => $this->name_abbreviation,
                ],
            ], ['self_order_id', 'platform_order_id', 'platform_name'], true, true);
            if (!$orgOrderInfo or !$orgOrderInfo->exists()) {
                return;
            }
            $extend = json_decode($orderInfo->extend, true) ?? [];
            $ownerOrderInfo = $extend['owner'] ?? $extend;
            (new FeiShu())->supplierRefundAlarm([
                'supplier_order_id' => $orderInfo->platform_order_id,
                'self_order_id'     => $orderInfo->self_order_id,
                'platform_order_id' => $orgOrderInfo->platform_order_id,
                'supplier_name'     => DockingPlatformInfoData::getPlatformNameByNameAbbreviation(
                    $this->name_abbreviation
                ),
                'station_name'      => $ownerOrderInfo['trade_place'],
                'org_name'          => DockingPlatformInfoData::getPlatformNameByNameAbbreviation(
                    $orgOrderInfo->platform_name
                ),
                'price'             => $ownerOrderInfo['trade_price'],
                'oil_num'           => $ownerOrderInfo['oil_num'],
                'money'             => $ownerOrderInfo['trade_money'],
            ]);
        } catch (Throwable $e) {
            ResponseLogData::handle([
                'exception' => $e,
            ]);
        }
    }
}
