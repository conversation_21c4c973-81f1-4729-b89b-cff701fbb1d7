<?php
/**
 * Created by <PERSON>pStor<PERSON>.
 * User: zdl
 * Date: 2019-01-17
 * Time: 19:03
 */

namespace App\Models\Logic\Code;


use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Base;
use App\Models\Logic\Code\GetSecondaryPaymentQrCode\CNPC;
use App\Models\Logic\Code\GetSecondaryPaymentQrCode\CY;
use App\Models\Logic\Code\GetSecondaryPaymentQrCode\DH;
use App\Models\Logic\Code\GetSecondaryPaymentQrCode\DT;
use App\Models\Logic\Code\GetSecondaryPaymentQrCode\GDQP;
use App\Models\Logic\Code\GetSecondaryPaymentQrCode\GS;
use App\Models\Logic\Code\GetSecondaryPaymentQrCode\HBKJ;
use App\Models\Logic\Code\GetSecondaryPaymentQrCode\HSY;
use App\Models\Logic\Code\GetSecondaryPaymentQrCode\JHCX;
use App\Models\Logic\Code\GetSecondaryPaymentQrCode\JT;
use App\Models\Logic\Code\GetSecondaryPaymentQrCode\JTX;
use App\Models\Logic\Code\GetSecondaryPaymentQrCode\SAIC;
use App\Models\Logic\Code\GetSecondaryPaymentQrCode\SC;
use App\Models\Logic\Code\GetSecondaryPaymentQrCode\SH;
use App\Models\Logic\Code\GetSecondaryPaymentQrCode\SHSX;
use App\Models\Logic\Code\GetSecondaryPaymentQrCode\SQZL;
use App\Models\Logic\Code\GetSecondaryPaymentQrCode\ThirdParty;
use App\Models\Logic\Code\GetSecondaryPaymentQrCode\WJY;
use App\Models\Logic\Code\GetSecondaryPaymentQrCode\XMSK;
use App\Models\Logic\Code\GetSecondaryPaymentQrCode\YC;
use App\Models\Logic\Code\GetSecondaryPaymentQrCode\ZDC;
use App\Models\Logic\Code\GetSecondaryPaymentQrCode\ZY;
use Illuminate\Http\JsonResponse;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Throwable;


class GetSecondaryPaymentQrCode extends Base
{
    private static $classMapping = [
        'sc'        => SC::class,
        'yc'        => YC::class,
        'sx'        => SC::class,
        'fj'        => SC::class,
        'gs'        => GS::class,
        'zy'        => ZY::class,
        'zyLng'     => ZY::class,
        'wjy'       => WJY::class,
        'hb'        => SC::class,
        'saic_aj'   => SAIC::class,
        'saic_fl'   => SAIC::class,
        'saic'      => SAIC::class,
        'cnpc'      => CNPC::class,
        'dt_zj'     => DT::class,
        'dt'        => DT::class,
        'jhcx'      => JHCX::class,
        'saic_ajsw' => SAIC::class,
        'cy'        => CY::class,
        'hbkj'      => HBKJ::class,
        'saic_yc'   => SAIC::class,
        'hsy'       => HSY::class,
        'sqzl'      => SQZL::class,
        'gdqp'      => GDQP::class,
        'zdc'       => ZDC::class,
        'sh'        => SH::class,
        'shsx'      => SHSX::class,
        'jt'        => JT::class,
        'jtx'       => JTX::class,
        'dh'        => DH::class,
        'xmsk'      => XMSK::class,
        'zjqp'      => SC::class,
    ];
    /**
     * @var ThirdParty
     */
    private $worker = null;

    private $orderIdMapping = [
        'fy' => 'orderId',
    ];

    /**
     * GetSecondaryPaymentQrCode constructor.
     * @param array $parameter
     * @throws Throwable
     */
    public function __construct(array $parameter)
    {
        parent::__construct($parameter);
        $this->orderIdConvert();
        if (!isset($this->data['supplier_code']) and $this->data['auth_data']['role'] == 6) {
            $data = FOSS_ORDERRequest::handle('/api/oil_adapter/getOrderItem', [
                'order_id' => $this->data['order_id'],
            ]);
            if ($data['data']['order_status'] != 2) {
                responseFormat(5000115, null, true);
            }
            $realOrderModel = null;
            $orderInfoModel = OrderAssocData::getOrderInfoByWhere([
                [
                    'field'    => 'self_order_id',
                    'operator' => '=',
                    'value'    => $this->data['order_id'],
                ],
            ], ["*"], false, true);
            if (!$orderInfoModel) {
                responseFormat(5000018, null, true);
            }
            if ($orderInfoModel->count() > 1) {
                foreach ($orderInfoModel as $v) {
                    if ($v->platform_name != $this->name_abbreviation) {
                        $realOrderModel = $v;
                        break;
                    }
                }
            } else {
                if ($orderInfoModel->count() <= 0) {
                    responseFormat(5000018, null, true);
                }
                $realOrderModel = $orderInfoModel[0];
            }
            $authInfo = AuthInfoData::getAuthInfoFieldByNameAbbreviation($realOrderModel->platform_name);
            if (empty($authInfo)) {
                responseFormat(5000018, null, true);
            }
            $this->worker = new self::$classMapping[$authInfo['name_abbreviation']](
                array_merge($this->data, [
                    'auth_data' => $authInfo,
                ])
            );
            return;
        }
        $authInfo = AuthInfoData::getAuthInfoByRoleCode($this->data['supplier_code'], true);
        if (isset(self::$classMapping[$authInfo['name_abbreviation']])) {
            $this->worker = new self::$classMapping[$authInfo['name_abbreviation'] ?? ''](
                array_merge(
                    $this->data,
                    [
                        'auth_data' => $authInfo,
                    ]
                )
            );
        }
    }

    /**
     * @return JsonResponse
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/11/12 3:50 下午
     */
    public function handle()
    {
        if (!$this->worker) {
            return responseFormat(5000101);
        }
        return $this->worker->handle();
    }

    /**
     * @return bool
     * <AUTHOR> <<EMAIL>>
     * @since 2021/6/23 6:18 下午
     */
    public function checkWorkerExists(): bool
    {
        return (bool)$this->worker;
    }

    public function orderIdConvert()
    {
        if (isset($this->orderIdMapping[$this->name_abbreviation])) {
            $this->data['order_id'] = $this->data[$this->orderIdMapping[$this->name_abbreviation]] ?? '';
        }
    }
}

