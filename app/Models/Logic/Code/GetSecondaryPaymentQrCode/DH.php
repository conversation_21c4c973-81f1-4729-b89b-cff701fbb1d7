<?php


namespace App\Models\Logic\Code\GetSecondaryPaymentQrCode;


use App\Models\Dao\OrderAssoc as OrderAssocDao;
use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\OrderAssoc as OrderAssocData;
use Illuminate\Http\JsonResponse;
use Throwable;


class DH extends ThirdParty
{
    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2020/6/9 5:03 下午
     */
    public function handle()
    {
        $orderInfo = OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'platform_name',
                'operator' => '=',
                'value'    => $this->name_abbreviation,
            ],
            [
                'field'    => 'self_order_id',
                'operator' => '=',
                'value'    => $this->data['order_id'],
            ],
        ], [
            'id',
            'platform_order_status',
            'platform_order_id',
            'extend',
        ], true, true);
        if (!$orderInfo instanceof OrderAssocDao) {
            return responseFormat(5000024);
        }
        if ($orderInfo->platform_order_status != 1) {
            return responseFormat(5000136);
        }
        $extend = json_decode($orderInfo->extend, true);
        return responseFormat(0, [
            'certificate_type'           => "url",
            'certificate_resource'       => renderMessageTemplate([
                AuthConfigData::getAuthConfigValByName(
                    "DH_SECONDARY_PAYMENT_URL"
                )
            ], [
                'stationName' => urlencode($extend['owner']['trade_place']),
                'payTime'     => urlencode($extend['owner']['trade_time']),
                'cardNo'      => urlencode($extend['owner']['supplementary_card_no']),
                'gunMoney'    => number_format(
                    (float)$extend['owner']['pushExtends']['amountGun'],
                    2,
                    '.',
                    ''
                ),
            ]),
            'platform_name_abbreviation' => $this->name_abbreviation,
            'expiration'                 => -1,
        ]);
    }
}