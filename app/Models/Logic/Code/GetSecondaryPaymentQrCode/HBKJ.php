<?php


namespace App\Models\Logic\Code\GetSecondaryPaymentQrCode;


use App\Models\Dao\OrderAssoc as OrderAssocDao;
use App\Models\Data\OrderAssoc as OrderAssocData;
use Endroid\QrCode\ErrorCorrectionLevel;
use Endroid\QrCode\QrCode;
use Illuminate\Http\JsonResponse;
use Request\HBKJ as HBKJRequest;
use Throwable;


class HBKJ extends ThirdParty
{
    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2020/6/9 5:03 下午
     */
    public function handle()
    {
        $orderInfo = OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'platform_name',
                'operator' => '=',
                'value'    => $this->name_abbreviation,
            ],
            [
                'field'    => 'self_order_id',
                'operator' => '=',
                'value'    => $this->data['order_id'],
            ],
        ], [
            'self_order_id',
            'extend',
            'id',
            'platform_order_id',
        ], true, true);
        if (!$orderInfo or !$orderInfo instanceof OrderAssocDao) {

            return responseFormat(5000024);
        }
        $redis = app('redis');
        if (!$qrCodeContent = $redis->get("verification_certificate_" . $orderInfo->self_order_id . "_" .
            $orderInfo->platform_order_id)) {
            $qrCodeContent = HBKJRequest::handle('getCheckCode', [
                'businessId' => $orderInfo->self_order_id,
                'voucher'  => $orderInfo->platform_order_id,
            ])['checkCode'];
            $redis->setex("verification_certificate_" . $orderInfo->self_order_id . "_" .
                $orderInfo->platform_order_id, 178, $qrCodeContent);
        }
        $qrCodeGen = new QrCode($qrCodeContent);
        $qrCodeGen->setSize(300);
        $qrCodeGen->setWriterByName('png');
        $qrCodeGen->setEncoding('ISO-8859-1');
        $qrCodeGen->setErrorCorrectionLevel(ErrorCorrectionLevel::HIGH());
        $qrCodeGen->setValidateResult(false);
        $qrCodeGen->setRoundBlockSize(true);
        $qrCodeGen->setMargin(10);
        $qrCodeGen->setWriterOptions(['exclude_xml_declaration' => true]);
        return responseFormat(0, [
            'certificate_type'           => "image",
            'certificate_resource'       => $qrCodeGen->writeDataUri(),
            'platform_name_abbreviation' => $this->name_abbreviation,
            'expiration'                 => $redis->ttl("verification_certificate_" .
                $orderInfo->self_order_id . "_" . $orderInfo->platform_order_id),
        ]);
    }
}
