<?php


namespace App\Models\Logic\Code\GetSecondaryPaymentQrCode;


use App\Models\Dao\OrderAssoc as OrderAssocDao;
use App\Models\Data\OrderAssoc as OrderAssocData;
use Endroid\QrCode\ErrorCorrectionLevel;
use Endroid\QrCode\QrCode;
use Illuminate\Http\JsonResponse;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Request\SHSX as SHSXRequest;
use Throwable;


class SHSX extends ThirdParty
{
    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/6/9 5:03 下午
     */
    public function handle()
    {
        $orderInfo = OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'platform_name',
                'operator' => '=',
                'value'    => $this->name_abbreviation,
            ],
            [
                'field'    => 'self_order_id',
                'operator' => '=',
                'value'    => $this->data['order_id'],
            ],
        ], [
            'self_order_id',
            'extend',
            'id',
            'platform_order_id',
        ], true, true);
        if (!$orderInfo or !$orderInfo instanceof OrderAssocDao) {
            return responseFormat(5000024);
        }
        $orderRefundData = FOSS_ORDERRequest::handle('/api/oil_adapter/getOrderRefundInfo', [
            'order_id' => $this->data['order_id']
        ])['data'] ?? [];
        if ($orderRefundData and $orderRefundData['approve_status'] != 5) {
            return responseFormat(5000115);
        }
        $data = SHSXRequest::handle('api/channel/refreshCouponCode', [
            'trade_no' => $this->data['order_id']
        ]);
        $qrCodeGen = new QrCode($data['data']['code']);
        $qrCodeGen->setSize(300);
        $qrCodeGen->setWriterByName('png');
        $qrCodeGen->setEncoding('ISO-8859-1');
        $qrCodeGen->setErrorCorrectionLevel(ErrorCorrectionLevel::HIGH());
        $qrCodeGen->setValidateResult(false);
        $qrCodeGen->setRoundBlockSize(true);
        $qrCodeGen->setMargin(10);
        $qrCodeGen->setWriterOptions(['exclude_xml_declaration' => true]);
        return responseFormat(0, [
            'certificate_type'           => "image",
            'certificate_resource'       => $qrCodeGen->writeDataUri(),
            'platform_name_abbreviation' => $this->name_abbreviation,
            'expiration' => 180,
        ]);
    }
}
