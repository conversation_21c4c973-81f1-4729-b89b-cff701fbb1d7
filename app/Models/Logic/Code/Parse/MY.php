<?php


namespace App\Models\Logic\Code\Parse;


use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\DecodingResultEntity;
use Exception;
use Request\MY as MYRequest;

class MY extends Basic implements ThirdParty
{
    /**
     * @throws Exception
     */
    public function handle(): DecodingResultEntity
    {
        $authInfo = AuthInfoData::getAuthInfoFieldByNameAbbreviation('my');
        $requestData = [
            'qrcode_str' => $this->data['qr_code'],
            'station_id' => $this->data['gasStationId'],
        ];
        $qrCodeParseRes = MYRequest::handle("codeVerification", $requestData);
        if (!isset($qrCodeParseRes['data'])) {

            throw new Exception("", 5000011);
        }
        if (empty($qrCodeParseRes['data']['driver_phone'])) {

            throw new Exception("", 5000011);
        }

        return new DecodingResultEntity([
            "card_no"         => $authInfo['card_no'],
            "org_code"        => $authInfo['role_code'],
            "driver_name"     => '',
            "extends"         => json_encode([
                "extends"              => $qrCodeParseRes['data']['extendsStr'] ?? '',
                "is_check_fuel_amount" => false,
                "plateNumber"          => $qrCodeParseRes['data']['plate_number'] ?? '',
                'fuel_info'            => [],
                "availableAmount"      => $qrCodeParseRes['data']['balance'] ?? 0.00,
            ]),
            'driver_phone'    => $qrCodeParseRes['data']['driver_phone'],
            'truck_no'        => $qrCodeParseRes['data']['plate_number'] ?? '',
            'availableAmount' => $qrCodeParseRes['data']['balance'] ?? 0.00,
            'is_check_pwd'    => true,
            'is_self'         => false,
            'access_key'      => $authInfo['access_key'],
            'secret'          => $authInfo['secret'],
        ]);
    }
}
