<?php


namespace App\Models\Logic\Code\Parse;


use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\DecodingResultEntity;
use Exception;
use Request\ZJ as ZJRequest;

class ZJ extends Basic implements ThirdParty
{
    /**
     * @throws Exception
     */
    public function handle(): DecodingResultEntity
    {
        $authInfo = AuthInfoData::getAuthInfoFieldByNameAbbreviation('zj');
        $requestData = [
            'qrcodeStr' => $this->data['qr_code'],
            'stationId' => $this->data['gasStationId'],
            'driverId'  => array_slice(explode('_', $this->data['qr_code']), -1)[0],
        ];
        $qrCodeParseRes = ZJRequest::handle("checkQrcode", "post", $requestData);
        if (!isset($qrCodeParseRes['data'])) {

            throw new Exception("", 5000011);
        }
        if (isset($qrCodeParseRes['data']['orgCode']) and !empty($qrCodeParseRes['data']['orgCode'])) {
            $realAuthInfo = AuthInfoData::getAuthInfoByRoleCode($qrCodeParseRes['data']['orgCode'], true);
            if (empty($realAuthInfo)) {
                throw new Exception("", 4120402);
            }
            $authInfo['card_no'] = $realAuthInfo['card_no'];
            $authInfo['role_code'] = $realAuthInfo['role_code'];
        }

        return new DecodingResultEntity([
            "card_no"         => $authInfo['card_no'],
            "org_code"        => $authInfo['role_code'],
            "driver_name"     => $qrCodeParseRes['data']['driverName'] ?? '',
            "extends"         => json_encode([
                "extends"              => $qrCodeParseRes['data']['extendsInfo'] ?? '',
                "is_check_fuel_amount" => false,
                "plateNumber"          => $qrCodeParseRes['data']['plate_number'] ?? '',
                'fuel_info'            => [],
                "availableAmount"      => $qrCodeParseRes['data']['balance'] ?? 0.00,
            ]),
            'driver_phone'    => $qrCodeParseRes['data']['driverPhone'] ?? '',
            'truck_no'        => $qrCodeParseRes['data']['plateNumber'] ?? '',
            'availableAmount' => $qrCodeParseRes['data']['balance'] ?? 0.00,
            'is_check_pwd'    => true,
            'is_self'         => false,
            'access_key'      => $authInfo['access_key'],
            'secret'          => $authInfo['secret'],
        ]);
    }
}
