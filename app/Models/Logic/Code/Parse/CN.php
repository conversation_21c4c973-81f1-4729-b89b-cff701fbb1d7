<?php


namespace App\Models\Logic\Code\Parse;


use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\DecodingResultEntity;
use Exception;
use Request\CN as CNRequest;
use Request\FOSS_STATION as FOSS_STATIONRequest;
use Throwable;

class CN extends Basic implements ThirdParty
{
    /**
     * @return DecodingResultEntity
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/11/25 6:37 下午
     */
    public function handle(): DecodingResultEntity
    {
        $authInfo = AuthInfoData::getAuthInfoFieldByNameAbbreviation('cn');
        $requestData = [
            'qrcode_str' => $this->data['qr_code'],
        ];
        $qrCodeParseRes = CNRequest::handle("api.validateQrcode", $requestData);
        if (!isset($qrCodeParseRes['data'])) {

            throw new Exception("", 5000011);
        }

        $fuelAmountArr = [];
        $stationInfo = FOSS_STATIONRequest::handle("v1/station/getStationById", [
            'id' => $this->data["gasStationId"],
        ]);
        foreach ($stationInfo['data']['stationInfo']["price_list"] as $v) {

            $fuelTemp = [];
            $fuelTemp['gas_type_name'] = $v["oil_name"];
            $fuelTemp['gas_type_level'] = $v["oil_level"];
            $fuelTemp['gas_type_no'] = $v["oil_type"];
            $fuelTemp['fuelAmount'] = bcdiv($qrCodeParseRes['data']['balance'], $v["price"], 2);
            $fuelAmountArr[] = $fuelTemp;
        }

        return new DecodingResultEntity([
            "card_no"         => $authInfo['card_no'],
            "org_code"        => $authInfo['role_code'],
            "driver_name"     => '',
            "extends"         => json_encode([
                "extends"              => $qrCodeParseRes['data'],
                "is_check_fuel_amount" => true,
                "plateNumber"          => $qrCodeParseRes['data']['plate_number'],
                'fuel_info'            => $fuelAmountArr,
                "availableAmount"      => $qrCodeParseRes['data']['balance'],
            ]),
            'driver_phone'    => $qrCodeParseRes['data']['driver_phone'] ?? '',
            'truck_no'        => $qrCodeParseRes['data']['plate_number'] ?? '',
            'availableAmount' => $qrCodeParseRes['data']['balance'] ?? 0.00,
            'is_check_pwd'    => true,
            'is_self'         => false,
            'access_key'      => $authInfo['access_key'],
            'secret'          => $authInfo['secret'],
        ]);
    }
}
