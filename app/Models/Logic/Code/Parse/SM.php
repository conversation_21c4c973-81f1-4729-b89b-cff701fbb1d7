<?php


namespace App\Models\Logic\Code\Parse;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\DecodingResultEntity;
use Exception;
use Request\FOSS_STATION as FOSS_STATIONRequest;
use Request\SM as SMRequest;
use Throwable;

class SM extends Basic implements ThirdParty
{
    /**
     * @return DecodingResultEntity
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/11/25 6:34 下午
     */
    public function handle(): DecodingResultEntity
    {
        $requestData = [
            'qrcode_str' => $this->data['qr_code'],
        ];
        $qrCodeParseRes = SMRequest::handle("verificationReCode.do", $requestData);
        $stationInfo = FOSS_STATIONRequest::handle("v1/station/getStationById", [
            'id' => $this->data["gasStationId"],
        ]);
        $carrierToCardMapping = json_decode(AuthConfigData::getAuthConfigValByName(
            "SM_CARRIER_TO_CARD_MAPPING"), true);

        if (!$carrierToCardMapping) {

            throw new Exception("", 5000011);
        }

        if (!isset($qrCodeParseRes['data']['data'])) {

            throw new Exception("", 5000011);
        }

        if (!isset($carrierToCardMapping[$qrCodeParseRes['data']['data']['Carrier']])) {

            throw new Exception("", 5000011);
        }
        $authInfo = AuthInfoData::getAuthInfoFieldByNameAbbreviation(
            "sm_{$carrierToCardMapping[$qrCodeParseRes['data']['data']['Carrier']]}");
        if (empty($authInfo)) {

            throw new Exception("", 5000011);
        }

        return new DecodingResultEntity([
            "card_no"         => $authInfo['card_no'],
            "org_code"        => $authInfo['role_code'],
            "extends"         => json_encode([
                "data"                 => json_encode($qrCodeParseRes['data'] ?? '', 256),
                "is_check_fuel_amount" => false,
                "stationName"          => $stationInfo['data']['stationInfo']['station_name'],
            ]),
            "driver_name"     => '',
            'driver_phone'    => $qrCodeParseRes['data']['driver_phone'] ?? '',
            'truck_no'        => $qrCodeParseRes['data']['plate_number'] ?? '',
            'availableAmount' => '',
            'is_check_pwd'    => true,
            'is_self'         => false,
            'availableLiters' => [],
            'access_key'      => $authInfo['access_key'],
            'secret'          => $authInfo['secret'],
        ]);
    }
}
