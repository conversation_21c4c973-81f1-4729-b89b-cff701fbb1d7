<?php


namespace App\Models\Logic\Code\Parse;


use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\DecodingResultEntity;
use Exception;
use Request\BBSP as BBSPRequest;
use Request\FOSS_STATION as FOSS_STATIONRequest;
use Throwable;

class BBSP extends Basic implements ThirdParty
{
    /**
     * @return DecodingResultEntity
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/11/25 6:38 下午
     */
    public function handle(): DecodingResultEntity
    {
        $authInfo = AuthInfoData::getAuthInfoFieldByNameAbbreviation('bbsp');
        $stationQueryRes = FOSS_STATIONRequest::handle("v1/station/getStationById", [
            'id' => $this->data["gasStationId"],
        ]);
        if (!isset($stationQueryRes['data']['stationInfo'])) {

            throw new Exception("", 5000001);
        }

        $requestData = [
            'qr_code'    => $this->data['qr_code'],
            'station_id' => $this->data['gasStationId'],
        ];
        $qrCodeParseRes = BBSPRequest::handle("QRCode", $requestData);
        if (!isset($qrCodeParseRes['data'])) {

            throw new Exception("", 5000011);
        }

        $oilData = $stationQueryRes['data']['stationInfo'];
        $fuelInfo = [];
        foreach ($oilData["price_list"] as $v) {

            if ($v['price'] == 0) {

                continue;
            }

            $fuelInfoTemp = [];
            $fuelInfoTemp['gas_type_no'] = $v['oil_type'];
            $fuelInfoTemp['gas_type_name'] = $v['oil_name'];
            $fuelInfoTemp['gas_type_level'] = $v['oil_level'];
            $fuelInfoTemp['price'] = $v['price'];
            $fuelInfoTemp['fuelAmount'] = round(bcdiv($qrCodeParseRes['data']['availableAmount'], $v['price'], 3), 2);
            $fuelInfo[] = $fuelInfoTemp;
        }

        return new DecodingResultEntity([
            "card_no"         => $authInfo['card_no'],
            "org_code"        => $authInfo['role_code'],
            "extends"         => json_encode([
                "fuel_info"            => $fuelInfo,
                "user_id"              => $qrCodeParseRes['data']["user_id"] ?? '',
                "is_check_fuel_amount" => true,
                "availableAmount"      => $qrCodeParseRes['data']["availableAmount"] ?? '',
                "mobile"               => $qrCodeParseRes['data']["mobile"] ?? '',
            ]),
            "driver_name"     => '',
            'driver_phone'    => $qrCodeParseRes['data']["mobile"] ?? '',
            'truck_no'        => '',
            'availableAmount' => $qrCodeParseRes['data']["availableAmount"] ?? '',
            'is_check_pwd'    => true,
            'is_self'         => false,
            'access_key'      => $authInfo['access_key'],
            'secret'          => $authInfo['secret'],
        ]);
    }
}
