<?php


namespace App\Models\Logic\Code\Parse;


use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\DecodingResultEntity;
use Exception;
use Request\DESP as DESPRequest;

class DESP2H8BBD extends Basic implements ThirdParty
{
    /**
     * @throws Exception
     */
    public function handle(): DecodingResultEntity
    {
        $requestData = [
            'qr_code'    => $this->data['qr_code'],
            'station_id' => $this->data['gasStationId'],
        ];
        $qrCodeParseRes = DESPRequest::handle("open_api/v1/order/qrcode/parse", $requestData, explode('_',
            $this->data['qr_code'])[1]);
        if (!isset($qrCodeParseRes['data'])) {

            throw new Exception("", 5000011);
        }
        if (empty($qrCodeParseRes['data']['driver_tel'])) {

            throw new Exception("", 5000011);
        }
        $authInfo = AuthInfoData::getAuthInfoFieldByNameAbbreviation("desp|" . explode('_',
                $this->data['qr_code'])[1]);
        return new DecodingResultEntity([
            "card_no"         => $authInfo['card_no'],
            "org_code"        => $authInfo['role_code'],
            "driver_name"     => '',
            "extends"         => json_encode([
                "extends"              => json_encode($qrCodeParseRes['data']),
                "is_check_fuel_amount" => false,
                "plateNumber"          => $qrCodeParseRes['data']['truck_no'] ?? '',
                'fuel_info'            => [],
                "availableAmount"      => $qrCodeParseRes['data']['balance'] ?? 0.00,
            ]),
            'driver_phone'    => $qrCodeParseRes['data']['driver_tel'],
            'truck_no'        => $qrCodeParseRes['data']['truck_no'] ?? '',
            'availableAmount' => bcdiv($qrCodeParseRes['data']['balance'], 100, 2) ?? 0.00,
            'is_check_pwd'    => true,
            'is_self'         => false,
            'access_key'      => $authInfo['access_key'],
            'secret'          => $authInfo['secret'],
        ]);
    }
}
