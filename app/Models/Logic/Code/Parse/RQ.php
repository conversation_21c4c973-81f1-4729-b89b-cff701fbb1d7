<?php


namespace App\Models\Logic\Code\Parse;


use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\DecodingResultEntity;
use Exception;
use Request\RQ as RQRequest;

class RQ extends Basic implements ThirdParty
{
    /**
     * @throws Exception
     */
    public function handle(): DecodingResultEntity
    {
        $authInfo = AuthInfoData::getAuthInfoFieldByNameAbbreviation('rq');
        $requestData = [
            'authCode'    => $this->data['qr_code'],
            'stationCode' => $this->data['gasStationId'],
        ];
        $qrCodeParseRes = RQRequest::handle("rokin.partner.oil.driver", $requestData);
        if (!isset($qrCodeParseRes['result'])) {

            throw new Exception("", 5000011);
        }
        if (empty($qrCodeParseRes['result']['phone'])) {

            throw new Exception("", 5000011);
        }
        return new DecodingResultEntity([
            "card_no"         => $authInfo['card_no'],
            "org_code"        => $authInfo['role_code'],
            "driver_name"     => '',
            "extends"         => json_encode([
                "extends"              => $qrCodeParseRes['result'],
                "is_check_fuel_amount" => false,
                "plateNumber"          => $qrCodeParseRes['result']['driverName'] ?? '',
                'fuel_info'            => [],
                "availableAmount"      => 0.00,
            ]),
            'driver_phone'    => $qrCodeParseRes['result']['phone'],
            'truck_no'        => $qrCodeParseRes['result']['licensePlate'] ?? '',
            'availableAmount' => 0.00,
            'is_check_pwd'    => true,
            'is_self'         => false,
            'access_key'      => $authInfo['access_key'],
            'secret'          => $authInfo['secret'],
        ]);
    }
}
