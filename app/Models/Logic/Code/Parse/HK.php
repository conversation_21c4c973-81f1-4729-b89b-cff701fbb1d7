<?php


namespace App\Models\Logic\Code\Parse;


use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\DecodingResultEntity;
use Exception;
use Request\HK as HKRequest;

class HK extends Basic implements ThirdParty
{
    /**
     * @throws Exception
     */
    public function handle(): DecodingResultEntity
    {
        $authInfo = AuthInfoData::getAuthInfoFieldByNameAbbreviation('hk');
        $requestData = [
            'qrcode_str' => $this->data['qr_code'],
            'station_id' => $this->data['gasStationId'],
        ];
        $qrCodeParseRes = HKRequest::handle("/g7/order/check.action", $requestData);
        if (!isset($qrCodeParseRes['data'])) {
            throw new Exception("", 5000011);
        }
        if (empty($qrCodeParseRes['data']['driver_phone'])) {
            throw new Exception("", 5000011);
        }
        if (isset($qrCodeParseRes['data']['org_code']) and !empty($qrCodeParseRes['data']['org_code'])) {
            $realAuthInfo = AuthInfoData::getAuthInfoByRoleCode($qrCodeParseRes['data']['org_code'], true);
            if (empty($realAuthInfo)) {
                throw new Exception("", 4120402);
            }
            $authInfo['card_no'] = $realAuthInfo['card_no'];
            $authInfo['role_code'] = $realAuthInfo['role_code'];
        }
        return new DecodingResultEntity([
            "card_no"         => $authInfo['card_no'],
            "org_code"        => $authInfo['role_code'],
            "driver_name"     => $qrCodeParseRes['data']['driver_name'],
            "extends"         => json_encode([
                "extends"              => $qrCodeParseRes['data']['extends'] ?? '',
                "is_check_fuel_amount" => false,
                "plateNumber"          => isset($qrCodeParseRes['data']['plate_number']) ? (
                $qrCodeParseRes['data']['plate_number'] == 'null' ? '' :
                    $qrCodeParseRes['data']['plate_number']) : '',
                'fuel_info'            => [],
                "availableAmount"      => $qrCodeParseRes['data']['balance'] ?? 0.00,
            ]),
            'driver_phone'    => $qrCodeParseRes['data']['driver_phone'],
            'truck_no'        => isset($qrCodeParseRes['data']['plate_number']) ? (
            $qrCodeParseRes['data']['plate_number'] == 'null' ? '' :
                $qrCodeParseRes['data']['plate_number']) : '',
            'availableAmount' => $qrCodeParseRes['data']['balance'] ?? 0.00,
            'is_check_pwd'    => true,
            'is_self'         => false,
            'access_key'      => $authInfo['access_key'],
            'secret'          => $authInfo['secret'],
        ]);
    }
}
