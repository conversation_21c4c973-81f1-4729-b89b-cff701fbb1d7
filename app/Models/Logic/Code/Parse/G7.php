<?php


namespace App\Models\Logic\Code\Parse;

use App\Models\Data\DecodingResultEntity;
use Exception;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Throwable;


class G7 extends Basic implements ThirdParty
{
    /**
     * @return DecodingResultEntity
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2021/2/4 10:49 上午
     */
    public function handle(): DecodingResultEntity
    {
        $cardData = FOSS_ORDERRequest::handle('/api/qrCode/v1/parseForOa', [
            'qr_code'    => $this->data['qr_code'],
            'station_id' => $this->data['gasStationId'],
        ]);
        if (isset($this->data["actual_amount"]) and $cardData["data"]["balance"] < $this->data["actual_amount"]) {
            throw new Exception("", 5000009);
        }

        $fuelAmountInfo = [];
        foreach ($cardData['data']['oil_num'] as $v) {

            $v['oilTypeId'] = $v['oil_type'];
            $v['oilLevelId'] = $v['oil_level'];
            $v['oilNameId'] = $v['oil_name'];
            if (!empty($v['oil_type'])) {

                $v['oil_type'] = config("oil.oil_no_simple.{$v['oil_type']}");
            } else {

                $v['oil_type'] = '';
            }
            if (!empty($v['oil_level'])) {

                $v['oil_level'] = array_flip(config("oil.oil_level"))[$v['oil_level']];
            } else {

                $v['oil_level'] = '';
            }
            $v['oil_name'] = array_flip(config("oil.oil_type"))[$v['oil_name']];
            $fuelAmountInfo[] = $v;
        }

        return new DecodingResultEntity([
            "card_no"         => $cardData['data']['card_no'],
            "org_code"        => $cardData['data']['org_code'] ?? '',
            "driver_name"     => $cardData['data']['driver_name'],
            "extends"         => json_encode([
                'order_no' => $cardData['data']['order_no'] ?? '',
            ]),
            'driver_phone'    => $cardData['data']['driver_tel'],
            'truck_no'        => $cardData['data']['truck_no'],
            'availableAmount' => $cardData['data']['balance'],
            'is_check_pwd'    => $cardData['data']['is_check_pwd'],
            'is_self'         => true,
            'availableLiters' => $fuelAmountInfo,
            'order_no'        => $cardData['data']['order_no'] ?? '',
            'qr_code_source'  => $cardData['data']['qr_code_source'] ?? '',
        ]);
    }
}
