<?php


namespace App\Models\Logic\Account\Change;


use App\Models\Logic\Base;
use Exception;
use Illuminate\Http\JsonResponse;
use Throwable;


class Main extends Base
{
    private $workerMapping = [
        'kl' => KL::class,
    ];

    /**
     * @var ThirdParty
     */
    private $worker;

    /**
     * Main constructor.
     * @param array $parameter
     * @throws Throwable
     */
    public function __construct(array $parameter)
    {
        parent::__construct($parameter);

        if (!isset($this->workerMapping, $this->name_abbreviation)) {

            throw new Exception("", 4030004);
        }

        $this->worker = new $this->workerMapping[$this->name_abbreviation]($this->data);
    }

    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/8/17 4:20 下午
     */
    public function handle()
    {
        return $this->worker->handle();
    }
}
