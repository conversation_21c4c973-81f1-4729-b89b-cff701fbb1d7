<?php


namespace App\Models\Logic\Account\Query;


use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\CompanyMapping as CompanyMappingData;
use Illuminate\Http\JsonResponse;
use Request\FOSS as FOSSRequest;
use Throwable;


class YGY extends ThirdParty
{
    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/8/17 4:12 下午
     */
    public function handle(): JsonResponse
    {
        $companyInfo = CompanyMappingData::getCompanyByWhere([
            [
                'field'    => 'platform_company_no',
                'operator' => '=',
                'value'    => $this->data['companyId'],
            ],
            [
                'field'    => 'platform_name',
                'operator' => '=',
                'value'    => $this->name_abbreviation,
            ],
        ], ['company_no'], true, true);
        if (empty($companyInfo)) {

            return responseFormatForYgy(5000119);
        }
        $authInfo = AuthInfoData::getAuthInfoByRoleCode($companyInfo->company_no, true);
        if (empty($authInfo)) {

            return responseFormatForYgy(5000119);
        }
        $data = FOSSRequest::handle('gas.org_account.account_balance', [
            'vice_no'       => $authInfo['card_no'],
            'orgcode'       => $companyInfo->company_no,
            'isCardBalance' => 2,
            'accountStatus' => 2,
        ]);
        return responseFormatForYgy(0, [
            'cardNo'    => $companyInfo->company_no,
            'amount'    => bcmul($data['data']['use_balance'], 100, 2),
            'status'    => $data['data']['status'],
            'companyId' => $this->data['companyId'],
        ]);
    }
}
