<?php


namespace App\Models\Logic\QueryOrderToThirdParty;

use App\Jobs\QueryOrderToThirdParty;
use App\Models\Dao\OrderPushRecord;
use App\Models\Data\Log\QueueLog;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Data\Push\TradeResult\FY as FYTradeResult;
use App\Models\Logic\Trade\Pay\Simple as TradePaySimpleLogic;
use Exception;
use Illuminate\Support\Facades\Queue;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Request\FY as FYRequest;
use Throwable;

class FY extends Base implements Basic
{
    /**
     * @return void
     * @throws Exception|Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/9/26 8:11 下午
     */
    public function handle()
    {
        $orderInfo = OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'self_order_id',
                'operator' => '=',
                'value'    => $this->args['self_order_id'],
            ],
            [
                'field'    => 'platform_name',
                'operator' => '=',
                'value'    => $this->args['name_abbreviation'],
            ],
        ], ['extend', 'id', 'self_order_id'], true, true);
        $extend = json_decode($orderInfo->extend, true);
        if (bccomp(bcsub(time(), $extend['_create_order_success_time'], 2), 15, 2) >= 0) {
            return;
        }
        try {
            $payResult = TradePaySimpleLogic::getPayResult($orderInfo->self_order_id, $extend);
            if ($payResult->show_verification_certificate == 2 and empty($payResult->certificate_resource)) {
                Queue::later(
                    1,
                    (new QueryOrderToThirdParty([
                        'self_order_id'     => $this->args['self_order_id'],
                        'name_abbreviation' => $this->args['name_abbreviation']
                    ])),
                    '',
                    "adapter_deal_trade"
                );
                return;
            }
        } catch (Throwable $exception) {
            Queue::later(
                1,
                (new QueryOrderToThirdParty([
                    'self_order_id'     => $this->args['self_order_id'],
                    'name_abbreviation' => $this->args['name_abbreviation']
                ])),
                '',
                "adapter_deal_trade"
            );
            QueueLog::handle("trade result callback failed", [
                'data'      => $this->args,
                'exception' => $exception,
            ], "福佑", "trade_result_callback", "error");
            return;
        }
        try {
            $result = FYRequest::handle("sendTradeInfoResult", [
                "statusCode"                  => 0,
                "statusDesc"                  => "成功",
                "tradeId"                     => $extend['trade_callback_info']['tradeId'],
                "orderId"                     => $extend['trade_callback_info']['orderId'],
                "cashAmount"                  => $extend['trade_callback_info']['cashAmount'],
                "returnAmount"                => $extend['trade_callback_info']['returnAmount'],
                "aliAmount"                   => $extend['trade_callback_info']['aliAmount'],
                "aliAccount"                  => $extend['trade_callback_info']['aliAccount'],
                "supplierAccountName"         => $extend['trade_callback_info']['supplierAccountName'],
                "accountBalance"              => $extend['trade_callback_info']['accountBalance'],
                "showVerificationCertificate" => FYTradeResult::getShowVerificationCertificateMapping(
                )[$payResult->show_verification_certificate],
                "certificateType"             => $payResult->certificate_type,
                "certificateResource"         => $payResult->certificate_resource,
            ], 'post');
            (new OrderPushRecord())->insert([
                "self_order_id"              => $extend['trade_callback_info']['orderId'],
                "platform_order_id"          => $extend['trade_callback_info']['tradeId'],
                "platform_name_abbreviation" => $this->args['name_abbreviation'],
                "push_status"                => 1,
                "push_data"                  => json_encode(array_merge([
                    "statusCode"                  => 0,
                    "statusDesc"                  => "成功",
                    "showVerificationCertificate" => FYTradeResult::getShowVerificationCertificateMapping(
                    )[$payResult->show_verification_certificate],
                    "certificateType"             => $payResult->certificate_type,
                    "certificateResource"         => $payResult->certificate_resource,
                ], $extend['trade_callback_info'])),
                "push_result"                => json_encode($result),
                "self_order_status"          => 1,
                "push_time"                  => date('Y-m-d H:i:s'),
            ]);
        } catch (Throwable $exception) {
            (new OrderPushRecord())->insert([
                "self_order_id"              => $extend['trade_callback_info']['orderId'],
                "platform_order_id"          => $extend['trade_callback_info']['tradeId'],
                "platform_name_abbreviation" => $this->args['name_abbreviation'],
                "push_status"                => 2,
                "push_data"                  => json_encode(array_merge([
                    "statusCode"                  => 0,
                    "statusDesc"                  => "成功",
                    "showVerificationCertificate" => FYTradeResult::getShowVerificationCertificateMapping(
                    )[$payResult->show_verification_certificate],
                    "certificateType"             => $payResult->certificate_type,
                    "certificateResource"         => $payResult->certificate_resource,
                ], $extend['trade_callback_info'])),
                "push_result"                => $exception->getMessage(),
                "self_order_status"          => 1,
                "push_time"                  => date('Y-m-d H:i:s'),
            ]);
            if ($exception->getCode() != 5000999) {
                throw $exception;
            }
        }
    }
}
