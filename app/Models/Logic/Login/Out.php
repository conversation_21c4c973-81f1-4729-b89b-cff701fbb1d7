<?php
/**
 * Created by PhpStorm.
 * User: zdl
 * Date: 2019-01-18
 * Time: 14:13
 */

namespace App\Models\Logic\Login;


use App\Models\Logic\Base;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Session;
use <PERSON><PERSON>\Lumen\Http\Redirector;
use Throwable;

class Out extends Base
{
    /**
     * @return JsonResponse|RedirectResponse|Redirector
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-05 15:49
     */
    public function handle()
    {
        if (isset($this->data['token'])) {

            app('redis')->del($this->data['token']);
            return responseFormat();
        }

        Session::remove("userId");
        Session::save();
        return redirect(getLaravelAndMachineEnv("APP__URL") . "/web/login/index");
    }
}
