<?php


namespace App\Models\Logic\RolePermission;


use App\Models\Data\RolePermission as RolePermissionData;
use App\Models\Logic\Base;
use Exception;
use Throwable;

class Info extends Base
{
    public function handle()
    {
    }

    public function getData()
    {
        $this->filterEmptyValue($this->data);
        return RolePermissionData::getData($this->data);
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/6/5 4:25 下午
     */
    public function create()
    {
        RolePermissionData::create($this->data);
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/6/5 4:25 下午
     */
    public function update()
    {
        RolePermissionData::update($this->data);
    }

    /**
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-24 20:51
     */
    public function delete()
    {
        RolePermissionData::delete($this->data);
    }
}
