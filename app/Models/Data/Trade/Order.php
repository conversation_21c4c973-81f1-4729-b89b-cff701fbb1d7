<?php
/**
 * Created by PhpStorm.
 * User: zdl
 * Date: 2019-01-16
 * Time: 21:09
 */

namespace App\Models\Data\Trade;

use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Base;

class Order extends Base
{
    public static    $data               = [];
    protected static $orderPersistFields = [
        'sg'      => [
            'id',
            'card_no',
            'station_id',
            'pay_status',
            'truck_no',
            'price',
            'money',
            'oil_level',
            'oil_num',
            'createtime',
            'oil_no',
            'order_id',
            'oil_type',
            'gpsno',
            'oil_converge',
            'real_total_amount',
            'status',
            'pay_date',
            'out_partner_id',
            'out_org_id',
            'out_order_no',
            'buyer_logon_id',
        ],
        'dmd'     => [
            'id',
            'card_no',
            'station_id',
            'pay_status',
            'truck_no',
            'price',
            'money',
            'oil_level',
            'oil_num',
            'createtime',
            'oil_no',
            'order_id',
            'gun_name',
            'station_name',
            'oil_type',
            'gpsno',
        ],
        'default' => [
            'id',
            'card_no',
            'station_id',
            'pay_status',
            'truck_no',
            'price',
            'money',
            'oil_level',
            'oil_num',
            'createtime',
            'oil_no',
            'order_id',
            'gun_name',
            'station_name',
            'oil_type',
            'gpsno',
        ],
    ];
    protected static $payStatusMapping   = [
        'sg' => [
            1 => 'PAYING',
            2 => 'SUCCESS',
        ],
    ];

    public static function handle(string $source = 'default', array $initData = []): array
    {
        self::$data = $initData;
        self::$source();
        return self::$data;
    }

    public static function sg()
    {
        self::$data['oil_no'] = array_flip(config("oil.oil_no_int"))[array_flip(config("oil.oil_no"))
        [self::$data['oil_type']]];
        self::$data['oil_type'] = array_flip(config("oil.oil_type_int"))[array_flip(config("oil.oil_type"))
        [self::$data['oil_name']]] ?? 0;
        self::$data['oil_level'] = 0;
        self::$data['oil_converge'] = (array_flip(config("oil.oil_mapping")['sg'])[self::$data['oil_no'] . '_' .
                                                                                   self::$data['oil_type'] . '_0']) ?? -1;
        self::$data['real_total_amount'] = self::$data['oil_money'];
        self::$data['status'] = self::$payStatusMapping['sg'][self::$data['order_status']];
        self::$data['pay_date'] = date('YmdHis', strtotime(self::$data['oil_time']));
        self::$data['out_partner_id'] = AuthConfigData::getAuthConfigValByName('SG_PARTNER_ID');
        self::$data['out_org_id'] = AuthConfigData::getAuthConfigValByName('SG_ORG_ID');
        self::$data['out_order_no'] = self::$data['order_id'];
        self::$data['buyer_logon_id'] = self::$data["card_no"];
        self::$data = array_columns([self::$data], self::$orderPersistFields['sg'])[0];
    }

    public static function default()
    {
        self::$data['oil_no'] = array_flip(config("oil.oil_no_int"))[array_flip(config("oil.oil_no"))
        [self::$data['oil_type']]];
        self::$data['oil_type'] = array_flip(config("oil.oil_type_int"))[array_flip(config("oil.oil_type"))
        [self::$data['oil_name']]] ?? 0;
        self::$data['oil_level'] = array_flip(config("oil.oil_level_int"))[array_flip(config("oil.oil_level"))
        [self::$data['oil_level']]];
        self::$data = array_columns([self::$data], self::$orderPersistFields['default'])[0];
    }
}
