<?php


namespace App\Models\Data\OilMapping;


use App\Models\Data\RegionalInfo as RegionalInfoData;
use Throwable;

class HR extends Basic
{
    /**
     * @param array $oilStationData
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhu<PERSON><PERSON>@zhongqixing.com>
     * @since 2020/6/5 4:37 下午
     */
    public static function getOilStationData(array &$oilStationData)
    {
        self::replaceKey($oilStationData, [
            'push_stop'    => 'is_stop',
            'provice_code' => 'province_code',
        ]);
        self::filterField($oilStationData, [
            'pcode',
            'station_code',
            'tag_list',
            'role_code',
            'fixed_face_value_transaction',
            'station_transaction_desc',
            'station_brand',
            'payment_certificate_type',
            'business_hours',
        ]);
        foreach ($oilStationData["price_list"] as $k => &$v) {
            if (!isset(config('oil.oil_mapping.hr')["{$v['oil_name']}_{$v['oil_type']}_{$v['oil_level']}"])) {
                unset($oilStationData["price_list"][$k]);
                continue;
            }
            $v['oil_code'] = (int)config("oil.oil_mapping.hr.{$v['oil_name']}_{$v['oil_type']}_{$v['oil_level']}");
            self::filterField($v, [
                'oil_title_name',
                'oil_type',
                'oil_name',
                'ndrc_price',
                'coupon_list',
                'discount_rate',
            ]);
            self::replaceKey($v, [
                'oil_type_val'  => 'oil_type',
                'oil_name_val'  => 'oil_name',
                'oil_level_val' => 'oil_level',
            ]);
        }
        $oilStationData['price_list'] = array_values($oilStationData['price_list']);
        if (empty($oilStationData['price_list'])) {
            $oilStationData['is_stop'] = 1;
        }
        if (self::$dbActionInner) {
            $regionalMapping = RegionalInfoData::getNameByCode(
                [$oilStationData['province_code'], $oilStationData['city_code']]
            );
            $oilStationData['province_name'] = $regionalMapping[$oilStationData['province_code']];
            $oilStationData['city_name'] = $regionalMapping[$oilStationData['city_code']];
        }
        $oilStationData['province_code'] = $oilStationData['init_provice_code'];
        $oilStationData['city_code'] = $oilStationData['init_city_code'];
    }
}
