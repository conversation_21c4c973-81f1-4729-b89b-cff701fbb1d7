<?php


namespace App\Models\Data\OilMapping;


use App\Models\Data\RegionalInfo as RegionalInfoData;
use Throwable;


class JDWC extends Basic
{
    /**
     * @param array $oilStationData
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2020/6/5 4:31 下午
     */
    public static function getOilStationData(array &$oilStationData)
    {
        self::replaceKey($oilStationData, [
            'init_provice_code'  => 'provinceCode',
            'init_city_code'     => 'cityCode',
            'station_name'       => 'name',
            'contact_phone'      => 'contacterPhoneNo',
            'lng'                => 'longitude',
            'lat'                => 'latitude',
            'dataOrigin'         => '1',
            'is_highway'         => 'isHighway',
            'push_stop'          => 'isStop',
            'supplier_name'      => 'supplierName',
            'station_type'       => 'type',
            'station_brand_name' => 'brandName',
            'rebate_grade'       => 'rebateGrade',
            'station_event_tag'  => 'eventTag',
            'station_line_tag'   => 'lineTag',
            'price_list'         => 'oilQualityList',
            'id'                 => 'stationId',
        ]);
        self::filterField($oilStationData, [
            'pcode',
            'station_code',
            'is_highway',
            'tag_list',
            'station_brand',
            'province_name',
            'city_name',
            'role_code',
            'fixed_face_value_transaction',
            'station_transaction_desc',
            'payment_certificate_type',
            'business_hours',
        ]);

        foreach ($oilStationData["oilQualityList"] as $k => &$v) {

            if ($v['oil_name_val'] != '柴油') {

                unset($oilStationData['price_list'][$k]);
            }

            $v['station_name'] = $oilStationData['name'];
            $v['oilOrigin'] = '1';
            $v['dataOrigin'] = '1';
            $v['salePrice'] = (float)$v['price'];
            $v['supplierName'] = $oilStationData['supplierName'];
            self::replaceKey($v, [
                'oil_type_val'  => 'oilType',
                'oil_name_val'  => 'oilName',
                'oil_level_val' => 'oilLevel',
                'starttime'     => 'startTime',
                'endtime'       => 'endTime',
                'price'         => 'salePrice',
            ]);
            self::filterField($v, [
                'oil_title_name',
                'oil_type',
                'oil_level',
                'oil_name',
                'ndrc_price',
                'coupon_list',
                'discount_rate',
            ]);
        }

        $oilStationData['oilQualityList'] = array_values($oilStationData['oilQualityList']);
        $oilStationData['appId'] = 'G7';
        $oilStationData['serverId'] = 'G7';
        $oilStationData['lineTag'] = implode('|', $oilStationData['lineTag']);
        $oilStationData['dataOrigin'] = '1';
        $oilStationData['provinceCode'] = (int)$oilStationData['provinceCode'];
        $oilStationData['cityCode'] = (int)$oilStationData['cityCode'];
        $oilStationData['isHighway'] = (int)$oilStationData['isHighway'];
        $oilStationData['isStop'] = (int)$oilStationData['isStop'];
        $oilStationData['type'] = (int)$oilStationData['type'];

        if (self::$dbActionInner) {

            $regionalMapping = RegionalInfoData::getNameByCode([$oilStationData['provice_code'], $oilStationData['city_code']]);
            $oilStationData['provinceName'] = $regionalMapping[$oilStationData['provice_code']];
            $oilStationData['cityName'] = $regionalMapping[$oilStationData['city_code']];
        }

        self::filterField($oilStationData, [
            'provice_code',
            'city_code',
        ]);
    }
}
