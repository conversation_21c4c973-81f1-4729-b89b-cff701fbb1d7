<?php


namespace App\Models\Data\OilMapping;


class LF extends Basic
{
    public static function getOilStationData(array &$oilStationData)
    {
        self::replaceKey($oilStationData, [
            'push_stop'    => 'is_stop',
            'provice_code' => 'province_code',
        ]);
        self::filterField($oilStationData, [
            'pcode',
            'station_code',
            'tag_list',
            'fixed_face_value_transaction',
            'station_transaction_desc',
            'station_brand',
            'payment_certificate_type',
            'business_hours',
        ]);
        $oilStationData['province_code'] = substr(
            $oilStationData['province_code'],
            0,
            strlen($oilStationData['province_code']) - 6
        );
        $oilStationData['city_code'] = substr(
            $oilStationData['city_code'],
            0,
            strlen($oilStationData['city_code']) - 6
        );

        foreach ($oilStationData["price_list"] as $k => &$v) {
            if (strpos($v['oil_name_val'], '天然气') !== false or
                strpos($v['oil_name_val'], '尿素') !== false) {
                unset($oilStationData["price_list"][$k]);
                continue;
            }

            self::filterField($v, [
                'oil_title_name',
                'oil_type',
                'oil_name',
                'ndrc_price',
                'coupon_list',
                'discount_rate',
            ]);
            self::replaceKey($v, [
                'oil_type_val'  => 'oil_type',
                'oil_name_val'  => 'oil_name',
                'oil_level_val' => 'oil_level',
            ]);
        }

        $oilStationData["price_list"] = array_values($oilStationData["price_list"]);
    }
}
