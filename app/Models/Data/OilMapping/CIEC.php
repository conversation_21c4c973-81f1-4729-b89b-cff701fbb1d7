<?php


namespace App\Models\Data\OilMapping;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\RegionalInfo as RegionalInfoData;
use Throwable;

class CIEC extends Basic
{
    /**
     * @param array $oilStationData
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/6/5 4:37 下午
     */
    public static function getOilStationData(array &$oilStationData)
    {
        $oilStationData['station_img'] = AuthConfigData::getAuthConfigValByName("STATION_DEFAULT_LOGO");
        self::replaceKey($oilStationData, [
            'push_stop'    => 'is_stop',
            'provice_code' => 'province_code',
        ]);
        self::filterField($oilStationData, [
            'pcode',
            'station_code',
            'tag_list',
            'role_code',
            'station_line_tag',
            'station_event_tag',
            'fixed_face_value_transaction',
            'station_transaction_desc',
            'station_brand',
            'payment_certificate_type',
            'business_hours',
        ]);
        foreach ($oilStationData["price_list"] as &$v) {
            self::filterField($v, [
                'oil_title_name',
                'oil_type',
                'oil_name',
                'starttime',
                'endtime',
                'coupon_list',
                'discount_rate',
            ]);
            self::replaceKey($v, [
                'oil_type_val'  => 'oil_type',
                'oil_name_val'  => 'oil_name',
                'oil_level_val' => 'oil_level',
                'ndrc_price'    => 'base_price',
            ]);
        }
        $oilStationData['price_list'] = array_values($oilStationData['price_list']);
        if (empty($oilStationData['price_list'])) {
            $oilStationData['is_stop'] = 1;
        }
        if (self::$dbActionInner) {
            $regionalMapping = RegionalInfoData::getNameByCode(
                [$oilStationData['province_code'], $oilStationData['city_code']]
            );
            $oilStationData['province_name'] = $regionalMapping[$oilStationData['province_code']];
            $oilStationData['city_name'] = $regionalMapping[$oilStationData['city_code']];
        }
        $oilStationData['province_code'] = substr(
            $oilStationData['province_code'],
            0,
            strlen($oilStationData['province_code']) - 6
        );
        $oilStationData['city_code'] = substr(
            $oilStationData['city_code'],
            0,
            strlen($oilStationData['city_code']) - 6
        );
    }
}
