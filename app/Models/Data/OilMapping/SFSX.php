<?php
// 顺象

namespace App\Models\Data\OilMapping;


use App\Models\Data\RegionalInfo as RegionalInfoData;
use Throwable;

class SFSX extends Basic
{
    /**
     * @param array $oilStationData
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/6/5 4:37 下午
     */
    public static function getOilStationData(array &$oilStationData)
    {
        self::replaceKey($oilStationData, [
            'push_stop'    => 'is_stop',
            'provice_code' => 'province_code',
        ]);
        self::filterField($oilStationData, [
            'pcode',
            'station_code',
            'tag_list',
            'role_code',
            'fixed_face_value_transaction',
            'station_transaction_desc',
            'station_brand',
            'payment_certificate_type',
            'business_hours',
        ]);
        $oilStationData['price_list'] = Common::comparePriceAndDealRepeatOil($oilStationData['price_list']);
        foreach ($oilStationData["price_list"] as $k => &$v) {
            if (strpos($v['oil_name_val'], '柴油') === false) {
                unset($oilStationData["price_list"][$k]);
                continue;
            }
            self::filterField($v, [
                'oil_title_name',
                'oil_type',
                'oil_name',
                'coupon_list',
                'discount_rate',
            ]);
            self::replaceKey($v, [
                'oil_type_val'  => 'oil_type',
                'oil_name_val'  => 'oil_name',
                'oil_level_val' => 'oil_level',
            ]);
        }
        $oilStationData['price_list'] = array_values($oilStationData['price_list']);
        if (empty($oilStationData['price_list'])) {
            $oilStationData['is_stop'] = 1;
        }
        if (self::$dbActionInner) {
            $regionalMapping = RegionalInfoData::getNameByCode(
                [$oilStationData['province_code'], $oilStationData['city_code']]
            );
            $oilStationData['province_name'] = $regionalMapping[$oilStationData['province_code']];
            $oilStationData['city_name'] = $regionalMapping[$oilStationData['city_code']];
        }
        self::replaceKey($oilStationData, [
            'init_provice_code' => 'province_code',
            'init_city_code'    => 'city_code',
        ]);
    }
}
