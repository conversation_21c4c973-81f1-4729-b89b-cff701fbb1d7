<?php


namespace App\Models\Data\OilMapping;


use App\Models\Data\Common as CommonData;
use App\Models\Data\RegionalInfo as RegionalInfoData;
use App\Models\Logic\Code\GetSecondaryPaymentQrCode;
use App\Models\Logic\Data\Push\AutonomousOrder;
use Throwable;

class DESP2A6LA9 extends Basic
{
    public static $stationEnableStateMapping = [
        0 => 1,
        1 => 0,
    ];

    public static $stationTradeTypeMapping = [
        1  => 10,
        3  => 15,
        5  => 25,
        30 => 30,
        4  => 15,
    ];

    public static $stationOilUnitMapping = [
        0 => 1,
        1 => 2,
    ];

    /**
     * @param array $oilStationData
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/6/5 4:34 下午
     */
    public static function getOilStationData(array &$oilStationData)
    {
        self::replaceKey($oilStationData, [
            'push_stop'         => 'use_status',
            'provice_code'      => 'province_code',
            'station_event_tag' => 'activity',
            'oil_unit'          => 'oil_mode',
            'station_line_tag'  => 'station_tag',
            'supplier_name'     => 'slogan',
        ]);
        if (in_array($oilStationData['trade_type'], CommonData::RECEIVE_TRADE_FOR_DOWNSTREAM_BY_TRADE_ENUM)) {
            $autonomousOrderObj = new AutonomousOrder([
                'data' => [
                    'pcode' => $oilStationData['pcode'],
                ]
            ], false);
            if ($autonomousOrderObj->checkWorkerExists()) {
                $getSecondaryPaymentQrCodeObj = (new GetSecondaryPaymentQrCode([
                    'supplier_code' => $oilStationData['pcode'],
                ]));
                if ($getSecondaryPaymentQrCodeObj->checkWorkerExists()) {
                    $oilStationData['trade_type'] = 30;
                }
            }
        }
        self::filterField($oilStationData, [
            'pcode',
            'station_code',
            'tag_list',
            'fixed_face_value_transaction',
            'station_transaction_desc',
            'station_brand',
            'payment_certificate_type',
            'business_hours',
        ]);
        $oilStationData['trade_type'] = self::$stationTradeTypeMapping[$oilStationData['trade_type']];
        $oilStationData['use_status'] = self::$stationEnableStateMapping[$oilStationData['use_status']];
        foreach ($oilStationData["price_list"] as $k => &$v) {
            $v['goods_name'] = $v['oil_name_val'] . $v['oil_type_val'] . $v['oil_level_val'];
            $v['goods_code'] = config(
                "oil.oil_mapping.desp.mapping.{$v['oil_name']}_{$v['oil_type']}_{$v['oil_level']}",
                ""
            );
            if (empty($v['goods_code'])) {
                unset($oilStationData['price_list'][$k]);
                continue;
            }
            self::filterField($v, [
                'oil_title_name',
                'oil_type',
                'oil_name',
                'oil_level',
                'oil_type_val',
                'oil_name_val',
                'oil_level_val',
                'ndrc_price',
                'coupon_list',
                'discount_rate',
            ]);
            foreach ($v['gun_numbers'] as &$gv) {
                $gv = (string)$gv;
            }
            self::replaceKey($v, [
                'gun_numbers' => 'gun_number',
            ]);
        }
        $oilStationData['price_list'] = array_values($oilStationData['price_list']);
        if (self::$dbActionInner) {
            $regionalMapping = RegionalInfoData::getNameByCode(
                [$oilStationData['province_code'], $oilStationData['city_code']]
            );
            $oilStationData['province_name'] = $regionalMapping[$oilStationData['province_code']];
            $oilStationData['city_name'] = $regionalMapping[$oilStationData['city_code']];
        }
    }
}
