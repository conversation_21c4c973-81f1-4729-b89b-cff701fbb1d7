<?php


namespace App\Models\Data\OilMapping;


class Jd extends Basic
{
    public static function getOilStationData(array &$oilStationData)
    {
        self::dealOilStationDataNullValue($oilStationData);
        self::replace<PERSON>ey($oilStationData, [
            'push_stop'         => 'is_stop',
            'init_provice_code' => 'province_code',
            'init_city_code'    => 'city_code',
        ]);
        self::filterField($oilStationData, [
            'pcode',
            'station_code',
            'tag_list',
            'role_code',
            'fixed_face_value_transaction',
            'station_transaction_desc',
            'station_brand',
            'payment_certificate_type',
            'business_hours',
        ]);

        foreach ($oilStationData["price_list"] as &$v) {
            self::filterField($v, [
                'oil_title_name',
                'oil_type',
                'oil_name',
                'ndrc_price',
                'coupon_list',
                'discount_rate',
            ]);
            self::replaceKey($v, [
                'oil_type_val'  => 'oil_type',
                'oil_name_val'  => 'oil_name',
                'oil_level_val' => 'oil_level',
            ]);
            $v['price'] = (float)$v['price'];
            self::dealOilStationDataNullValue($v);
        }
    }
}
