<?php


namespace App\Models\Data\OilMapping;


use App\Models\Data\AuthConfig;
use App\Models\Data\RegionalInfo as RegionalInfoData;
use Throwable;

class CHTX extends Basic
{
    /**
     * @param array $oilStationData
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/6/5 4:37 下午
     */
    public static function getOilStationData(array &$oilStationData)
    {
        self::replaceKey($oilStationData, [
            'push_stop'    => 'is_stop',
            'provice_code' => 'province_code',
        ]);
        self::filterField($oilStationData, [
            'pcode',
            'station_code',
            'tag_list',
            'role_code',
            'fixed_face_value_transaction',
            'station_transaction_desc',
            'station_brand',
            'payment_certificate_type',
            'business_hours',
        ]);
        $specialPricingStationIdAndPrice = json_decode(
            AuthConfig::getAuthConfigValByName(
                "CHTX_SPECIAL_PRICING_STATION_AND_PRICE"
            ),
            true
        );
        foreach ($oilStationData["price_list"] as $k => &$v) {
            if ($v['oil_name_val'] != '柴油') {
                unset($oilStationData["price_list"][$k]);
                continue;
            }
            self::filterField($v, [
                'oil_title_name',
                'oil_type',
                'oil_name',
                'ndrc_price',
                'coupon_list',
                'discount_rate',
            ]);
            self::replaceKey($v, [
                'oil_type_val'  => 'oil_type',
                'oil_name_val'  => 'oil_name',
                'oil_level_val' => 'oil_level',
            ]);
            if (!empty($v['price']) and isset($specialPricingStationIdAndPrice[$oilStationData['id']]) and
                                        is_numeric($specialPricingStationIdAndPrice[$oilStationData['id']]) and
                                        bccomp(
                                            $v['price'],
                                            $specialPricingStationIdAndPrice[$oilStationData['id']]
                                        ) === 1) {
                $v['price'] = bcadd(
                    $v['price'],
                    $specialPricingStationIdAndPrice[$oilStationData['id']],
                    2
                );
            }
        }

        if (self::$dbActionInner) {
            $regionalMapping = RegionalInfoData::getNameByCode(
                [$oilStationData['province_code'], $oilStationData['city_code']]
            );
            $oilStationData['province_name'] = $regionalMapping[$oilStationData['province_code']];
            $oilStationData['city_name'] = $regionalMapping[$oilStationData['city_code']];
        }

        if (count($oilStationData['price_list']) <= 0) {
            $oilStationData['is_stop'] = 1;
        }
        $oilStationData['price_list'] = array_values($oilStationData['price_list']);
    }
}
