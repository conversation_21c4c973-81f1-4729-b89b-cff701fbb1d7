<?php


namespace App\Models\Data\OilMapping;


class Ygj extends Basic
{
    public static function getOilStationData(array &$oilStationData)
    {
        self::replaceKey($oilStationData, [
            'push_stop'         => 'is_stop',
            'init_provice_code' => 'province_code',
            'init_city_code'    => 'city_code',
        ]);
        self::filterField($oilStationData, [
            'pcode',
            'station_code',
            'is_highway',
            'role_code',
            'fixed_face_value_transaction',
            'station_transaction_desc',
            'station_brand',
            'payment_certificate_type',
            'business_hours',
        ]);
        $oilStationData['rebate_label'] = $oilStationData['rebate_grade'];

        foreach ($oilStationData["price_list"] as &$v) {
            self::filterField($v, [
                'oil_title_name',
                'oil_type',
                'oil_name',
                'ndrc_price',
                'coupon_list',
                'discount_rate',
            ]);
            self::replaceKey($v, [
                'oil_type_val'  => 'oil_type',
                'oil_name_val'  => 'oil_name',
                'oil_level_val' => 'oil_level',
            ]);
            $v['oil_level'] = (is_null($v['oil_level']) or $v['oil_level'] == 'null') ? '' : $v['oil_level'];
            $v['oil_type'] = (is_null($v['oil_type']) or $v['oil_type'] == 'null') ? '' : $v['oil_type'];
        }

        if (empty($oilStationData['price_list'])) {
            $oilStationData['is_stop'] = 1;
        }
    }
}
