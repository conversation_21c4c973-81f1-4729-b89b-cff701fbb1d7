<?php


namespace App\Models\Data\OilMapping;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\RegionalInfo as RegionalInfoData;
use Throwable;

class YGY extends Basic
{
    public static $stationTradeTypeMapping = [
        1 => 1,
        3 => 2,
        4 => 2,
    ];

    public static $stationHighWayMapping = [
        0 => 1,
        1 => 0,
    ];

    /**
     * @param array $oilStationData
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/6/5 4:37 下午
     */
    public static function getOilStationData(array &$oilStationData)
    {
        $oilStationData['openTime'] = '00:00-23:59';
        $oilStationData['threeStation'] = $oilStationData['supplier_name'] == 'G7能源' ? 0 : 1;
        $oilStationData['threeQCodeStyle'] = 'HT_YGY_';
        $oilStationData['sourceKey'] = AuthConfigData::getAuthConfigValByName("YGY_SOURCE_KEY");
        $oilStationData['trade_type'] = self::$stationTradeTypeMapping[$oilStationData['trade_type']];
        if (checkIsMunicipality($oilStationData['city_code'])) {
            $oilStationData['provice_code'] = RegionalInfoData::getParentCodeByChildren(
                $oilStationData['city_code']
            )->parent;
        }
        self::replaceKey($oilStationData, [
            'id'                 => 'stationId',
            'station_name'       => 'stationName',
            'push_stop'          => 'isStop',
            'provice_code'       => 'provinceCode',
            'city_code'          => 'cityCode',
            'is_highway'         => 'isHighSpeed',
            'trade_type'         => 'payType',
            'price_list'         => 'fuels',
            'station_brand_name' => 'brandName',
            'province_name'      => 'provinceName',
            'city_name'          => 'cityName',
            'oil_unit'           => 'deductionMode',
        ]);
        $oilStationData['isHighSpeed'] = self::$stationHighWayMapping[$oilStationData['isHighSpeed']];
        $oilStationData['highSpeedInfo'] = json_encode($oilStationData['station_line_tag'] ?? [], 320);
        self::filterField($oilStationData, [
            'pcode',
            'station_code',
            'tag_list',
            'role_code',
            'station_brand',
            'contact',
            'contact_phone',
            'rebate_grade',
            'station_line_tag',
            'station_event_tag',
            'station_type',
            'supplier_name',
            'order_need_gun',
            'fixed_face_value_transaction',
            'station_transaction_desc',
            'payment_certificate_type',
            'business_hours',
        ]);
        $oilStationData['gunNumber'] = [];
        self::getAvailableOil($oilStationData["fuels"]);
        $oilConfig = config("oil.oil_mapping.ygy");
        foreach ($oilStationData["fuels"] as $k => &$v) {
            if (!isset($oilConfig['mapping']["{$v['oil_name']}_{$v['oil_type']}_{$v['oil_level']}"])) {
                unset($oilStationData["fuels"][$k]);
                continue;
            }
            $v['fuelNo'] = $oilConfig['mapping']["{$v['oil_name']}_{$v['oil_type']}_{$v['oil_level']}"];
            array_push($oilStationData['gunNumber'], ...$v['gun_numbers']);
            self::filterField($v, [
                'oil_type',
                'oil_type_val',
                'oil_name',
                'oil_name_val',
                'oil_level',
                'oil_level_val',
                'ndrc_price',
                'gun_numbers',
                'starttime',
                'endtime',
                'coupon_list',
                'discount_rate',
            ]);
            self::replaceKey($v, [
                'oil_title_name' => 'fuelName',
                'price'          => 'discountPrice',
                'gun_price'      => 'price',
            ]);
            $v['price'] = bcmul($v['price'], 100);
            $v['discountPrice'] = bcmul($v['discountPrice'], 100);
            $v['status'] = 0;
        }
        $oilStationData['fuels'] = array_values($oilStationData['fuels']);
        if (empty($oilStationData['fuels'])) {
            $oilStationData['isStop'] = 1;
        }
        if (self::$dbActionInner) {
            $regionalMapping = RegionalInfoData::getNameByCode(
                [$oilStationData['provinceCode'], $oilStationData['cityCode']]
            );
            $oilStationData['provinceName'] = $regionalMapping[$oilStationData['provinceCode']];
            $oilStationData['cityName'] = $regionalMapping[$oilStationData['cityCode']];
        }
        $oilStationData['provinceCode'] = substr($oilStationData['provinceCode'], 0, 6);
        $oilStationData['cityCode'] = substr($oilStationData['cityCode'], 0, 6);
    }

    public static function getAvailableOil(array &$priceList)
    {
        $oilNameAndTypeGroup = [];
        $oilLevelConfig = config("oil.oil_level_int");
        $oilLevelMapping = array_flip($oilLevelConfig);
        foreach ($priceList as $v) {
            if (!isset($oilNameAndTypeGroup[$v['oil_name_val'] . $v['oil_type_val']])) {
                $oilNameAndTypeGroup[$v['oil_name_val'] . $v['oil_type_val']] = [];
            }
            $oilNameAndTypeGroup[$v['oil_name_val'] . $v['oil_type_val']][] = $oilLevelMapping[$v['oil_level_val']] ?? '';
        }
        foreach ($oilNameAndTypeGroup as $gk => &$gv) {
            if (count($gv) > 1) {
                rsort($gv);
            }
            $oilNameAndTypeGroup[$gk] = $gv[0];
        }
        foreach ($priceList as $k => &$v) {
            if (isset($oilNameAndTypeGroup[$v['oil_name_val'] . $v['oil_type_val']]) and
                ($oilLevelMapping[$v['oil_level_val']] ?? '') != $oilNameAndTypeGroup[$v['oil_name_val'] .
                                                                                      $v['oil_type_val']]) {
                unset($priceList[$k]);
            }
        }
        $priceList = array_values($priceList);
    }
}
