<?php
/**
 * Created by PhpStorm.
 * User: zdl
 * Date: 2019-01-10
 * Time: 15:20
 */

namespace App\Models\Data;


use App\Models\Dao\RegionalInfo as RegionalInfoDao;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Query\Builder;
use Throwable;


class RegionalInfo
{
    public const CITY_CODE_TO_NAME_KEY        = "city_code_to_name";
    public const CITY_CHILDREN_TO_PARENT_CODE = "children_to_parent_code";

    public static function getCityCodeByName($cityName, $queryLevel = false): array
    {
        $selectField = ['city_name', 'city_code'];
        $convertKey = ['city_name'];

        if ($queryLevel) {
            $selectField[] = 'level';
            $convertKey[] = 'level';
        }

        $query = (new RegionalInfoDao())->select($selectField);

        if (is_array($cityName)) {
            $query = $query->whereIn("city_name", $cityName);
        } else {
            $query = $query->where("city_name", "=", $cityName);
        }

        $regionalData = $query->get()->toArray();
        return convertListToDict($regionalData, $convertKey);
    }

    public static function getCityCodeByNameFilterUnit($cityName, $queryLevel = false): array
    {
        $selectField = ['city_name', 'city_code'];
        $convertKey = ['city_name'];

        if ($queryLevel) {
            $selectField[] = 'level';
            $convertKey[] = 'level';
        }

        $query = (new RegionalInfoDao())->select($selectField);

        if (is_array($cityName)) {
            $query = $query->whereRaw("city_name regexp ? ", [implode('|', $cityName)]);
        } else {
            $query = $query->whereRaw("city_name like ? ", [$cityName . '%']);
        }

        $regionalData = $query->get()->toArray();

        foreach ($regionalData as &$v) {
            if (in_array($v['level'], [3, 4])) {
                $v['city_name'] = str_replace('市', '', $v['city_name']);
                $v['city_name'] = str_replace('区', '', $v['city_name']);
                $v['city_name'] = str_replace('县', '', $v['city_name']);
            }
        }

        return convertListToDict($regionalData, $convertKey);
    }

    public static function filterCode(array $regionCodes): array
    {
        return (new RegionalInfoDao())
            ->select(['city_code'])
            ->whereIn('city_code', $regionCodes)
            ->pluck('city_code')
            ->toArray();
    }

    /**
     * @param $code
     * @return array
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/6/5 3:15 下午
     */
    public static function getNameByCode($code): array
    {
        if (!is_array($code) and !is_string($code)) {
            throw new Exception("The arg type is invalid.Type: array|string");
        }

        $selectField = ['city_name', 'city_code'];
        $convertKey = ['city_code'];
        $cacheField = md5(var_export($code, true));
        if (!$returnData = json_decode(app('redis')->hget(self::CITY_CODE_TO_NAME_KEY, $cacheField), true)) {
            $returnData = [];
            $codeArr = array_chunk(is_string($code) ? [$code] : $code, 5000);
            foreach ($codeArr as $cv) {
                $query = (new RegionalInfoDao())->select($selectField);

                if (is_array($code)) {
                    $query = $query->whereIn("city_code", $cv);
                } else {
                    $query = $query->where("city_code", "=", $cv);
                }

                $regionalData = $query->get()->toArray();
                $regionalData = convertListToDict($regionalData, $convertKey);
                array_walk($regionalData, function ($value, $key) use (&$returnData) {
                    $returnData[$key] = $value['city_name'];
                });
            }
            app('redis')->hset(self::CITY_CODE_TO_NAME_KEY, $cacheField, json_encode($returnData));
        }

        return $returnData;
    }

    /**
     * @param $regionCode
     * @return RegionalInfoDao|Model|Builder|object|null
     * <AUTHOR> <<EMAIL>>
     * @since 2022/2/17 8:22 PM
     */
    public static function getParentCodeByChildren($regionCode)
    {
        $cacheField = $regionCode;
        if (!$returnData = @unserialize(app('redis')->hget(self::CITY_CHILDREN_TO_PARENT_CODE, $cacheField))) {
            $returnData = (new RegionalInfoDao())
                ->select(['parent'])
                ->where('city_code', '=', $regionCode)
                ->first();
            app('redis')->hset(self::CITY_CHILDREN_TO_PARENT_CODE, $cacheField, serialize($returnData));
        }
        return $returnData;
    }
}
