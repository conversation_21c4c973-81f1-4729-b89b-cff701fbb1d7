<?php
/**
 * Created by PhpStorm.
 * User: zdl
 * Date: 2019-01-10
 * Time: 15:20
 */

namespace App\Models\Data;

use App\Models\Dao\CompanyMapping as CompanyMappingDao;
use Throwable;

class CompanyMapping
{
    public const COMPANY_MAPPING_CACHE_KEY = "company_mapping";

    /**
     * @param array $whereParameters
     * @param array $fields
     * @param bool $only
     * @param bool $returnModel
     * @return CompanyMappingDao|array
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/1/2 11:42 上午
     */
    public static function getCompanyByWhere(
        array $whereParameters,
        array $fields,
        bool $only = true,
        bool $returnModel = false
    ) {
        $cacheKey = md5(var_export(func_get_args(), true));
        if (!$data = @unserialize(app('redis')->hget(self::COMPANY_MAPPING_CACHE_KEY, $cacheKey))) {
            $companyMappingModel = (new CompanyMappingDao());
            $companyMappingFillAble = $companyMappingModel->getFillable();

            foreach ($whereParameters as $value) {
                if (!isset($value['field']) or !isset($value['operator']) or !isset($value['value'])) {
                    continue;
                }

                if (is_object($value['value']) or is_array($value['field']) or is_object($value['field']) or
                    !is_string($value['operator'])) {
                    continue;
                }

                if (in_array($value['field'], $companyMappingFillAble)) {
                    switch ($value['operator']) {
                        case 'in':
                            $companyMappingModel = $companyMappingModel->whereIn(
                                "{$value['field']}",
                                $value['value']
                            );
                            break;
                        default:
                            $companyMappingModel = $companyMappingModel->where(
                                "{$value['field']}",
                                $value['operator'],
                                $value['value']
                            );
                    }
                }
            }

            if ($only) {
                $data = $companyMappingModel->first($fields);
            } else {
                $data = $companyMappingModel->get($fields);
            }
            if (!$data) {
                $data = $returnModel ? null : [];
            }
            app('redis')->hset(self::COMPANY_MAPPING_CACHE_KEY, $cacheKey, serialize($data));
        }

        return $data;
    }

    public static function update(array $params)
    {
        $companyMappingModel = new CompanyMappingDao();
        Base::popNotInFillAbleElement($companyMappingModel, $params);
        $params['updated_at'] = date('Y-m-d H:i:s');
        $companyMappingModel->where('id', '=', $params['id'])->update($params);
        app('redis')->del([self::COMPANY_MAPPING_CACHE_KEY]);
    }

    /**
     * @param array $params
     * @return void
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/8/26 10:55 上午
     */
    public static function create(array $params)
    {
        $model = new CompanyMappingDao();
        $model->company_no = $params["company_no"];
        $model->platform_company_no = $params["platform_company_no"];
        $model->platform_name = $params["platform_name"];
        $model->created_at = $params["created_at"] ?? date("Y-m-d H:i:s");
        $model->save();
        app('redis')->del([self::COMPANY_MAPPING_CACHE_KEY]);
    }
}
