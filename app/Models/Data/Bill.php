<?php


namespace App\Models\Data;


class Bill extends Base
{

    public static function handle(string $source = 'default', array $initData = [])
    {
        // TODO: Implement handle() method.
    }

    public static function formatBillData(array &$initData)
    {
        self::filterField($initData, [
            "vice_no",
            "id",
            "unit",
            "cancel_sn",
            "trade_place_provice_code",
            "trade_time",
            "trade_type",
            "oil_name",
            "service_money",
            "total_money",
            "account_no",
            "account_name",
            "use_fanli_money",
            "mini_trade_price",
            "station_code",
            "station_id",
            "trade_jifen",
            "oil_balance",
            "trade_place_provice_code",
            "trade_place_provice_name",
            "trade_place_city_code",
            "trade_place_city_name",
            "regions_id",
            "regions_name",
            "fetch_time",
            "is_fanli",
            "main_no",
            "card_owner",
            "oil_com",
            "card_from",
            "active_region",
            "org_id",
            "main_operator_id",
            "org_operator_id",
            "consume_type",
            "truck_no",
            "fanli_no",
            "fanli_money",
            "fanli_jifen",
            "policy_id",
            "fanli_way",
            "imgurl",
            "updatetime",
            "qz_drivername",
            "qz_drivertel",
            "receipt_remain",
            "is_open_invoice",
            "data_updated",
            "mileage",
            "regions_code",
            "oil_type",
            "trade_type_name",
            "org_id_code",
            "org_id_name",
            "third_id",
            "sys_id",
            "id",
            "created_at",
            "deleted_at",
            "updated_at",
            "consume_type_name",
            "oil_com_name",
            "card_from_name",
            "_card_from",
            "_driver_line",
            "driver_tel",
            "balance",
            "api_id",
            "oil_level",
            "history_params",
            'order_sn',
            'trade_no',
            'mirror',
            'ndrc_price',
        ]);
        self::replaceKey($initData, [
            "trade_place"   => "station_name",
            "trade_money"   => "money",
            "trade_price"   => "price",
            "trade_num"     => "oil_num",
            "createtime"    => "create_time",
            "oil_name_val"  => "oil_name",
            "oil_type_val"  => "oil_type",
            "oil_level_val" => "oil_level",
            "history_id"    => "trade_id",
        ]);
        $initData['money'] = (float)$initData['money'];
    }

    public static function formatBillDataNew(array &$initData)
    {
        self::filterField($initData, [
            "vice_no",
            "id",
            "unit",
            "cancel_sn",
            "trade_place_provice_code",
            "trade_time",
            "trade_type",
            "oil_name",
            "service_money",
            "total_money",
            "account_no",
            "account_name",
            "use_fanli_money",
            "mini_trade_price",
            "station_code",
            "trade_jifen",
            "oil_balance",
            "trade_place_provice_code",
            "trade_place_provice_name",
            "trade_place_city_code",
            "trade_place_city_name",
            "regions_id",
            "regions_name",
            "fetch_time",
            "is_fanli",
            "main_no",
            "card_owner",
            "oil_com",
            "card_from",
            "active_region",
            "org_id",
            "main_operator_id",
            "org_operator_id",
            "consume_type",
            "truck_no",
            "fanli_no",
            "fanli_money",
            "fanli_jifen",
            "policy_id",
            "fanli_way",
            "imgurl",
            "updatetime",
            "qz_drivername",
            "qz_drivertel",
            "receipt_remain",
            "is_open_invoice",
            "data_updated",
            "mileage",
            "regions_code",
            "oil_type",
            "trade_type_name",
            "org_id_code",
            "org_id_name",
            "third_id",
            "sys_id",
            "id",
            "created_at",
            "deleted_at",
            "updated_at",
            "consume_type_name",
            "oil_com_name",
            "card_from_name",
            "_card_from",
            "_driver_line",
            "driver_tel",
            "balance",
            "api_id",
            "oil_level",
            "history_params",
            "pcode",
            "consumeFlag",
            "order_channel",
            "order_type",
            "order_status",
            "province_code",
            "city_code",
            "supplier_code",
            "org_code",
            "card_no",
            "card_type",
            "card_level",
            "driver_name",
            "driver_source",
            "driver_phone",
            "oil_unit",
            "oil_money",
            "oil_price",
            "real_oil_num",
            "service_price",
            "creator",
            "update_time",
            "updator",
            "trade_id",
            "third_order_id",
            "payment_id",
            "pay_time",
            "remark",
            "order_type_name",
            "order_channel_name",
            "org_name",
            "driver_source_name",
            "card_type_name",
            "card_level_name",
            "account_type_name",
            "remark_name",
            "station_address",
            "rebate_grade",
            "supplier_name",
            "province_name",
            "city_name",
            "goods",
            "gun_id",
            "gun_name",
            "tank_id",
            "tank_name",
            "supplier_price",
            "platform_price",
            "price_id",
            "list_price",
            "is_check_password",
            "order_token",
            "operator_id",
            "is_special_refund_pcode",
            "plummet_rebate_money",
            "cal_cost_money",
            "cal_cost_price",
            "plummet_rebate_money",
            "later_rebate_money",
            "charge_rebate_money",
            "gun_number",
            "upstream_settle_price",
            "upstream_settle_money",
            "g7_coupon_flag",
            "third_coupon_flag",
            "original_order_id",
            "approve_remark",
            "open_api_org_code",
            "is_sync",
            "app_station_id",
            "driver_signature",
            "order_flag",
            "lat",
            "lng",
            "trade_mode",
            'order_sn',
            'trade_no',
            'mirror',
            'ndrc_price',
        ]);
        self::replaceKey($initData, [
            "trade_place"    => "station_name",
            "trade_money"    => "money",
            "trade_price"    => "price",
            "trade_num"      => "oil_num",
            "createtime"     => "create_time",
            "oil_name_name"  => "oil_name",
            "oil_type_name"  => "oil_type",
            "oil_level_name" => "oil_level",
            "history_id"     => "trade_id",
        ]);
        $initData['money'] = (float)$initData['money'];
    }
}

