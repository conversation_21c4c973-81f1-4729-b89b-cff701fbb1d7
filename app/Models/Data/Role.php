<?php


namespace App\Models\Data;

use App\Models\Dao\Role as RoleDao;
use Exception;

class Role
{
    public const ALL_CACHE_KEY = 'role_info';

    public static function getData(array $params): array
    {
        $model = new RoleDao();

        if (isset($params['role_name'])) {
            $model = $model->where('role_name', 'like', "%{$params['role_name']}%");
        }

        $count = $model->count();
        $model = $model
            ->offset(($params['page'] - 1) * $params['limit'])
            ->limit($params['limit'])
            ->orderBy('created_at', 'desc');
        $list = $model->get();

        return [
            'count' => $count,
            'list'  => $list ? $list->toArray() : [],
        ];
    }

    public static function getSelect()
    {
        $redisConn = app('redis');

        if ($cacheData = json_decode($redisConn->get(self::ALL_CACHE_KEY), true)) {
            return $cacheData;
        }

        $list = (new RoleDao())->get(['id', 'role_name']);
        $data = $list ? $list->toArray() : [];
        $redisConn->set(self::ALL_CACHE_KEY, json_encode($data));
        return $data;
    }

    public static function update(array $params)
    {
        $model = new RoleDao();
        Base::popNotInFillAbleElement($model, $params);
        $params['updated_at'] = date('Y-m-d H:i:s');
        $model->where('id', '=', $params['id'] ?? '')->update($params);
        app('redis')->del([self::ALL_CACHE_KEY]);
    }

    /**
     * @param array $params
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-24 20:45
     */
    public static function delete(array $params)
    {
        (new RoleDao())->where('id', '=', $params['id'] ?? '')->delete();
        app('redis')->del([self::ALL_CACHE_KEY]);
    }

    public static function create(array $params)
    {
        $model = new RoleDao();
        $model->role_name = $params['role_name'];
        $model->save();
        app('redis')->del([self::ALL_CACHE_KEY]);
    }

    public static function getValidateData(): array
    {
        $list = (new RoleDao())->pluck('id');
        return $list ? $list->toArray() : [];
    }
}
