<?php


namespace App\Models\Data\DecodingQrCodeAdaptation;


use App\Models\Data\DecodingResultEntity;

class XY extends BASIC
{
    public function getAdaptationResult(DecodingResultEntity $entity): array
    {
        $decodingResultData = get_object_vars($entity);
        self::filterField($decodingResultData, [
            'is_check_pwd',
            'access_key',
            'secret',
            'driver_name',
            'availableLiters',
            'org_code',
            'driver_phone',
            'card_no',
            'org_code',
            'station_id',
            'name_abbreviation',
            'qrCode',
            'order_no',
            'qr_code_source',
            'is_self',
        ]);
        self::replaceKey($decodingResultData, [
            'truck_no'        => 'plate_number',
            'availableAmount' => 'balance',
        ]);
        $decodingResultData['card_no'] = md5($entity->qrCode);
        app('redis')->setex($decodingResultData['card_no'], 7200, json_encode(get_object_vars($entity)));
        return $decodingResultData;
    }
}
