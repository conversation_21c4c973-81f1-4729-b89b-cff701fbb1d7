<?php


namespace App\Models\Data;

use App\Models\Dao\StationPushRecord as StationPushRecordDao;
use Illuminate\Support\Facades\Log;
use Throwable;

class StationPushRecord
{
    public static function handle(
        string $platformName,
        $stationId,
        string $stationName,
        int $pushStatus,
        string $pushedAt,
        array $pushRecordData = []
    ) {
        try {
            $model = new StationPushRecordDao();
            $model->platform_name = $platformName;
            $model->station_id = $stationId;
            $model->station_name = $stationName;
            $model->push_status = $pushStatus;
            $model->pushed_at = $pushedAt;
            $model->push_data = trim(json_encode($pushRecordData['push_data'] ?? '', 256), '"') ?? '';
            $model->push_result = trim(json_encode($pushRecordData['push_result'] ?? '', 256), '"') ?? '';
            $tableName = $model->getTable() . "_" . Base::getCurYear(false) . "_" . date('m');
            $model->setTable($tableName)->save();
        } catch (Throwable $exception) {
            Log::error("Log for station push record of $platformName failed", [
                'data' => func_get_args(),
                'exception' => $exception,
            ]);
        }
    }

    public static function getData(array $params): array
    {
        $model = new StationPushRecordDao();
        $where = [];
        $fillAble = $model->getFillable();

        foreach ($params as $k => $v) {
            if (in_array($k, $fillAble)) {
                switch ($k) {
                    case 'station_name':

                        $where[] = "station_name like '%{$params['station_name']}%'";
                        break;
                    case 'station_id':

                        $where[] = "station_id = '{$params['station_id']}'";
                        break;
                    case 'push_status':

                        $where[] = "push_status = {$params['push_status']}";
                        break;
                    case 'platform_name':

                        $where[] = "platform_name = '{$params['platform_name']}'";
                        break;
                }
            }

            switch ($k) {
                case 'startTime':

                    $where[] = "pushed_at >= '{$params['startTime']}'";
                    break;
                case 'endTime':

                    $where[] = "pushed_at <= '{$params['endTime']}'";
                    break;
            }
        }

        $params['startTime'] = isset($params['startTime']) ? strtotime($params['startTime']) : time();
        $params['endTime'] = isset($params['endTime']) ? strtotime($params['endTime']) : time();
        $sql = Base::getStationPushRecordTableSqlByTime(
            $model->getRealTable(),
            $params['startTime'],
            $params['endTime'],
            [
                'offset' => ($params['page'] - 1) * $params['limit'],
                'limit'  => $params['limit'],
            ],
            "*",
            implode(" and ", $where)
        );
        $connection = $model->getConnection();
        return [
            'count' => $connection->select($sql['count'])[0]->total ?? 0,
            'list'  => $connection->select($sql['page']),
        ];
    }
}
