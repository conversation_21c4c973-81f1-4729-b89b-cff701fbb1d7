<?php


namespace App\Models\Data\OilPriceMapping;


use App\Models\Data\Log\QueueLog as Log;


class CZB extends Basic
{
    public static function getOilPriceData(array &$oilPriceData): array
    {
        $oilPriceArr = [];
        $oilArr = [];

        foreach ($oilPriceData as $cv) {

            $oilPriceTemp = [
                'oil_level' => '',
            ];

            switch ($cv['oilType']) {

                case 1:

                    $oilPriceTemp['oil_name'] = config("oil.oil_type.汽油", "");
                    $oilPriceTemp['oil_type'] = config("oil.oil_no.汽油{$cv['oilName']}", "");
                    break;
                case 2:

                    if ($cv['oilName'] == '0#') {

                        $oilPriceTemp['oil_name'] = config("oil.oil_type.车用柴油(Ⅵ)", "");
                        $oilPriceTemp['oil_type'] = config("oil.oil_no.车用柴油(Ⅵ){$cv['oilName']}", "");
                    } else {

                        $oilPriceTemp['oil_name'] = config("oil.oil_type.柴油", "");
                        $oilPriceTemp['oil_type'] = config("oil.oil_no.柴油{$cv['oilName']}", "");
                    }
                    break;
                case 3:

                    switch ($cv['oilName']) {

                        case 'CNG':

                            $oilPriceTemp['oil_name'] = config("oil.oil_type.压缩天然气", "");
                            $oilPriceTemp['oil_type'] = "";
                            break;
                        case 'LNG':

                            $oilPriceTemp['oil_name'] = config("oil.oil_type.液化天然气", "");
                            $oilPriceTemp['oil_type'] = "";
                            break;
                    }
                    break;
                default:
                    continue 2;
            }

            $oilArr[] = $oilPriceTemp['oil_name'] . $oilPriceTemp['oil_type'];

            //如果油品名称为空那么过滤该商品
            if (empty($oilPriceTemp['oil_name'])) {

                Log::handle("The oil of station is invalid", [
                    'data' => $cv,
                    'oil'  => $oilPriceTemp,
                ], '车主邦', 'oil_station_data', 'error');
                continue;
            }

            $oilPriceTemp['price'] = $cv['priceYfq'];
            $oilPriceTemp['mac_price'] = $cv['priceGun'];
            $oilPriceTemp['sale_price'] = bcmul($cv['priceYfq'], 100);
            $oilPriceTemp['listing_price'] = bcmul($cv['priceGun'], 100);
            $oilPriceTemp['issue_price'] = bcmul($cv['priceOfficial'], 100);

            if (!is_array($cv['gunNos']) or empty($cv['gunNos'])) {

                continue;
            }

            $oilPriceTemp['gun_no'] = array_column($cv['gunNos'], 'gunNo');
            $oilPriceArr[] = $oilPriceTemp;
        }

        //找出重复油品
        $repeatOilArr = array_diff_assoc($oilArr, array_unique($oilArr));

        if (!empty($repeatOilArr)) {

            foreach ($oilPriceArr as $opK => $opV) {

                //如果当前油品为重复油品,那么移除当前油品
                if (in_array("{$opV['oil_name']}{$opV['oil_type']}", $repeatOilArr)) {

                    unset($oilPriceArr[$opK]);
                }
            }
        }

        return array_values($oilPriceArr);
    }
}
