<?php


namespace App\Models\Data;

use App\Models\Dao\OrderAssoc as OrderAssocDao;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log as LogFacade;
use stdClass;
use Throwable;


class OrderAssoc
{
    public static $orderStatusMapping = [
        '1' => '已支付',
        '2' => '待支付',
        '3' => '已退款',
        '4' => '已取消',
    ];

    private static $id;

    public static function setId($id)
    {
        self::$id = $id;
    }

    /**
     * @param int $selfOrderStatus
     * @param int $platformOrderStatus
     * @param $selfOrderId
     * @param $platformOrderId
     * @param string $platformName
     * @param null $reason
     * @param null $platformReason
     * @param string $extend
     * @param string $remark
     * @return OrderAssocDao|void
     * @throws Throwable
     * ---------------------------------------------------
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2019/9/27 5:14 下午
     */
    public static function insert(
        int $selfOrderStatus,
        int $platformOrderStatus,
        $selfOrderId,
        $platformOrderId,
        string $platformName,
        $reason = null,
        $platformReason = null,
        string $extend = '',
        string $remark = ""
    ): OrderAssocDao {
        $model = new OrderAssocDao();
        if (self::$id) {
            $model->id = self::$id;
        }

        $model->created_at = date("Y-m-d H:i:s");
        $model->reason = empty($reason) ? '' : self::getMessage($reason);
        $model->platform_reason = empty($platformReason) ? '' : self::getMessage($platformReason);
        $model->self_order_id = $selfOrderId;
        $model->platform_name = $platformName;
        $model->platform_order_id = $platformOrderId;
        $model->platform_order_status = $platformOrderStatus;
        $model->self_order_status = $selfOrderStatus;
        $model->extend = $extend;
        $model->remark = $remark;
        $model->save();
        return $model;
    }

    /**
     * @param null $reason
     * @return false|string|null
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-09 19:53
     */
    public static function getMessage($reason = null)
    {
        if (is_object($reason) or is_array($reason)) {
            return json_encode($reason);
        }
        return $reason;
    }

    public static function updateSelfOrderStatus($selfOrderId, int $selfOrderStatus, $reason = null)
    {
        try {
            $updateData = [];
            $updateData['updated_at'] = date("Y-m-d H:i:s");
            if (!empty($reason)) {
                $updateData['reason'] = self::getMessage($reason);
            }
            $updateData['self_order_status'] = $selfOrderStatus;
            return (new OrderAssocDao())->where([
                'self_order_id' => $selfOrderId
            ])->update($updateData);
        } catch (Throwable $exception) {
            LogFacade::error("Save order assoc failed", [
                'data'      => func_get_args(),
                'exception' => $exception,
            ]);
            return false;
        }
    }

    public static function updateOrderInfoByOrderId(
        string $orderId,
        array $orderInfo,
        bool $isSelf = true,
        string $platformName = ''
    ) {
        try {
            $updateData = [];
            $updateData['reason'] = empty($orderInfo['reason']) ? '' : self::getMessage($orderInfo['reason']);
            $updateData['platform_reason'] = empty($orderInfo['platform_reason']) ? '' : self::getMessage(
                $orderInfo['platform_reason']
            );
            if (isset($orderInfo['self_order_status'])) {
                $updateData['self_order_status'] = (int)$orderInfo['self_order_status'];
            }
            if (isset($orderInfo['platform_order_status'])) {
                $updateData['platform_order_status'] = (int)$orderInfo['platform_order_status'];
            }
            if (isset($orderInfo['platform_name'])) {
                $updateData['platform_name'] = $orderInfo['platform_name'];
            }
            if (isset($orderInfo['platform_order_id'])) {
                $updateData['platform_order_id'] = $orderInfo['platform_order_id'];
            }
            if (isset($orderInfo['self_order_id'])) {
                $updateData['self_order_id'] = $orderInfo['self_order_id'];
            }
            if (isset($orderInfo['extend']) and !empty($orderInfo['extend'])) {
                $updateData['extend'] = self::getMessage($orderInfo['extend']);
            }
            $updateData['updated_at'] = date("Y-m-d H:i:s");
            $where = [];

            if ($isSelf) {
                $where['self_order_id'] = $orderId;
            } else {
                $where['platform_order_id'] = $orderId;
            }

            if (!empty($platformName)) {
                $where['platform_name'] = $platformName;
            }
            return (new OrderAssocDao())->where($where)->update($updateData);
        } catch (Throwable $exception) {
            LogFacade::error("Save order assoc failed", [
                'data'      => func_get_args(),
                'exception' => $exception,
            ]);
            return false;
        }
    }

    public static function updateOrderInfoById(string $id, array $orderInfo)
    {
        try {
            $updateData = [];
            $updateData['reason'] = empty($orderInfo['reason']) ? '' : self::getMessage($orderInfo['reason']);
            $updateData['platform_reason'] = empty($orderInfo['platform_reason']) ? '' : self::getMessage(
                $orderInfo['platform_reason']
            );
            if (isset($orderInfo['self_order_status'])) {
                $updateData['self_order_status'] = (int)$orderInfo['self_order_status'];
            }
            if (isset($orderInfo['platform_order_status'])) {
                $updateData['platform_order_status'] = (int)$orderInfo['platform_order_status'];
            }
            if (isset($orderInfo['platform_name'])) {
                $updateData['platform_name'] = $orderInfo['platform_name'];
            }
            if (isset($orderInfo['platform_order_id'])) {
                $updateData['platform_order_id'] = $orderInfo['platform_order_id'];
            }
            if (isset($orderInfo['self_order_id'])) {
                $updateData['self_order_id'] = $orderInfo['self_order_id'];
            }
            $updateData['updated_at'] = date("Y-m-d H:i:s");
            return (new OrderAssocDao())->where([
                'id' => $id
            ])->update($updateData);
        } catch (Throwable $exception) {
            LogFacade::error("Save order assoc failed", [
                'data'      => func_get_args(),
                'exception' => $exception,
            ]);
            return false;
        }
    }

    public static function getOrderInfoByPlatformOrderId(
        $platformOrderId,
        int $platformOrderStatus = 0,
        int $selfOrderStatus = 0
    ) {
        $orderInfoObj = (new OrderAssocDao())->where('platform_order_id', $platformOrderId);

        if ($platformOrderStatus != 0) {
            $orderInfoObj->where('platform_order_status', '=', $platformOrderStatus);
        }

        if ($selfOrderStatus != 0) {
            $orderInfoObj->where('self_order_status', '=', $selfOrderStatus);
        }

        $orderInfoObj = $orderInfoObj->first();
        if (!is_null($orderInfoObj)) {
            return $orderInfoObj->toArray();
        }
        return [];
    }

    public static function getOrderInfoBySelfOrderId(
        $selfOrderId,
        array $whereParameters = [],
        array $fields = ['*'],
        bool $returnObject = false
    ) {
        $orderInfoObj = (new OrderAssocDao())->where('self_order_id', $selfOrderId);

        if (isset($whereParameters['platform_order_status'])) {
            $orderInfoObj->where('platform_order_status', '=', $whereParameters['platform_order_status']);
        }

        if (isset($whereParameters['self_order_status'])) {
            $orderInfoObj->where('self_order_status', '=', $whereParameters['self_order_status']);
        }

        if (isset($whereParameters['platform_name'])) {
            $orderInfoObj->where('platform_name', '=', $whereParameters['platform_name']);
        }

        $orderInfoObj = $orderInfoObj->first($fields);

        if (isset($orderInfoObj->id)) {
            if (!$returnObject) {
                return $orderInfoObj->toArray();
            }
            return $orderInfoObj;
        } else {
            if (!$returnObject) {
                return [];
            }
            return new stdClass();
        }
    }

    /**
     * 通过指定查询条件查询订单信息
     * @param array $whereParameters
     * @param array $fields
     * @param bool $only
     * @param bool $returnModel
     * @return orderAssocDao|array
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/1/2 11:42 上午
     */
    public static function getOrderInfoByWhere(
        array $whereParameters,
        array $fields,
        bool $only = true,
        bool $returnModel = false
    ) {
        $orderInfoModel = (new OrderAssocDao());
        $orderInfoFillAble = $orderInfoModel->getFillable();

        foreach ($whereParameters as $value) {
            if (!isset($value['field']) or !isset($value['operator']) or !isset($value['value'])) {
                continue;
            }

            if (is_object($value['value']) or is_array($value['field']) or is_object($value['field']) or
                !is_string($value['operator'])) {
                continue;
            }

            if (in_array($value['field'], $orderInfoFillAble)) {
                switch ($value['operator']) {
                    case 'in':
                        if (strpos($value['field'], 'order_id') !== false) {
                            array_walk($value['value'], function (&$v) {
                                $v = (string)$v;
                            });
                        }
                        $orderInfoModel = $orderInfoModel->whereIn(
                            "{$value['field']}",
                            $value['value']
                        );
                        break;
                    default:
                        if (strpos($value['field'], 'order_id') !== false) {
                            $value['value'] = (string)$value['value'];
                        }
                        $orderInfoModel = $orderInfoModel->where(
                            "{$value['field']}",
                            $value['operator'],
                            $value['value']
                        );
                }
            }
        }

        if ($only) {
            $orderInfoObj = $orderInfoModel->first($fields);
        } else {
            $orderInfoObj = $orderInfoModel->get($fields);
        }
        if (is_null($orderInfoObj)) {
            return $returnModel ? null : [];
        }
        return $returnModel ? $orderInfoObj : $orderInfoObj->toArray();
    }

    /**
     * 通过指定查询条件查询一个订单信息并加悲观锁
     * @param array $whereParameters
     * @param array $fields
     * @param bool $returnModel
     * @return orderAssocDao|array
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/1/2 11:42 上午
     */
    public static function getOneOrderByWhereAndLock(
        array $whereParameters,
        array $fields,
        bool $returnModel = false
    ) {
        $orderInfoModel = (new OrderAssocDao());
        $orderInfoFillAble = $orderInfoModel->getFillable();

        foreach ($whereParameters as $value) {
            if (!isset($value['field']) or !isset($value['operator']) or !isset($value['value'])) {
                continue;
            }

            if (is_object($value['value']) or is_array($value['field']) or is_object($value['field']) or
                !is_string($value['operator'])) {
                continue;
            }

            if (in_array($value['field'], $orderInfoFillAble)) {
                switch ($value['operator']) {
                    case 'in':
                        if (strpos($value['field'], 'order_id') !== false) {
                            array_walk($value['value'], function (&$v) {
                                $v = (string)$v;
                            });
                        }
                        $orderInfoModel = $orderInfoModel->whereIn(
                            "{$value['field']}",
                            $value['value']
                        );
                        break;
                    default:
                        if (strpos($value['field'], 'order_id') !== false) {
                            $value['value'] = (string)$value['value'];
                        }
                        $orderInfoModel = $orderInfoModel->where(
                            "{$value['field']}",
                            $value['operator'],
                            $value['value']
                        );
                }
            }
        }

        $orderInfoObj = $orderInfoModel->lockForUpdate()->first($fields);
        if (is_null($orderInfoObj)) {
            return $returnModel ? null : [];
        }
        return $returnModel ? $orderInfoObj : $orderInfoObj->toArray();
    }

    /**
     * @param array $orderInfo
     * @return array|null
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/10/22 3:11 下午
     */
    public static function insertBySearchForPlatform(array $orderInfo)
    {
        if (!isset($orderInfo['platform_order_id'])) {
            return null;
        }

        DB::beginTransaction();
        $orderInfoArr = [];
        $allowPushTask = false;

        try {
            $sql = "select * from oil_adapter.adapter_order_assoc where platform_order_id = ? ";
            $sqlBindings = [
                $orderInfo['platform_order_id'],
            ];
            $remark = "";
            $sql .= " for update";
            $orderInfoObj = DB::selectOne($sql, $sqlBindings);
            //增加判断该订单历史状态是否成功
            if (date("d") != date("d", strtotime($orderInfo['refueling_time'])) and
                is_null($orderInfoObj)) {
                $remark = "补推";
                $allowPushTask = true;
            }

            if (is_null($orderInfoObj)) {
                try {
                    $model = new OrderAssocDao();
                    $model->created_at = date("Y-m-d H:i:s");
                    $model->reason = !isset($orderInfo['reason']) ? '' : self::getMessage($orderInfo['reason']);
                    $model->platform_reason = !isset($orderInfo['platform_reason']) ? '' :
                        self::getMessage($orderInfo['platform_reason']);
                    $model->self_order_id = '';
                    $model->platform_name = $orderInfo['platform_name'];
                    $model->platform_order_id = $orderInfo['platform_order_id'];
                    $model->platform_order_status = $orderInfo['platform_order_status'];
                    $model->self_order_status = 2;
                    $model->extend = $orderInfo['extend'] ?? '';
                    $model->remark = $remark;
                    $model->save();
                    $orderInfoArr = $model->toArray();
                    $allowPushTask = true;
                } catch (Throwable $exception) {
                    LogFacade::error("Save order assoc for {$orderInfo['platform_name']} failed", [
                        'data'      => func_get_args(),
                        'exception' => $exception,
                    ]);
                }
            } else {
                $orderInfoArr = get_object_vars($orderInfoObj);
            }

            DB::commit();
        } catch (Throwable $exception) {
            DB::rollBack();
            throw $exception;
        }

        $orderInfoArr['allowPushTask'] = $allowPushTask;
        return $orderInfoArr;
    }

    public static function getData(array $params)
    {
        $model = new OrderAssocDao();

        if (isset($params['self_order_id']) and !empty($params['self_order_id'])) {
            if (is_array($params['self_order_id'])) {
                $model = $model->whereIn('self_order_id', $params['self_order_id']);
            } else {
                $model = $model->where('self_order_id', $params['self_order_id']);
            }
        }

        if (isset($params['platform_order_id']) and !empty($params['platform_order_id'])) {
            $model = $model->where('platform_order_id', $params['platform_order_id']);
        }

        if (isset($params['platform_name']) and !empty($params['platform_name'])) {
            $model = $model->where('platform_name', $params['platform_name']);
        }

        if (isset($params['createdStartTime']) and !empty($params['createdStartTime'])) {
            $model = $model->where('created_at', '>=', $params['createdStartTime']);
        }

        if (isset($params['createdEndTime']) and !empty($params['createdEndTime'])) {
            $model = $model->where('created_at', '<=', $params['createdEndTime']);
        }

        if (isset($params['updatedStartTime']) and !empty($params['updatedStartTime'])) {
            $model = $model->where('updated_at', '>=', $params['updatedStartTime']);
        }

        if (isset($params['updatedEndTime']) and !empty($params['updatedEndTime'])) {
            $model = $model->where('updated_at', '<=', $params['updatedEndTime']);
        }

        if (isset($params['platform_order_status']) and !empty($params['platform_order_status'])) {
            $model = $model->where('platform_order_status', '=', $params['platform_order_status']);
        }

        if (isset($params['self_order_status']) and !empty($params['self_order_status'])) {
            $model = $model->where('self_order_status', '=', $params['self_order_status']);
        }

        $count = $model->count();
        $model = $model
            ->offset(($params['page'] - 1) * $params['limit'])
            ->limit($params['limit'])
            ->orderBy('created_at', 'desc');

        if ($list = $model->get()->toArray()) {
            $platformInfoMapping = convertListToDict(
                DockingPlatformInfoData::getPlatformNameByNameAbbreviation(
                    array_column($list, 'platform_name')
                ),
                'name_abbreviation'
            );

            foreach ($list as &$v) {
                $v['platform_order_status_val'] = self::$orderStatusMapping[$v['platform_order_status']];
                $v['self_order_status_val'] = self::$orderStatusMapping[$v['self_order_status']];
                $v['platform_name_val'] = $platformInfoMapping[$v['platform_name']]['platform_name'] ?? (
                    $v['platform_name'] ?? '');
            }
        }

        return [
            'count' => $count,
            'list'  => $list,
        ];
    }

    public static function getChart()
    {
        $returnData = [
            'title'      => '近七周订单分布图',
            'subTitle'   => '',
            'yAxisTitle' => '订单数量',
        ];
        $model = new OrderAssocDao();
        $beginCreateTime = date("Y-m-d 00:00:00", strtotime("-7 week"));
        $weekTimeArr = [];
        $series = [];

        for ($i = 1; $i < 8; $i++) {
            $weekTimeArr[$i] = [
                'startTime' => date("Y-m-d 00:00:00", strtotime("-$i week")),
                'endTime'   => date("Y-m-d 23:59:59", strtotime("-" . ($i - 1) . " week")),
                'week'      => "第{$i}周",
            ];
        }

        $data = $model->select(["platform_name", "id", "created_at"])
                      ->where("created_at", ">=", $beginCreateTime)
                      ->where("created_at", "<=", date("Y-m-d 23:59:59"))
                      ->get()
                      ->toArray();

        foreach ($data as $v) {
            foreach ($weekTimeArr as $wk => $wv) {
                if (!isset($series[$v['platform_name']])) {
                    $series[$v['platform_name']] = [
                        'name' => $v['platform_name'],
                        'data' => [],
                    ];
                }

                if ($v['created_at'] >= $wv['startTime'] and $v['created_at'] <= $wv['endTime']) {
                    if (isset($series[$v['platform_name']]['data'][$wk])) {
                        $series[$v['platform_name']]['data'][$wk]++;
                    } else {
                        $series[$v['platform_name']]['data'][$wk] = 1;
                    }
                } elseif (!isset($series[$v['platform_name']]['data'][$wk])) {
                    $series[$v['platform_name']]['data'][$wk] = 0;
                }
            }
        }

        $series = array_values($series);

        foreach ($series as &$v) {
            ksort($v['data']);
            $v['data'] = array_values($v['data']);
        }

        $returnData['yAxisCategories'] = array_column($weekTimeArr, 'week');
        $returnData['series'] = $series;

        return $returnData;
    }

    /**
     * 根据传入数据唯一标识结合G7订单交易状态查询需要重新扣费的订单数据
     * @param array $ids
     * @param array $fields
     * @return array
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/11/14 4:30 下午
     */
    public static function getRetryOrderById(array $ids, array $fields = ['*']): array
    {
        $retryOrderData = (new OrderAssocDao())->where('self_order_status', '=', '2')
                                               ->whereIn('id', $ids)
                                               ->select($fields)
                                               ->get();
        if (is_null($retryOrderData)) {
            return [];
        }
        return $retryOrderData->toArray();
    }
}
