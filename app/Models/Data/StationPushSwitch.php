<?php
/**
 * Created by PhpStorm.
 * User: zdl
 * Date: 2019-01-10
 * Time: 15:20
 */

namespace App\Models\Data;

use App\Jobs\BasicJob;
use App\Models\Dao\StationPushSwitch as StationPushSwitchDao;
use App\Models\Logic\Data\Push\OilStationData as OilStationDataLogic;
use Illuminate\Http\JsonResponse;
use Throwable;


/**
 * Class StationPushSwitch
 * @package App\Models\Data
 */
class StationPushSwitch
{
    public const allCache     = "station_push_switch_all";
    public const openAllCache = "station_push_switch_open_all";

    /**
     * @param array $parameter
     * @return array
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/10/29 7:20 下午
     */
    public static function getData(array $parameter)
    {
        $model = new StationPushSwitchDao();

        if (isset($parameter['name_abbreviation'])) {
            $model = $model->where('name_abbreviation', 'like', "%{$parameter['name_abbreviation']}%");
        }

        if (isset($parameter['switch_status'])) {
            $model = $model->where('switch_status', '=', "{$parameter['switch_status']}");
        }

        $count = $model->count();
        $model = $model
            ->offset(($parameter['page'] - 1) * $parameter['limit'])
            ->limit($parameter['limit'])
            ->orderBy('created_at', 'desc');
        $listData = $model->get();
        $listData = $listData ? $listData->toArray() : [];
        $redis = app('redis');
        $pushedStationCount = $redis->pipeline(function ($pipe) use ($listData) {
            foreach ($listData as $v) {
                $pipe->hlen(BasicJob::OIL_STATION_CACHE_CHECK . '_' . $v['name_abbreviation']);
            }
        });

        foreach ($listData as $k => &$v) {
            $v['pushedStationCount'] = $pushedStationCount[$k];
        }

        return [
            'count' => $count,
            'list'  => $listData,
        ];
    }

    public static function getOpenAll()
    {
        $model = new StationPushSwitchDao();
        $redisConn = app("redis");
        if (!$data = json_decode($redisConn->get(self::openAllCache), true)) {
            $model = $model
                ->leftJoin(
                    "auth_info",
                    "station_push_switch.name_abbreviation",
                    "=",
                    "auth_info.name_abbreviation"
                )
                ->where('station_push_switch.switch_status', '=', 1);
            $data = $model->get(['station_push_switch.name_abbreviation', 'auth_info.role_code'])->toArray();
            app("redis")->set(self::openAllCache, json_encode($data));
        }
        return $data;
    }

    /**
     * @param array $params
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/10/29 7:20 下午
     */
    public static function update(array $params)
    {
        $model = new StationPushSwitchDao();
        Base::popNotInFillAbleElement($model, $params);
        $params['updated_at'] = date('Y-m-d H:i:s');
        $model->where('id', '=', $params['id'] ?? '')->update($params);
        app("redis")->del([self::allCache, self::openAllCache]);
    }

    /**
     * @param array $params
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/10/29 7:20 下午
     */
    public static function create(array $params)
    {
        $model = new StationPushSwitchDao();
        $model->name_abbreviation = $params["name_abbreviation"];
        $model->switch_status = $params["switch_status"];
        $model->save();
        app("redis")->del([self::allCache, self::openAllCache]);
        return responseFormat(0, ["id" => $model->id]);
    }

    /**
     * @param array $params
     * @return array
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/8/31 12:03 下午
     * @deprecated 由于客户站点缓存数据优化为加密数据，故此处原有功能已无法使用，前端功能也已下线
     */
    public static function getPushedStationList(array $params): array
    {
        if (!isset($params['name_abbreviation'])) {
            return [];
        }

        if (!isset($params['page'])) {
            $params['page'] = is_int($params['page']) ? $params['page'] : 1;
        }

        if (!isset($params['limit'])) {
            $params['limit'] = is_int($params['limit']) ? $params['limit'] : 10;
        }

        $offset = ($params['page'] - 1) * $params['limit'];
        $redis = app('redis');
        $fields = $redis->hkeys(BasicJob::OIL_STATION_CACHE_CHECK . '_' . $params['name_abbreviation']);

        if (isset($params['station_id']) and !empty($params['station_id'])) {
            if (in_array($params['station_id'], $fields)) {
                $currentFields = [$params['station_id']];
                $fields = [$params['station_id']];
            } else {
                $currentFields = [];
                $fields = [];
            }
        } else {
            sort($fields);
            $currentFields = array_slice($fields, max(($offset - 1), 0), $params['limit']);
        }

        if (!empty($currentFields)) {
            $data = $redis->hmget(
                BasicJob::OIL_STATION_CACHE_CHECK . '_' .
                $params['name_abbreviation'],
                $currentFields
            );

            foreach ($data as $k => &$v) {
                $v = json_decode($v, true);
                if (!$v) {
                    unset($data[$k]);
                    continue;
                }
                if (!array_has($v, 'station_name')) {
                    $stationAllData = json_decode(
                                          $redis->hget(
                                              OilStationDataLogic::$oilStationCacheForHubKey,
                                              $currentFields[$k]
                                          ),
                                          true
                                      ) ?? [];
                    $v['station_name'] = $stationAllData['station_name'] ?? '';
                }

                $v['station_data'] = json_encode($v);
            }

            return [
                'list'  => array_values($data),
                'count' => count($fields),
            ];
        }

        return [
            'list'  => [],
            'count' => 0,
        ];
    }

    public static function getAll()
    {
        $model = new StationPushSwitchDao();
        $redisConn = app("redis");
        if (!$data = json_decode($redisConn->get(self::allCache), true)) {
            $model = $model
                ->leftJoin(
                    "auth_info",
                    "station_push_switch.name_abbreviation",
                    "=",
                    "auth_info.name_abbreviation"
                );
            $data = $model->get(['station_push_switch.name_abbreviation', 'auth_info.role_code'])->toArray();
            app("redis")->set(self::allCache, json_encode($data));
        }
        return $data;
    }
}
