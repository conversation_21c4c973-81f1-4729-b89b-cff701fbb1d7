<?php


namespace App\Models\Data\Log;

use App\Models\Dao\ResponseLog as ResponseLogDao;
use App\Models\Data\Base;
use App\Models\Data\Log\Log as LogInterface;
use Throwable;

class ResponseLog extends BaseLog implements LogInterface
{
    /**
     * @param null $content
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-04-25 15:25
     */
    public static function handle($content = null)
    {
        try {
            self::initFileLogChannel();
            self::$logChannel->info("Log Response.", $content);
            $model = new ResponseLogDao();
            $model->content = self::getMessage($content);
            $model->created_at = date("Y-m-d H:i:s");
            global $routeId;
            $model->route_id = $routeId;
            $tableName = $model->getTable() . "_" . Base::getCurYear() . "_" . Base::getCurWeekNo();
            $model->setTable($tableName)->save();
        } catch (Throwable $exception) {

            self::$logChannel->error("Log Response failed.", [
                'data'      => func_get_args(),
                'exception' => $exception
            ]);
        }
    }

    /**
     * @param mixed $context
     * @return false|string
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-04 18:39
     */
    public static function getMessage($context = null)
    {
        if (is_array($context) or is_object($context)) {

            switch (gettype($context)) {

                case 'object':

                    if (property_exists($context, 'exception')) {

                        if ($context['exception'] instanceof Throwable) {

                            $context['exception'] = [
                                'code'    => $context->exception->getCode(),
                                'message' => $context->exception->getMessage(),
                                'line'    => $context->exception->getLine(),
                                'file'    => $context->exception->getFile()
                            ];
                        }
                    }

                    $context['docker_ip'] = config('machine.docker_ip', '');
                    $context['agent_ip'] = config('machine.agent_ip', '');
                    break;
                case 'array':

                    if (isset($context['exception'])) {

                        if ($context['exception'] instanceof Throwable) {

                            $context['exception'] = [
                                'code'    => $context['exception']->getCode(),
                                'message' => $context['exception']->getMessage(),
                                'line'    => $context['exception']->getLine(),
                                'file'    => $context['exception']->getFile()
                            ];
                        }
                    }

                    $context['docker_ip'] = config('machine.docker_ip', '');
                    $context['agent_ip'] = config('machine.agent_ip', '');
                    break;
            }

            return json_encode($context, 256);
        }

        return (string)$context;
    }

    public static function getData(array $params): array
    {
        $model = new ResponseLogDao();
        $where = [];
        $fillAble = $model->getFillable();
        $params = array_merge(array_intersect_key(array_flip($fillAble), $params), $params);
        foreach ($params as $k => $v) {
            if (in_array($k, $fillAble)) {
                switch ($k) {

                    case 'content':

                        $where[] = "content like '%{$params['content']}%'";
                        break;
                    case 'third_party':

                        $where[] = "third_party = '{$params['third_party']}'";
                        break;
                    case 'route_id':

                        $where[] = "route_id = '{$params['route_id']}'";
                        break;
                }
            }

            switch ($k) {

                case 'startTime':

                    $where[] = "created_at >= '{$params['startTime']}'";
                    break;
                case 'endTime':

                    $where[] = "created_at <= '{$params['endTime']}'";
                    break;
            }
        }

        $params['startTime'] = isset($params['startTime']) ? strtotime($params['startTime']) : time();
        $params['endTime'] = isset($params['endTime']) ? strtotime($params['endTime']) : time();
        $sql = Base::getLogTableSqlByTime($model->getRealTable(), $params['startTime'], $params['endTime'], [
            'offset' => ($params['page'] - 1) * $params['limit'],
            'limit'  => $params['limit'],
        ], "*", implode(" and ", $where));
        $connection = $model->getConnection();
        return [
            'count' => $connection->select($sql['count'])[0]->total ?? 0,
            'list'  => $connection->select($sql['page']),
        ];
    }

    public static function getCurDayCount()
    {
        $model = new ResponseLogDao();
        $connection = $model->getConnection();
        $queryResult = $connection->select(Base::getCurrentDayCountSqlByTable($model->getRealTable()));
        return $queryResult[0]->total ?? 0;
    }
}
