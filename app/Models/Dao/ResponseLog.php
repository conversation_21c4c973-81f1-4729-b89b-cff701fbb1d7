<?php namespace App\Models\Dao;

use Eloquent;
use Illuminate\Database\Eloquent\Builder;

/**
 * App\Models\Dao\ResponseLog
 *
 * @property int $id
 * @property string $content 请求内容
 * @property string $route_id 链路ID
 * @property string $created_at 创建时间
 * @mixin Eloquent
 * @method static Builder|ResponseLog newModelQuery()
 * @method static Builder|ResponseLog newQuery()
 * @method static Builder|ResponseLog query()
 * @method static Builder|ResponseLog whereContent($value)
 * @method static Builder|ResponseLog whereCreatedAt($value)
 * @method static Builder|ResponseLog whereId($value)
 * @method static Builder|ResponseLog whereRouteId($value)
 */
class ResponseLog extends Base
{
    public $timestamps = false;
    protected $table = 'response_log';
    protected $connection = 'mysql';
    protected $fillable = [
        'content',
        'route_id',
        'created_at'
    ];

    protected $hidden = [
    ];

    public function getRealTable(): string
    {
        return config('database.connections.mysql.prefix') . parent::getTable();
    }
}
