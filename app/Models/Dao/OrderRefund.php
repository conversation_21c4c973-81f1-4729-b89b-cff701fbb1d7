<?php namespace App\Models\Dao;

use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;


/**
 * App\Models\Dao\OrderRefund
 *
 * @property int $id
 * @property string $platform_order_id 对接平台订单编号
 * @property string|null $refund_result 退款结果
 * @property string $platform_name 对接平台名称
 * @property bool $refund_status 退款状态
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @method static Builder|OrderRefund newModelQuery()
 * @method static Builder|OrderRefund newQuery()
 * @method static Builder|OrderRefund query()
 * @method static Builder|OrderRefund whereCreatedAt($value)
 * @method static Builder|OrderRefund whereId($value)
 * @method static Builder|OrderRefund wherePlatformName($value)
 * @method static Builder|OrderRefund wherePlatformOrderId($value)
 * @method static Builder|OrderRefund whereRefundResult($value)
 * @method static Builder|OrderRefund whereRefundStatus($value)
 * @method static Builder|OrderRefund whereUpdatedAt($value)
 * @mixin Eloquent
 */
class OrderRefund extends Base
{
    protected $table = 'order_refund';
    protected $connection = 'mysql';


    protected $fillable = [
        'refund_result',
        'platform_order_id',
        'platform_name',
        'refund_status',
        'created_at',
    ];

    protected $hidden = [
    ];
}
