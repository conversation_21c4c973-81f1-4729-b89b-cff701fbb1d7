<?php namespace App\Models\Dao;

use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;


/**
 * App\Models\Dao\Base
 *
 * @method static Builder|Base newModelQuery()
 * @method static Builder|Base newQuery()
 * @method static Builder|Base query()
 * @mixin Eloquent
 */
class Base extends Model
{
    protected $fieldOrderForIndex;

    public function getRealTable(): string
    {
        return config('database.connections.mysql.prefix') . parent::getTable();
    }

    public function getFieldOrderForIndex()
    {
        return $this->fieldOrderForIndex;
    }
}
