<?php namespace App\Models\Dao;

use Eloquent;
use Illuminate\Database\Eloquent\Builder;

/**
 * App\Models\Dao\RequestLog
 *
 * @property int $id
 * @property string $third_party 第三方
 * @property string $address 地址
 * @property string $content 请求内容
 * @property string $log_level 日志等级
 * @property string $route_id 链路ID
 * @property string $result 请求结果
 * @property string $created_at 创建时间
 * @mixin Eloquent
 * @method static Builder|RequestLog newModelQuery()
 * @method static Builder|RequestLog newQuery()
 * @method static Builder|RequestLog query()
 * @method static Builder|RequestLog whereAddress($value)
 * @method static Builder|RequestLog whereContent($value)
 * @method static Builder|RequestLog whereCreatedAt($value)
 * @method static Builder|RequestLog whereId($value)
 * @method static Builder|RequestLog whereLogLevel($value)
 * @method static Builder|RequestLog whereResult($value)
 * @method static Builder|RequestLog whereRouteId($value)
 * @method static Builder|RequestLog whereThirdParty($value)
 */
class RequestLog extends Base
{
    public    $timestamps = false;
    protected $table      = 'request_log';
    protected $connection = 'mysql';
    protected $fillable   = [
        'third_party',
        'log_level',
        'created_at',
        'address',
        'route_id',
        'content',
        'result',
    ];

    protected $fieldOrderForIndex = [
        'third_party',
        'log_level',
        'startTime',
        'endTime',
        'address',
        'route_id',
        'content',
        'result',
    ];

    protected $hidden = [
    ];

    public function getRealTable(): string
    {
        return config('database.connections.mysql.prefix') . parent::getTable();
    }
}
