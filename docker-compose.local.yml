version: "2"
services:
  oil-adapter:
    restart: always
    image: kevin50ster/bluesword-php:v0.2.5
    volumes:
      - $PWD:/data/web
      - $PWD/conf/nginx/conf/vhosts:/etc/nginx/sites-available
    cap_add:
      - SYS_PTRACE
    environment:
      PHPENV: 7.2
      APPLICATION__NAME: ecard
      APPLICATION__DEBUG: "true"
      DB__CONNECTION: mysql
      DB__HOST: ************
      CRONTAB__ENABLE: 'off'
      SUPERVISOR__ENABLE: 'off'
      COMPOSER_UPDATE: 'off'
      WEBROOT: '/data/web/public'
      PHP_ERRORS_STDERR: 'true'
      SKIP_COMPOSER: '1'
    mem_limit: 2147483648
    ports:
      - "8089:80"
      - "4439:443"
