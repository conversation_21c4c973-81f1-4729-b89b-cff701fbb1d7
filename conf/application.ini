[product]
;不允许修改
application.directory = APPLICATION_PATH
;应用名
application.name = "alpha"
;应用版本，会注册到微服务的配置中心
application.version = v1
;开启时，Yaf会在发生错误的地方抛出异常
application.dispatcher.throwException = 1
;如果有未捕获的异常，Yaf将会把它定向到Error controller, Error Action
application.dispatcher.catchException = 1
;注册模块，默认Index是注册的
application.modules = "Index"
;默认的模块名（不带路径访问）
application.dispatcher.defaultModuel = Index
;默认的控制器名（不带路径访问）
application.dispatcher.defaultController = Index
;默认的动作名（不带路径访问）
application.dispatcher.defaultAction = index
application.http.domain = "dsp-micro.dev.chinawayltd.com"
