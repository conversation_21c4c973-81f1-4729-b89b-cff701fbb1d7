<?php
/**
 * Created by PhpStorm.
 * User: kevin
 * Date: 17-3-23
 * Time: 下午6:00
 */

namespace GosSDK;

use GosSDK\Config\Config;
use GosSDK\Lib\Encrypter;
class Crypt
{
    private $config = NULL;

    protected $rsa = NULL;

    public function __construct()
    {
        if (!$this->config) {
            $this->config = Config::getConfig();
        }
        ConfigCheck::check($this->config);
    }

    public function encode(array $params = [])
    {
        $rsa = new Encrypter($this->config['public_key']);
//        $message = \GuzzleHttp\json_encode($params);

        return $rsa->encrypt($params);
    }

    public function decode($content)
    {
        $rsa = new Encrypter($this->config['public_key']);

        return $rsa->decrypt($content);
    }

    public function getAppId()
    {
        return $this->config['app_id'];
    }

    public function getAppUrl()
    {
        return $this->config['url'];
    }

    public function getCallbakUrl()
    {
        return $this->config['callbak_url'];
    }

    public function getPageStyle()
    {
        return $this->config['page_style'];
    }

}