<?php
/**
 * GOS可接收字段同步
 * User: kevin
 * Date: 17-5-25
 * Time: 下午2:31
 */

namespace GosSDK;


class SyncFieldsConfig
{
    protected $path;

    const FIELDS_CONFIG_SYNC = 'sdkHelper/index';

    public function __construct()
    {
        $this->path = __DIR__.'/Defines/Fields/';
    }

    public function sync()
    {
        $data = (new Gos())
            ->setMethod(self::FIELDS_CONFIG_SYNC)//设置异步方式或同步接口方法
            ->setRequestType('GET')//设置请求方式，POST或GET，默认POST，如果为POST可不设定
            ->setParams([])//设置接口所需传输参数，数组类型
            ->sync();//执行请求

        if($data){
            $data = \GuzzleHttp\json_encode($data);
            $data = \GuzzleHttp\json_decode($data,  true);
            foreach($data as $k=>$v){
                $fileName = $k.'.php';
                $content = "<?php\r\n\r\n"."return ".var_export($v, TRUE).";\r\n";
                \file_put_contents($this->path.$fileName, $content);
            }
        }

        echo "Finish";
    }
}