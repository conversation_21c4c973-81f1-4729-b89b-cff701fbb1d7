<?php
/**
 * Created by PhpStorm.
 * User: kevin
 * Date: 17-5-26
 * Time: 下午4:06
 */

namespace GosSDK\Lib;

use GosSDK\Config\Config;
use GosSDK\Log;

class Middleware
{
    private $config;

    public function __construct()
    {
        if (!$this->config) {
            $this->config = Config::getConfig();
        }
    }

    /**
     * @title   执行前
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  GosSDK\Lib
     * @since
     * @params   type filedName required?
     * @param array $params
     */
    public function beforeAction(array $params)
    {
        if(isset($this->config['middleware']) && isset($this->config['middleware']['BeforeMiddleware']) &&
            $this->config['middleware']['BeforeMiddleware']){
            $className = $this->config['middleware']['BeforeMiddleware'];
            $this->handle($className, $params);
        }
    }

    /**
     * @title   执行后
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  GosSDK\Lib
     * @since
     * @params   type filedName required?
     * @param $params
     * @return null
     */
    public function afterAction($params)
    {
        if(isset($this->config['middleware']) && isset($this->config['middleware']['AfterMiddleware']) &&
            $this->config['middleware']['AfterMiddleware']){
            $className = $this->config['middleware']['AfterMiddleware'];
            $this->handle($className, $params);
        }
    }

    private function handle($className, $params)
    {
        if(\class_exists($className)){
            $middlewareObject = new $className;
            if(\method_exists($middlewareObject, 'handle')){
                try{
                    $middlewareObject->handle($params);
                }catch (\Exception $e){
                    Log::dataLog($className.' Error'.strval($e));
                }
            }
        }
    }
}