<?php
/**
 * Created by PhpStorm.
 * User: tim
 * Date: 2017/5/25
 * Time: 17:09
 */

namespace GosSDK\Defines\Methods;


class GasPrice {


    /**
     * 单条新增
     */
    const CREATE = 'v1/gasPrice/store';

    /**
     * 删除(支持单条和批量)
     */
    const DELETE = 'v1/gasPrice/destroyByThirdIdAndSysId';

    /**
     * 单条编辑
     */
    const UPDATE = 'v1/gasPrice/updateByThirdIdAndSysId';

    /**
     * 获取卡片列表
     */
    const GET_LIST = 'v1/gasPrice/get_list';

    /**
     * 批量新增卡片
     */
    const CREATE_BATCH = 'v1/gasPrice/batchStore';


    /**
     * 批量修改卡片
     */
    const UPDATE_BATCH = 'v1/gasPrice/batchUpdateByThirdIdAndSysId';

    /**
     * 获取最新价格
     */
    const GET_LATEST_FOR_G7S = 'v1/gasPrice/getLatest';

    /**
     * 获取一天内价格
     */
    const GET_ONE_DAY_FOR_G7S = 'v1/gasPrice/getOneDayPrice';


} 