<?php
/**
 * Created by PhpStorm.
 * User: tim
 * Date: 2017/5/25
 * Time: 17:09
 */

namespace GosSDK\Defines\Methods;


class BankRecords {
    /**
     * 删除(支持单条和批量)
     */
    const DELETE = 'v1/bankRecords/destroyByThirdIdAndSysId';

    /**
     * 获取列表
     */
    const GET_LIST = 'v1/bankRecords/get_list';

    /**
     * 获取id获取详情
     */
    const GET_DETAIL = 'v1/bankRecords/show';

    /**
     * 批量新增
     */
    const CREATE_BATCH = 'v1/bankRecords/batchStore';


    /**
     * 批量修改
     */
    const UPDATE_BATCH = 'v1/bankRecords/batchUpdateByThirdIdAndSysId';

    /**
     * 按流水号批量修改
     */
    const UPDATE_BATCH_BY_PAY_NO = 'v1/bankRecords/batchUpdateByPayNo';

    /**
     * 按流水号查询
     */
    const GET_BY_PAY_NO = 'v1/bankRecords/getByPayNo';

    /**
     * 刷新数据
     */
    const REFRESH_NEW = 'v1/bankRecords/syncFromCmBank';

    /**
     * 获取收款单列表
     */
    const STATUS_NUM = 'v1/bankRecords/statusNum';

    /**
     * 获取收款信息
     */
    const GET_PAY_INFO = 'v1/bankRecords/getPayInfo';


} 