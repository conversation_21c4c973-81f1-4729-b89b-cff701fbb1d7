<?php

return [
    'info'      =>
        [
            'name'    => 'CardVice',
            'comment' => '副卡信息',
        ],
    'type'      => 'mongo',
    'fields'    =>
        [
            'id'                  => '主键ID',
            'sys_id'              => '系统ID',
            'third_id'            => '业务系统pk',
            'vice_no'             => '副卡号',
            'password'            => '卡密码',
            'oil_com'             => '油卡商（油卡类型）（1:中石化，2:中石油,3:中交石油，4:上海盛海，6：充值卡，7：记账卡，8：加油员，50：中交石化）',
            '_oil_com'            => '油卡商（油卡类型）（1:中石化，2:中石油,3:中交石油，4:上海盛海，6：充值卡，7：记账卡，8：加油员，50：中交石化）',
            'card_from'           => '卡来源（10汇通卡 20中交卡 30撬装卡 40托管卡 50盛海卡）',
            '_card_from'          => '卡来源',
            'card_main_id'        => '主卡id',
            'fanli_region_code'   => '积分可用地区编码',
            'fanli_region_name'   => '积分可用地区',
            'vice_tmp_id'         => '开卡申请子表id',
            'truck_no'            => '车牌号',
            'driver_tel'          => '司机电话',
            'driver_name'         => '登记司机姓名',
            'driver_line'         => '登记线路',
            'card_owner'          => '持卡人',
            'org_id'              => '所属机构id',
            'org_id_fanli'        => '返利机构',
            'gas_money_id'        => '撬装账户机构ID',
            'reserve_remain'      => '备付金余额',
            'comp_remain'         => '备付金补偿值',
            'card_remain'         => '卡余额',
            'oil_balance'         => '油量余额（升）',
            'total_charge'        => '累计充值（分配）',
            'total_trade'         => '累计消费',
            'point_reserve_total' => '积分备付金',
            'point_remain'        => '积分余额',
            'remain_get_time'     => '余额抓取时间',
            'remain_syn_time'     => '余额同步时间',
            'active_time'         => '开卡日期（默认当前时间）',
            'gsporg_id'           => '业务组织id',
            'user_id'             => '业务负责人Id',
            'bind_status'         => '车卡绑定状态（-1解绑，0未绑，1已绑）',
            '_bind_status'        => '车卡绑定状态',
            'is_return_abnormal'  => '是否抓取异常',
            'return_result'       => '抓取返回结果',
            'day_top'             => '当天加油上限',
            'oil_top'             => '单次加油上限',
            'unit'                => '油卡余额单位 1 元，  2 升',
            'pin'                 => '校验码',
            'refresh_status'      => '余额实时刷新状态（-10刷新失败，10刷新成功,5刷新中）',
            'is_tip_sms'          => '是否交易短息提醒  2提醒 1不提醒',
            'assign_time'         => '最新充值时间',
            'status'              => '卡状态|使用|锁定|其他',
            'remark'              => '外部备注',
            'remark_work'         => '客户备注',
            'creator_id'          => '创建人ID',
            'creator_name'        => '创建人名称',
            'updater_id'          => '最后修改者姓名',
            'updater_name'        => '最后修改者姓名',
            'trade_time'          => '最新消费时间',
            'createtime'          => '业务系统创建时间',
            'updatetime'          => '业务系统更新时间',
            'deletetime'          => '业务系统删除时间',
            'created_at'          => '创建时间',
            'updated_at'          => '更新时间',
            'deleted_at'          => '删除时间',
        ],
    'casts'     =>
        [
            'id'                  => 'string',
            'third_id'            => 'string',
            'sys_id'              => 'string',
            'vice_no'             => 'string',
            'vice_password'       => 'string',
            'oil_com'             => 'string',
            '_oil_com'            => 'string',
            'card_from'           => 'string',
            '_card_from'          => 'string',
            'card_main_id'        => 'string',
            'fanli_region_code'   => 'string',
            'fanli_region_name'   => 'string',
            'vice_tmp_id'         => 'string',
            'truck_no'            => 'string',
            'driver_tel'          => 'string',
            'driver_name'         => 'string',
            'driver_line'         => 'string',
            'card_owner'          => 'string',
            'org_id'              => 'string',
            'org_id_fanli'        => 'string',
            'gas_money_id'        => 'string',
            'reserve_remain'      => 'double',
            'comp_remain'         => 'double',
            'card_remain'         => 'double',
            'oil_balance'         => 'double',
            'total_charge'        => 'double',
            'total_trade'         => 'double',
            'point_reserve_total' => 'double',
            'point_remain'        => 'double',
            'remain_get_time'     => 'string',
            'remain_syn_time'     => 'string',
            'active_time'         => 'string',
            'gsporg_id'           => 'string',
            'user_id'             => 'string',
            'bind_status'         => 'int',
            '_bind_status'        => 'string',
            'is_return_abnormal'  => 'int',
            'return_result'       => 'string',
            'day_top'             => 'double',
            'oil_top'             => 'double',
            'unit'                => 'string',
            'pin'                 => 'string',
            'refresh_status'      => 'int',
            'status'              => 'string',
            'remark'              => 'string',
            'remark_work'         => 'string',
            'is_tip_sms'          => 'int',
            'creator_id'          => 'string',
            'creator_name'        => 'string',
            'last_operator'       => 'string',
            'assign_time'         => 'string',
            'trade_time'          => 'string',
            'createtime'          => 'string',
            'updatetime'          => 'string',
            'created_at'          => 'string',
            'updated_at'          => 'string',
        ],
    'validator' =>
        [
            'CREATE' =>
                [
                    'id'           => 'required',
                    'vice_no'      => 'required',
                    'card_from'    => 'required',
                    'oil_com'      => 'required',
                    'card_main_id' => 'required',
                    'org_id'       => 'required',
                    'status'       => 'required',
                ],
            'UPDATE' =>
                [
                    'id'           => 'required',
                    'vice_no'      => 'required',
                    'card_from'    => 'required',
                    'oil_com'      => 'required',
                    'card_main_id' => 'required',
                    'org_id'       => 'required',
                    'status'       => 'required',
                ],
        ],
];
