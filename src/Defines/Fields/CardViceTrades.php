<?php

return [
    'info'      =>
        [
            'name'    => 'CardViceTrades',
            'comment' => '加油记录',
        ],
    'type'      => 'mongo',
    'fields'    =>
        [
            'id'                 => '主键ID',
            'sys_id'             => '系统ID',
            'third_id'           => '业务系统pk',
            'api_id'             => '接口返回的唯一id',
            'vice_no'            => '副卡号',
            'unit'               => '1按金额，2升',
            '_unit'              => '1按金额，2升',
            'trade_time'         => '交易(加油)时间',
            'trade_type'         => '交易类型',
            'oil_name'           => '（油品）加油型号',
            'trade_money'        => '金额',
            'use_fanli_money'    => '使用返利金额',
            'trade_price'        => '单价',
            'trade_num'          => '加油数量',
            'trade_jifen'        => '奖励积分',
            'balance'            => '余额',
            'oil_balance'        => '剩余油量（升）',
            'trade_place'        => '交易地点',
            'regions_id'         => '积分可用地区ID',
            'regions_code'       => '积分可用地区名称（例如：北京、河北、湖南）',
            'regions_name'       => '积分可用地区名称（例如：北京、河北、湖南）',
            'fetch_time'         => '抓取时间',
            'is_fanli'           => '是否算过返利（0未算，1已算）',
            'main_no'            => '主卡号',
            'card_owner'         => '持卡人',
            'oil_com'            => '油卡商（油卡类型）（1、中石化，2、中石油，3、中车油，4、上海盛海，5、G7预存卡，6、G7资金卡）',
            '_oil_com'           => '油卡商（油卡类型）（1、中石化，2、中石油，3、中车油，4、上海盛海，5、G7预存卡，6、G7资金卡）',
            'card_from'          => '10汇通卡 20中交卡 30撬装卡 40托管卡 50盛海卡',
            '_card_from'         => '10汇通卡 20中交卡 30撬装卡 40托管卡 50盛海卡',
            'active_region'      => '开卡地区（关联省份表）',
            'active_region_code' => '开卡地区（关联省份表）',
            'active_region_name' => '开卡地区（关联省份表）',
            'org_id'             => '机构id',
            'org_name'           => '机构名称',
            'main_operator_id'   => '主卡运营商ID',
            'org_operator_id'    => '机构运营商ID',
            'consume_type'       => '消费类型(0.未知;1.现金消费; 2.积分消费;3.透支消费)',
            'truck_no'           => '车牌号',
            'fanli_no'           => '返利单号',
            'fanli_money'        => '返利资金',
            'fanli_jifen'        => '返利积分',
            'policy_id'          => '对应的返利政策ID',
            'fanli_way'          => '返利方式（1、现金，2、积分）',
            'imgurl'             => '手工签名图片地址',
            'createtime'         => '创建时间',
            'updatetime'         => '修改时间',
            'qz_drivername'      => '司机姓名',
            'qz_drivertel'       => '司机电话',
            'receipt_remain'     => '可开票金额',
            'consumer_name'      => '消费单位名称（针对撬装）',
            'data_updated'       => '业务数据变更日期',
            'created_at'         => '创建时间',
            'updated_at'         => '更新时间',
            'deleted_at'         => '删除时间',
        ],
    'casts'     =>
        [
            'id'                 => 'string',
            'third_id'           => 'string',
            'sys_id'             => 'string',
            'api_id'             => 'string',
            'vice_no'            => 'string',
            'unit'               => 'string',
            '_unit'              => 'string',
            'trade_time'         => 'string',
            'trade_type'         => 'string',
            'oil_name'           => 'string',
            'trade_money'        => 'double',
            'use_fanli_money'    => 'string',
            'trade_price'        => 'string',
            'trade_num'          => 'string',
            'trade_jifen'        => 'string',
            'balance'            => 'double',
            'oil_balance'        => 'string',
            'trade_place'        => 'string',
            'regions_id'         => 'string',
            'regions_name'       => 'string',
            'fetch_time'         => 'string',
            'is_fanli'           => 'string',
            'main_no'            => 'string',
            'card_owner'         => 'string',
            'oil_com'            => 'string',
            '_oil_com'           => 'string',
            'card_from'          => 'string',
            '_card_from'         => 'string',
            'active_region'      => 'string',
            'active_region_code' => 'string',
            'active_region_name' => 'string',
            'org_id'             => 'string',
            'org_name'           => 'string',
            'main_operator_id'   => 'string',
            'org_operator_id'    => 'string',
            'consume_type'       => 'string',
            'truck_no'           => 'string',
            'fanli_no'           => 'string',
            'fanli_money'        => 'string',
            'fanli_jifen'        => 'string',
            'policy_id'          => 'string',
            'fanli_way'          => 'string',
            'imgurl'             => 'string',
            'createtime'         => 'string',
            'updatetime'         => 'string',
            'qz_drivername'      => 'string',
            'qz_drivertel'       => 'string',
            'receipt_remain'     => 'string',
            'consumer_name'      => 'string',
            'data_updated'       => 'string',
            'created_at'         => 'string',
            'updated_at'         => 'string',
            'deleted_at'         => 'string',
        ],
    'validator' =>
        [
            'CREATE'  =>
                [
                    'id'                       => 'required',
                    'org_id'                   => 'required',
                    'vice_no'                  => 'required',
                    'trade_time'               => 'required',
                    'trade_type'               => 'required',
                    'oil_name'                 => 'required',
                    'trade_money'              => 'required',
                    'trade_price'              => 'required',
                    'trade_num'                => 'required',
                    'balance'                  => 'required',
                    'trade_place'              => 'required',
                    'trade_place_provice_name' => 'required',
                    'trade_place_city_name'    => 'required',
                ],
            'UPDATE'  =>
                [
                    'id'                       => 'required',
                    'org_id'                   => 'required',
                    'vice_no'                  => 'required',
                    'trade_time'               => 'required',
                    'trade_type'               => 'required',
                    'oil_name'                 => 'required',
                    'trade_money'              => 'required',
                    'trade_price'              => 'required',
                    'trade_num'                => 'required',
                    'balance'                  => 'required',
                    'trade_place'              => 'required',
                    'trade_place_provice_name' => 'required',
                    'trade_place_city_name'    => 'required',
                ],
            'DESTROY' =>
                [
                ],
        ],
];
