<?php
/**
 * Created by PhpStorm.
 * User: kevin
 * Date: 17-3-23
 * Time: 下午5:49
 */

namespace GosSDK;


use GosSDK\Lib\Helper;

class Gos
{
    public $method;

    public $action;

    public $dataType;

    public $apiParams;

    public $attach;

    public $channel;

    public $requestType = 'POST';

    /**
     *  下发队列
     */
    const QUEUE_PUSH = 'v1/queue/push';

    public function __construct()
    {
        global $sdkTimeStart;
        $sdkTimeStart = Log::getMicroTime();
    }

    /**
     * 设置接口
     *
     * @param null $method
     * @return $this
     */
    public function setMethod($method = NULL)
    {
        $this->method = $method;

        return $this;
    }

    /**
     * @param null $channel
     * @return $this
     */
    public function setChannel($channel = NULL)
    {
        $this->channel = $channel;

        return $this;
    }

    /**
     * 设置数据操作方式
     *
     * @param null $action
     * @return $this
     */
    private  function setAction($action = NULL)
    {
        $this->action = $action;

        return $this;
    }

    /**
     * 设置数据类型
     *
     * @param null $dataType
     * @return $this
     */
    private function setDataType($dataType = NULL)
    {
        $this->dataType = $dataType;

        return $this;
    }

    /**
     * 设置参数
     *
     * @param array $params
     * @return $this
     */
    public function setParams(array $params = [])
    {
        $this->apiParams = $this->convertPageArgs($params);

        $this->apiParams = Helper::filterUnSafeCharacter($this->apiParams);

        return $this;
    }

    public function setRequestType($requestType = 'POST')
    {
        $this->requestType = $requestType;

        return $this;
    }

    /**
     * @title   设置附加数据
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  GosSDK
     * @since
     * @params   type filedName required?
     * @param string $attach
     * @return $this
     * @returns
     * $this
     * @returns
     */
    public function setAttach($attach=\NULL)
    {
        $this->attach = $attach;

        return $this;
    }


    /**
     * 同步请求
     *
     * @return mixed|null
     */
    public function sync()
    {
        $data = Http::post($this);

        return $this->convertPageData($data);
    }

    /**
     * @title   异步请求
     * @desc
     * @version
     * <AUTHOR>
     * @package GosSDK
     * @since
     * @params  type filedName required?
     * @return mixed
     * @returns
     * []
     * @returns
     */
    public function async()
    {
        if(!$this->method){
            throw new \RuntimeException('method is null', 2);
        }
        $_method = explode('/', $this->method);
        if(count($_method) < 2){
            throw new \RuntimeException('method invalid', 2);
        }

        if(count($_method[0]) == 2){
            $this->setDataType($_method[0]);
            $this->setAction($_method[1]);
        }else{
            $dataTypeKey = count($_method) - 2;
            $this->setDataType($_method[$dataTypeKey]);
            $this->setAction(end($_method));
        }

        $this->setMethod(self::QUEUE_PUSH);

        $data = Http::post($this);

        return $data;
    }

    /**
     * @title   翻页参数转换
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  GosSDK
     * @since
     * @params   type filedName required?
     * @param $data
     * @return mixed
     * @returns
     * mixed
     * @returns
     */
    public function convertPageArgs($data)
    {
        $encrypt           = new Crypt();
        $pageStyle = $encrypt->getPageStyle();
        if($pageStyle){
            foreach($pageStyle as $key=>$val){
                if(!isset($data[$key]) && isset($data[$val]) && $key !== $val)
                {
                    if($key == 'current_page'){
                        $data['page'] = $data[$val];
                    }else{
                        $data[$key] = $data[$val];
                    }

                    unset($data[$val]);
                }
            }
        }

        return $data;
    }

    /**
     * @title   翻页数据转换
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  GosSDK
     * @since
     * @params   type filedName required?
     * @param $data
     * @return mixed
     * @returns
     * mixed
     * @returns
     */
    public function convertPageData($data)
    {
        $encrypt           = new Crypt();
        $pageStyle = $encrypt->getPageStyle();
        if($pageStyle){
            foreach($pageStyle as $key=>$val){
                if(!isset($data->$val) && isset($data->$key) && $val != $key)
                {
                    $data->$val = $data->$key;
                    unset($data->$key);
                }
            }
        }

        return $data;
    }

}